<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <artifactId>fs-crm-fmcg-wq</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.5.5-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <packaging>war</packaging>
    <name>fs-crm-fmcg-wq-web</name>
    <artifactId>fs-crm-fmcg-wq-web</artifactId>
    <properties>
        <skip_maven_deploy>true</skip_maven_deploy>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${jdk.version}</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-fmcg-wq-all</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>

    </build>
</project>