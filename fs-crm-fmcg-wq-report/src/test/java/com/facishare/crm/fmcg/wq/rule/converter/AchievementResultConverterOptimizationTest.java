package com.facishare.crm.fmcg.wq.rule.converter;

import com.facishare.crm.fmcg.wq.constants.DisplayProjectAchievementFields;
import com.facishare.crm.fmcg.wq.constants.TPMActivityProofDetailFields;
import com.facishare.crm.fmcg.wq.dao.DataReportResultDao;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AchievementResultConverter 优化验证测试
 * 主要测试优化后的 convertToDisplayAchievementByActivityItem 方法
 */
@ExtendWith(MockitoExtension.class)
class AchievementResultConverterOptimizationTest {

  @Mock
  private DataReportResultDao dataReportResultDao;

  @InjectMocks
  private AchievementResultConverter converter;

  private User testUser;
  private IObjectData testActivityProofData;
  private Map<String, Map<String, AchievementResultConverter.GroupAchievementDetail>> testFormIdAndGroupIdDetailsMap;
  private Map<String, List<IObjectData>> testProductAndMaterialGroup;

  @BeforeEach
  void setUp() {
    testUser = mock(User.class);
    when(testUser.getTenantId()).thenReturn("test-tenant");

    testActivityProofData = mock(IObjectData.class);
    testFormIdAndGroupIdDetailsMap = new HashMap<>();
    testProductAndMaterialGroup = new HashMap<>();

    // 设置基础数据
    when(testActivityProofData.get(TPMActivityProofDetailFields.DISPLAY_FORM_ID))
        .thenReturn("test-form-id");
    when(testActivityProofData.get(TPMActivityProofDetailFields.AMOUNT, Double.class))
        .thenReturn(100.0);
    when(testActivityProofData.get(TPMActivityProofDetailFields.PROOF_DETAIL_AMOUNT_STANDARD, Double.class))
        .thenReturn(80.0);
    when(testActivityProofData.get(TPMActivityProofDetailFields.ACTIVITY_DETAIL_ID, String.class))
        .thenReturn("test-activity-detail-id");
    when(testActivityProofData.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, String.class))
        .thenReturn("test-activity-item-id");
  }

  @Test
  void testConvertToDisplayAchievementByActivityItem_WithNullUser_ShouldReturnEmptyList() {
    // Given
    User nullUser = null;

    // When
    List<IObjectData> result = converter.convertToDisplayAchievementByActivityItem(
        nullUser, testActivityProofData, testFormIdAndGroupIdDetailsMap, testProductAndMaterialGroup);

    // Then
    assertTrue(result.isEmpty());
  }

  @Test
  void testConvertToDisplayAchievementByActivityItem_WithNullActivityProofData_ShouldReturnEmptyList() {
    // Given
    IObjectData nullData = null;

    // When
    List<IObjectData> result = converter.convertToDisplayAchievementByActivityItem(
        testUser, nullData, testFormIdAndGroupIdDetailsMap, testProductAndMaterialGroup);

    // Then
    assertTrue(result.isEmpty());
  }

  @Test
  void testConvertToDisplayAchievementByActivityItem_WithNullFormIdAndGroupIdDetailsMap_ShouldReturnEmptyList() {
    // Given
    Map<String, Map<String, AchievementResultConverter.GroupAchievementDetail>> nullMap = null;

    // When
    List<IObjectData> result = converter.convertToDisplayAchievementByActivityItem(
        testUser, testActivityProofData, nullMap, testProductAndMaterialGroup);

    // Then
    assertTrue(result.isEmpty());
  }

  @Test
  void testConvertToDisplayAchievementByActivityItem_WithValidData_ShouldReturnResults() {
    // Given
    when(testActivityProofData.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class))
        .thenReturn(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_pass);

    // Mock the getDisplayProjectAchievement method result
    IObjectData mockResult = mock(IObjectData.class);
    when(mockResult.getId()).thenReturn("test-result-id");

    // Mock dataReportResultDao.createBaseObjectData
    when(dataReportResultDao.createBaseObjectData(anyString(), any(), anyString()))
        .thenReturn(mockResult);

    // When
    List<IObjectData> result = converter.convertToDisplayAchievementByActivityItem(
        testUser, testActivityProofData, testFormIdAndGroupIdDetailsMap, testProductAndMaterialGroup);

    // Then
    assertNotNull(result);
    assertFalse(result.isEmpty());
  }

  @Test
  void testConvertToDisplayAchievementByActivityItem_WithNullSystemJudgmentStatus_ShouldInitializeStatus() {
    // Given
    when(testActivityProofData.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class))
        .thenReturn(null);

    // Mock the getDisplayProjectAchievement method result
    IObjectData mockResult = mock(IObjectData.class);
    when(mockResult.getId()).thenReturn("test-result-id");

    // Mock dataReportResultDao.createBaseObjectData
    when(dataReportResultDao.createBaseObjectData(anyString(), any(), anyString()))
        .thenReturn(mockResult);

    // When
    List<IObjectData> result = converter.convertToDisplayAchievementByActivityItem(
        testUser, testActivityProofData, testFormIdAndGroupIdDetailsMap, testProductAndMaterialGroup);

    // Then
    verify(testActivityProofData).set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS,
        TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_not_display);
    assertNotNull(result);
  }

  @Test
  void testConvertToDisplayAchievementByActivityItem_WithPendingApprovalStatus_ShouldHandleCorrectly() {
    // Given
    when(testActivityProofData.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class))
        .thenReturn(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_pending_approval);
    when(testActivityProofData.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class,
        TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_not_display))
        .thenReturn(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_pending_approval);

    // Mock the getDisplayProjectAchievement method result
    IObjectData mockResult = mock(IObjectData.class);
    when(mockResult.getId()).thenReturn("test-result-id");

    // Mock dataReportResultDao.createBaseObjectData
    when(dataReportResultDao.createBaseObjectData(anyString(), any(), anyString()))
        .thenReturn(mockResult);

    // When
    List<IObjectData> result = converter.convertToDisplayAchievementByActivityItem(
        testUser, testActivityProofData, testFormIdAndGroupIdDetailsMap, testProductAndMaterialGroup);

    // Then
    assertNotNull(result);
    assertFalse(result.isEmpty());
  }

  @Test
  void testConvertToDisplayAchievementByActivityItem_WithFailStatus_ShouldHandleCorrectly() {
    // Given
    when(testActivityProofData.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class))
        .thenReturn(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_fail);
    when(testActivityProofData.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class,
        TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_not_display))
        .thenReturn(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_fail);

    // Mock the getDisplayProjectAchievement method result
    IObjectData mockResult = mock(IObjectData.class);
    when(mockResult.getId()).thenReturn("test-result-id");

    // Mock dataReportResultDao.createBaseObjectData
    when(dataReportResultDao.createBaseObjectData(anyString(), any(), anyString()))
        .thenReturn(mockResult);

    // When
    List<IObjectData> result = converter.convertToDisplayAchievementByActivityItem(
        testUser, testActivityProofData, testFormIdAndGroupIdDetailsMap, testProductAndMaterialGroup);

    // Then
    assertNotNull(result);
    assertFalse(result.isEmpty());
  }

  @Test
  void testConvertToDisplayAchievementByActivityItem_WithPartialPassStatus_ShouldHandleCorrectly() {
    // Given
    when(testActivityProofData.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class))
        .thenReturn(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_partial_pass);
    when(testActivityProofData.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class,
        TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_not_display))
        .thenReturn(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_partial_pass);

    // Mock the getDisplayProjectAchievement method result
    IObjectData mockResult = mock(IObjectData.class);
    when(mockResult.getId()).thenReturn("test-result-id");

    // Mock dataReportResultDao.createBaseObjectData
    when(dataReportResultDao.createBaseObjectData(anyString(), any(), anyString()))
        .thenReturn(mockResult);

    // When
    List<IObjectData> result = converter.convertToDisplayAchievementByActivityItem(
        testUser, testActivityProofData, testFormIdAndGroupIdDetailsMap, testProductAndMaterialGroup);

    // Then
    assertNotNull(result);
    assertFalse(result.isEmpty());
  }

  @Test
  void testConvertToDisplayAchievementByActivityItem_WithNotDisplayStatus_ShouldHandleCorrectly() {
    // Given
    when(testActivityProofData.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class))
        .thenReturn(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_not_display);
    when(testActivityProofData.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class,
        TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_not_display))
        .thenReturn(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_not_display);

    // Mock the getDisplayProjectAchievement method result
    IObjectData mockResult = mock(IObjectData.class);
    when(mockResult.getId()).thenReturn("test-result-id");

    // Mock dataReportResultDao.createBaseObjectData
    when(dataReportResultDao.createBaseObjectData(anyString(), any(), anyString()))
        .thenReturn(mockResult);

    // When
    List<IObjectData> result = converter.convertToDisplayAchievementByActivityItem(
        testUser, testActivityProofData, testFormIdAndGroupIdDetailsMap, testProductAndMaterialGroup);

    // Then
    assertNotNull(result);
    assertFalse(result.isEmpty());
  }

  @Test
  void testGetRuleGroupIdAndApiNameFormTPM_WithValidData_ShouldReturnPair() {
    // Given
    when(testActivityProofData.get(TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID, String.class))
        .thenReturn("test-agreement-id");

    // When
    org.apache.commons.lang3.tuple.Pair<String, String> result = converter.getRuleGroupIdAndApiNameFormTPM(testActivityProofData);

    // Then
    assertNotNull(result);
    assertEquals("test-agreement-id", result.getKey());
  }

  @Test
  void testGetRuleGroupIdAndApiNameFormTPM_WithNullAgreementId_ShouldUseActivityItemId() {
    // Given
    when(testActivityProofData.get(TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID, String.class))
        .thenReturn(null);
    when(testActivityProofData.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, String.class))
        .thenReturn("test-item-id");

    // When
    org.apache.commons.lang3.tuple.Pair<String, String> result = converter.getRuleGroupIdAndApiNameFormTPM(testActivityProofData);

    // Then
    assertNotNull(result);
    assertEquals("test-item-id", result.getKey());
  }

  @Test
  void testGetRuleGroupIdAndApiNameFormTPM_WithNullIds_ShouldReturnNull() {
    // Given
    when(testActivityProofData.get(TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID, String.class))
        .thenReturn(null);
    when(testActivityProofData.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, String.class))
        .thenReturn(null);

    // When
    org.apache.commons.lang3.tuple.Pair<String, String> result = converter.getRuleGroupIdAndApiNameFormTPM(testActivityProofData);

    // Then
    assertNull(result);
  }
}
