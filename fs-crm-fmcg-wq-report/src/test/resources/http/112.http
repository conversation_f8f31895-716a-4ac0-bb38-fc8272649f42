# curl 'https://crm.ceshi112.com/FHH/EM1HWaiQinV2/ruleWebServiceV2/checkAdminInfo?_fs_token=PZCqCsDZPZOjP68uCoqqOMHcBM9YPZ0jOJ1cPJHbEJGqEJbc&traceId=E-E.83150.1000-83599111'
#  -H 'accept: application/json, text/javascript, */*; q=0.01'
#  -H 'accept-language: zh-CN,zh-TW;0.9,en;0.8'
#  -H 'content-type: application/json; charset=UTF-8'
#  -H 'origin: https://crm.ceshi112.com'
#  -H 'priority: u=1, i'
#  -H 'referer: https://crm.ceshi112.com/XV/UI/Home'
#  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "Windows"'
#  -H 'sec-fetch-dest: empty'
#  -H 'sec-fetch-mode: cors'
#  -H 'sec-fetch-site: same-origin'
#  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
#  -H 'x-requested-with: XMLHttpRequest'
#  -H 'x-trace-id: 83150_1000_1746692056795:55'
#  --data-raw '{}'
POST https://crm.ceshi112.com/FHH/EM1HWaiQinV2/outerService/getQRCodeUrl
accept: application/json, text/javascript, */*; q=0.01
accept-language: zh-CN,zh-TW;0.9,en;0.8
origin: https://crm.ceshi112.com
priority: u=1, i
referer: https://crm.ceshi112.com/XV/UI/Home
sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
sec-fetch-dest: empty
sec-fetch-mode: cors
sec-fetch-site: same-origin
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
x-requested-with: XMLHttpRequest
x-trace-id: 83150_1000_1746692056795:55
Content-Type: application/json; charset=UTF-8
cookie: FSAuthX=0G60kERc1GC0003pw1AFHknwkYERMAjLy4nRJqYgVyvOG3LnpmeI6pcXYkSWiUAESKGT4u2BXLLnTeqRzes1Q2uwpU3dey4QbENHni8Pp9vDkNvqSLKKR9PdHPK8pStTWyhmLQCa8hnAKZIFUH9OzXaL0yTkKb09WwY2VpCtbRbAfMXCHamXC374eT1XP1IahgUsmFvcKeo4xIM8YeUmzOMdUSDCPVsvNg0zy7I7FdZwmbFllsizsvkrdA70; FSAuthXC=0G60kERc1GC0003pw1AFHknwkYERMAjLy4nRJqYgVyvOG3LnpmeI6pcXYkSWiUAESKGT4u2BXLLnTeqRzes1Q2uwpU3dey4QbENHni8Pp9vDkNvqSLKKR9PdHPK8pStTWyhmLQCa8hnAKZIFUH9OzXaL0yTkKb09WwY2VpCtbRbAfMXCHamXC374eT1XP1IahgUsmFvcKeo4xIM8YeUmzOMdUSDCPVsvNg0zy7I7FdZwmbFllsizsvkrdA70;

{"id": "MN_ae3b6d2b8726cb08b5534ee0c3e2edc7"}

###

# curl --location --request POST 'http://127.0.0.1:8080/fs-crm-fmcg-wq-web/API/v1/rest/object/UserScheduleObj/controller/ListFillData?triggerFlow=true&skipFunctionAction=true&triggerWorkFlow=false'
#--header 'x-fs-peer-name: OpenAPI-V2.0'
#--header 'x-fs-userInfo: -10000'
#--header 'x-fs-ei: 84788'
#--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)'
#--header 'Content-Type: application/json;charset=utf-8'
#--data-raw '{
#    "userScheduleContent": {
#        "excelAccountIds": [
#            "63730ce7be72310001aaf525"
#        ]
#    }
#}'
POST http://127.0.0.1:80/API/v1/rest/object/DisplayDistrAchSummaryObj/controller/TotalReport
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 784077
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "accountId": "65f7e0451fb7080001d04dee",
  "businessDataApiName": "TPMActivityProofObj",
  "businessDataId": "681075ebf643240001483a20",
  "checkinsId": "68103c944a024834cae65e1d",
  "dataMainApiName": "TPMActivityProofObj",
  "dataMainId": "681075ebf643240001483a20",
  "recordType": "activity_evidence_report__c",
  "standardMainApiName": "TPMActivityAgreementObj",
  "standardMainId": "6810759a63f29b0001274cbf",
  "tenantId": "784077"
}

###

14:47:39.934 [Thread-8] INFO  c.f.p.a.j.p.RequestLogFilter fs-crm-fmcg-service/foneshare-gray/6810760af643240001484e07  uri:/
v1/object/DisplayDistrAchSummaryObj/controller/TotalReport,method:POST,queryParameters:{},headers:[accept-encoding=gzip,apibu
s-inner-application=FS-CRM-FMCG-WQ,apibus-inner-route-vip=FS-CRM-FMCG-WQ-STAGE,content-length=403,content-type=application/js
on; charset=utf-8,host=************:15238,user-agent=okhttp/4.11.0,x-forwarded-for=*************,x-forwarded-host=ncrm.nsvc.f
oneshare.cn,x-forwarded-port=8887,x-forwarded-proto=http,x-fs-ei=784077,x-fs-employee-id=6449,x-fs-enterprise-id=784077,x-fs-
peer-name=fs-crm-fmcg-service,x-fs-rpc-id=0.164,x-fs-trace-id=fs-crm-fmcg-service/foneshare-gray/6810760af643240001484e07,x-f
s-userinfo=6449,x-peer-name=fs-crm-fmcg-service/foneshare-gray,x-real-ip=*************,x-sys-not-replace=all,x-tenant-id=7840
77,x-trace=traceId=fs-crm-fmcg-service/foneshare-gray/6810760af643240001484e07;userId=null;color=false;rpcId=0.164,x-user-id=
6449],body:{"accountId":"65f7e0451fb7080001d04dee","businessDataApiName":"TPMActivityProofObj","businessDataId":"681075ebf643
240001483a20","checkinsId":"68103c944a024834cae65e1d","dataMainApiName":"TPMActivityProofObj","dataMainId":"681075ebf64324000
1483a20","recordType":"activity_evidence_report__c","standardMainApiName":"TPMActivityAgreementObj","standardMainId":"6810759
a63f29b0001274cbf","tenantId":"784077"},bodySize:403,byteCount:403 B




# curl --location --request POST 'http://127.0.0.1:8080/API/v1/object/CheckinsObj/controller/TestRoute'
#--header 'x-fs-ei: <x-fs-ei>'
#--header 'X-fs-Enterprise-Id: <X-fs-Enterprise-Id>'
#--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)'
#--header 'content-type: <content-type>'
#--data-raw '<body data here>'
#DisplayDistrAchSummaryProdMatResController
POST http://127.0.0.1:8080/API/v1/object/DisplayDistrAchSummaryObj/controller/ProdMatRes
x-fs-ei: 83921
X-fs-Enterprise-Id: 83921
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json; charset=UTF-8

{
  "accountId" : "659fd3d281e51600019ee41a",
  "businessDataApiName" : "TPMActivityProofObj",
  "businessDataId" : "67f488a1aeb42f000192e025",
  "checkinsId" : "67f48839ea3fa82cdcc5a7ca",
  "dataMainApiName" : "TPMActivityProofObj",
  "dataMainId" : "67f4cb37716e460001a4d991",
  "recordType" : "activity_evidence_report__c",
  "standardMainApiName" : "TPMActivityAgreementObj",
  "standardMainId" : "67f33ca80a96e200011a1914",
  "tenantId" : "83921"
}
###


POST http://127.0.0.1:8080/API/v1/object/DisplayDistrAchSummaryObj/controller/TotalReport
x-fs-ei: 83921
X-fs-Enterprise-Id: 83921
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json; charset=UTF-8

{
  "accountId" : "659fd3d281e51600019ee41a",
  "businessDataApiName" : "TPMActivityProofObj",
  "businessDataId" : "67f488a1aeb42f000192e025",
  "checkinsId" : "67f48839ea3fa82cdcc5a7ca",
  "dataMainApiName" : "TPMActivityProofObj",
  "dataMainId" : "67f4cb37716e460001a4d991",
  "recordType" : "activity_evidence_report__c",
  "standardMainApiName" : "TPMActivityAgreementObj",
  "standardMainId" : "67f33ca80a96e200011a1914",
  "tenantId" : "83921"
}

