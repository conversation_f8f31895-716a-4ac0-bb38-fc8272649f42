package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 数据上报标准的DAO
 * @author: zhangsm
 * @create: 2025-03-07 17:45
 **/
@Component
public class DataReportResultDao extends AbstractDao {

    /**
     * 通过业务单据id 查询数据上报结果主数据
     *
     * @param businessApiName 业务单据api名称
     * @param businessId      业务单据id
     * @return 数据上报结果主数据
     */
    public List<IObjectData> getMainByBusinessId(User user, String businessApiName, String businessId,String recordType) {
        SearchQuery.SearchQueryBuilder eq = SearchQuery.builder()
                .eq(DisplayDistrAchSummaryFields.BUSINESS_DOCUMENTS_APINAME, businessApiName)
                .eq(DisplayDistrAchSummaryFields.BUSINESS_DOCUMENTS_ID, businessId);
        if (StringUtils.isNotBlank(recordType)){
            eq.eq(BaseField.recordType.getApiName(),recordType);
        }
        List<IObjectData> resultMainList = getAllIObjectDataListByQuery(user, eq.build(), DisplayDistrAchSummaryFields.API_NAME);
        if (resultMainList != null && !resultMainList.isEmpty()) {
            return resultMainList;
        }
        return null;
    }

    public List<IObjectData> getMainByBusinessId(User user, String businessApiName, String businessId,List<String> recordTypeList) {
        List<IObjectData> resultMainList = getAllIObjectDataListByQuery(user, SearchQuery.builder()
                .eq(DisplayDistrAchSummaryFields.BUSINESS_DOCUMENTS_APINAME, businessApiName)
                .eq(DisplayDistrAchSummaryFields.BUSINESS_DOCUMENTS_ID, businessId)
                .in(BaseField.recordType.getApiName(),recordTypeList).build(), DisplayDistrAchSummaryFields.API_NAME);
        if (resultMainList != null && !resultMainList.isEmpty()) {
            return resultMainList;
        }
        return Lists.newArrayList();
    }

    /**
     * 通过数据上报结果主数据删除数据上报结果
     *
     * @param user             用户
     * @param dataReportResult 数据上报结果
     */
    public void deleteByDisplayDistrAchSummaryObj(User user, IObjectData dataReportResult) {
        List<String> mainIds = Lists.newArrayList(dataReportResult.getId());
        // 删除陈列铺货达成汇总
        batchInvalidAndDelOfSingleApiName(Lists.newArrayList(dataReportResult), user);

        //删除陈列形式达成总结
        delLookUp(user, mainIds, SummaryDisplayAchievementFields.API_NAME, Sets.newHashSet(
                SummaryDisplayAchievementFields.RELATED_REPORT));
        // 删除 陈列项目达成
        delLookUp(user, mainIds, DisplayProjectAchievementFields.API_NAME, Sets.newHashSet(
                DisplayProjectAchievementFields.RELATED_REPORT));
        // 删除 陈列分销达成
        delLookUp(user, mainIds,
                DistributionProductsAchievedFields.API_NAME, Sets.newHashSet(
                        DistributionProductsAchievedFields.RELATED_REPORT));
    }

}
