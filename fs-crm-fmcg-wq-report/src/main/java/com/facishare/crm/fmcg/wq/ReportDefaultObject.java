package com.facishare.crm.fmcg.wq;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhangsm on 2018/4/8/0008.
 */
@Slf4j
public enum ReportDefaultObject implements PreDefineObject {
    //{
    //                "DisplayFormObj",
    //                "DisplayProjectsObj",
    //                "DisplayTypeStandardsObj",
    //                "MaterialStandardDetailsObj",
    //                "MaterialStandardRequiremObj",
    //                "ProductItemStandardDetailObj",
    //                "ProductItemStandardObj",
    //                "DisplayDistrAchSummaryObj",
    //                "DisplayProjectAchievementObj",
    //                "DistributionProductsAchievedObj",
    //                "SummaryDisplayAchievementObj",
    //        }

    DisplayProjects("DisplayProjectsObj"),
    DisplayTypeStandards("DisplayTypeStandardsObj"),
    MaterialStandardDetails("MaterialStandardDetailsObj"),
    MaterialStandardRequirem("MaterialStandardRequiremObj"),
    ProductItemStandardDetail("ProductItemStandardDetailObj"),
    ProductItemStandard("ProductItemStandardObj"),
    DisplayDistrAchSummary("DisplayDistrAchSummaryObj"),
    DisplayProjectAchievement("DisplayProjectAchievementObj"),
    DistributionProductsAchieved("DistributionProductsAchievedObj"),
    SummaryDisplayAchievement("SummaryDisplayAchievementObj"),
    DisplayForm("DisplayFormObj"),
    /**
     * SuccessfulStoreRangeObj
     * ProjectStandardsObj
     * MustDistributeProductsObj
     */
    SuccessfulStoreRange("SuccessfulStoreRangeObj"),
    ProjectStandards("ProjectStandardsObj"),
    MustDistributeProducts("MustDistributeProductsObj")


    ;
    private final String apiName;

    private static String PACKAGE_NAME = ReportDefaultObject.class.getPackage().getName();

    ReportDefaultObject(String apiName) {
        this.apiName = apiName;
    }

    public static ReportDefaultObject getEnum(String apiName) {
        List<ReportDefaultObject> list = Arrays.asList(ReportDefaultObject.values());
        return list.stream().filter(m -> m.getApiName().equalsIgnoreCase(apiName)).findAny().orElse(null);
    }

    public static void init() {
        for (ReportDefaultObject object : ReportDefaultObject.values()) {
            log.info("init {}", object.toString());
            PreDefineObjectRegistry.register(object);
        }
    }

    @Override
    public String getApiName() {
        return this.apiName;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this + actionCode + "Action";
        return new ActionClassInfo(className);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this + methodName + "Controller";
        return new ControllerClassInfo(className);
    }

    public static void main(String[] args) {
        String enumValuesJoinedBySemicolon = Arrays.stream(ReportDefaultObject.values()).map(ReportDefaultObject::getApiName).distinct().collect(Collectors.joining(","));
        String enumValuesJoinedByPipe = Arrays.stream(ReportDefaultObject.values()).map(ReportDefaultObject::getApiName).distinct().collect(Collectors.joining("|"));
        System.out.println(enumValuesJoinedBySemicolon);
        System.out.println(enumValuesJoinedByPipe);
        List<Map<String ,String>> list = Arrays.stream(ReportDefaultObject.values()).map(m -> {
            Map<String, String> map = new HashMap<>();
            map.put("apiName", m.getApiName());
            map.put("hidden", "false");
            return map;
        }).collect(Collectors.toList());
        String jsonStr = JSON.toJSONString(list);
        System.out.println(jsonStr);
        // "DisplayFormObj": {
        //            "business": [
        //                {
        //                    "tenants": [
        //
        //                    ],
        //                    "data": {
        //                        "edit_layout": 1,
        //                        "is_master": 1,
        //                        "tile_mode": 1,
        //                        "table_mode": 1,
        //                        "ui_event": 1,
        //                        "summary_template_layout": 1,
        //                        "mobile_list_layout": 1,
        //                        "display_name": 1,
        //                        "layout_rule_page": 1,
        //                        "mobile_list_layout":1,
        //                        "new_scene": 1
        //                    },
        //                    "group": "default"
        //                }
        //            ]
        //        },
//        增加上面类似配置， fs-paas-filter-config-fmcg
        Map<String, Object> map = new HashMap<>();
        for (ReportDefaultObject object : ReportDefaultObject.values()) {
            map.put(object.getApiName(), JSON.parse("{\"business\":[{\"tenants\":[],\"data\":{\"add\":1,\"edit\":1,\"enable\":1,\"display\":1,\"remove\":1,\"attrs\":{\"is_required\":1,\"wheres\":1,\"label\":1,\"target_related_list_label\":1,\"related_wheres\":1}},\"group\":\"default\"}]}"));
        }
        System.out.println(JSON.toJSONString(map));

    }
}
