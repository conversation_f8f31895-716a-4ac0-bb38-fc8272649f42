package com.facishare.crm.fmcg.wq.rule.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantFieldCustomConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.FieldMappingConfigField;
import com.facishare.crm.fmcg.wq.rule.service.RuleMappingService;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 字段映射配置管理器
 * <p>
 * 该类负责从配置工厂获取和管理字段映射配置。
 * 字段映射使用格式：tenantId.sourceApiName.targetApiName.fieldName=sourceField
 */
@Slf4j
@Component
public class FieldMappingConfigManager {

    @Autowired
    private RuleMappingService ruleMappingService;
    // 缓存每个租户/源对象/目标对象的字段映射


    private Map<String, Map<String, FieldMappingConfigField>> fieldMappingCache = new ConcurrentHashMap<>();

    private List<TenantFieldCustomConfig> preFieldMappingCache = new ArrayList<>();

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        // 加载映射配置到缓存
        loadMappingConfigs();
    }
    
    /**
     * 从配置工厂加载映射配置
     */
    private void loadMappingConfigs() {
        ConfigFactory.getConfig("checkins-v2-config", config -> {
            processFieldMappings(config);
            processPreFieldMappings(config);
        });
    }

    private void processPreFieldMappings(IConfig config) {
        String preFieldMappingStr = config.get("preFieldMapping", "[{\"sourceObjectApiName\":\"ShelfReportDetailObj\",\"standardObjectApiName\":\"ProductItemStandardDetailObj\",\"fieldMappings\":[{\"targetField\":\"ProjectStandardsObj.display_format\",\"sourceField\":\"display_format\"},{\"targetField\":\"product_name\",\"sourceField\":\"product_id\"},{\"targetField\":\"ProjectStandardsObj.display_format\",\"sourceField\":\"display_format\"},{\"targetFieldName\":\"排面\",\"sourceField\":\"row_number\"},{\"targetFieldName\":\"SKU数量\",\"aggregationConfig\":{\"type\":\"DISTINCT_COUNT\"},\"sourceField\":\"product_id\"}]},{\"sourceObjectApiName\":\"MaterialReportDetailObj\",\"standardObjectApiName\":\"MaterialStandardDetailsObj\",\"fieldMappings\":[{\"targetField\":\"ProjectStandardsObj.display_format\",\"sourceField\":\"display_format\"},{\"targetField\":\"material_name\",\"sourceField\":\"material_name\"},{\"targetFieldName\":\"物料数\",\"sourceField\":\"quantity\"}]},{\"sourceObjectApiName\":\"MaterialReportDetailObj\",\"standardObjectApiName\":\"ProjectStandardsObj\",\"fieldMappings\":[{\"targetField\":\"ProjectStandardsObj.display_format\",\"sourceField\":\"display_format\"},{\"targetFieldName\":\"物料数\",\"sourceField\":\"quantity\"}]},{\"sourceObjectApiName\":\"ShelfReportDetailObj\",\"standardObjectApiName\":\"ProjectStandardsObj\",\"fieldMappings\":[{\"targetField\":\"ProjectStandardsObj.display_format\",\"sourceField\":\"display_format\"},{\"targetFieldName\":\"排面\",\"sourceField\":\"row_number\"},{\"targetFieldName\":\"SKU数量\",\"aggregationConfig\":{\"type\":\"DISTINCT_COUNT\"},\"sourceField\":\"product_id\"}]}]"); //ignoreI18n
        List<TenantFieldCustomConfig> preFieldMappingList = JSON.parseArray(preFieldMappingStr, TenantFieldCustomConfig.class);
        if (CollectionUtils.isNotEmpty(preFieldMappingList)) {
            preFieldMappingCache = preFieldMappingList;
            ruleMappingService.clearAllCaches();
        }
    }

    /**
     * 处理字段映射配置
     * 
     * @param config 配置对象
     */
    private void processFieldMappings(IConfig config) {
        String dataReport2FieldMappingStr = config.get("dataReport2FieldMapping", "{\"83150\":[]}");
    try {
        JSONObject jsonObject = JSON.parseObject(dataReport2FieldMappingStr);
        Map<String, Map<String, FieldMappingConfigField>> newFieldMappingCache = new ConcurrentHashMap<>();
        for (String tenantId : jsonObject.keySet()) {
            JSONArray mappingList = jsonObject.getJSONArray(tenantId);
            for (int i = 0; i < mappingList.size(); i++) {
                TenantFieldCustomConfig fieldMapping = JSON.parseObject(mappingList.getString(i), TenantFieldCustomConfig.class);
                String key = buildTenantObjectKey(tenantId, fieldMapping.getSourceObjectApiName(), fieldMapping.getStandardObjectApiName());
                fieldMapping.getFieldMappings().forEach(fieldMappingConfigField -> {
                    newFieldMappingCache.computeIfAbsent(key, k -> new ConcurrentHashMap<>()).put(fieldMappingConfigField.getTargetField(), fieldMappingConfigField);
                });
            }
        }
        if (MapUtils.isNotEmpty(newFieldMappingCache)){
            fieldMappingCache = newFieldMappingCache;
            ruleMappingService.clearAllCaches();
        }
    } catch (Exception e) {
        log.error("Error parsing field mapping configuration: {}", e.getMessage());
    }

    }

    /**
     * 获取特定租户/源对象/标准对象的所有字段映射
     * 
     * @param tenantId 租户ID
     * @param sourceObjectApiName 源对象API名称
     * @param standardObjectApiName 标准对象API名称
     * @return 字段映射Map，key为目标字段名，value为源字段名
     */
    public Map<String, FieldMappingConfigField> getFieldMappingConfig(String tenantId, String sourceObjectApiName, String standardObjectApiName) {
        String key = buildTenantObjectKey(tenantId, sourceObjectApiName, standardObjectApiName);
        Map<String, FieldMappingConfigField> fieldMap = fieldMappingCache.get(key);
        if (fieldMap == null) {
            List<FieldMappingConfigField> fieldMappingConfigFields = preFieldMappingCache.stream()
                    .filter(config -> config.getSourceObjectApiName().equals(sourceObjectApiName) && config.getStandardObjectApiName().equals(standardObjectApiName))
                    .findFirst()
                    .map(TenantFieldCustomConfig::getFieldMappings).orElse(null);
            if (CollectionUtils.isNotEmpty(fieldMappingConfigFields)) {
                fieldMap = new HashMap<>();
                for (FieldMappingConfigField fieldMappingConfigField : fieldMappingConfigFields) {
                    if (fieldMappingConfigField.getTargetField() != null) {
                        fieldMap.put(fieldMappingConfigField.getTargetField(), fieldMappingConfigField);
                    } else if (fieldMappingConfigField.getTargetFieldName() != null) {
                        fieldMap.put(fieldMappingConfigField.getTargetFieldName(), fieldMappingConfigField);
                    }
                }
            }

        }
        return fieldMap != null ? new HashMap<>(fieldMap) : new HashMap<>();
    }
    
//    /**
//     * 获取特定字段的映射
//     *
//     * @param tenantId 租户ID
//     * @param sourceObjectApiName 源对象API名称
//     * @param standardObjectApiName 标准对象API名称
//     * @param targetField 目标字段名称
//     * @return 源字段名称，如果没有找到则返回null
//     */
//    public FieldMappingConfigField getFieldMapping(String tenantId, String sourceObjectApiName, String standardObjectApiName, String targetField) {
//        String key = buildTenantObjectKey(tenantId, sourceObjectApiName, standardObjectApiName);
//        Map<String, FieldMappingConfigField> fieldMap = fieldMappingCache.get(key);
//        return fieldMap != null ? fieldMap.get(targetField) : null;
//    }

    
    /**
     * 构建租户/源对象/目标对象的缓存键
     * 
     * @param tenantId 租户ID
     * @param sourceObjectApiName 源对象API名称
     * @param standardObjectApiName 标准对象API名称
     * @return 缓存键
     */
    private String buildTenantObjectKey(String tenantId, String sourceObjectApiName, String standardObjectApiName) {
        return tenantId + "." + sourceObjectApiName + "." + standardObjectApiName;
    }

    /**
     * 获取预置的 对象关系映射 里的所有 fieldName
     * @return fieldName 集合
     */
    public Set<String> getAllFieldNames() {
        //取 preFieldMappingCache 里的所有 fieldName
        Set<String> fieldNames = new HashSet<>();
        for (TenantFieldCustomConfig fieldMapping : preFieldMappingCache) {
            if (CollectionUtils.isNotEmpty(fieldMapping.getFieldMappings())) {
                fieldMapping.getFieldMappings().forEach(fieldMappingConfigField -> {
                    if (StringUtils.isNotBlank(fieldMappingConfigField.getTargetFieldName())) {
                        fieldNames.add(fieldMappingConfigField.getTargetFieldName());
                    }
                });
            }
        }
        return fieldNames;
    }




} 
