package com.facishare.crm.fmcg.wq.rule.model.mapping.base;

import java.util.List;

import lombok.Data;

/**
 * Rule Field Condition Configuration
 * <p>
 * Defines a single condition based on a field value.
 */
@Data
public class RuleFieldConditionConfig {
    /**
     * Target object API name
     */
    private String targetApiName;

    /**
     * Target field name to apply the condition to
     */
    private String targetField;

    /**
     * Source field data type
     */
    private String sourceFieldType;

    /**
     * API name for lookup fields
     */
    private String sourceLookUpApiName;

    /**
     * List of supported field types for this condition
     */
    private List<String> supportFields;

    /**
     * Condition operator (e.g., "=", ">", "CONTAINS")
     */
    private String operator;

    /**
     * Value mapping configuration to apply before condition evaluation
     */
    private MappingConfig valueMappingConfig;

    /**
     * Key mapping configuration for field names
     */
    private MappingConfig keyMappingConfig;

    /**
     * Aggregation configuration for multi-value fields
     */
    private AggregationConfig aggregationConfig;

    private String targetFieldName;
    /**
     * 期望值字段
     */
    private Boolean expectValueField;

    /**
     * Create a simple equals condition
     *
     * @param field Field name to check
     * @param value Value to compare against
     * @return The configured condition
     */
    public static RuleFieldConditionConfig createEqualsCondition(String field, Object value) {
        RuleFieldConditionConfig condition = new RuleFieldConditionConfig();
        condition.setTargetField(field);
        condition.setOperator("=");

        MappingConfig valueMapping = new MappingConfig();
        valueMapping.setMappingType(MappingType.DEFAULT_VALUE);
        valueMapping.setDefaultValue(value.toString());
        condition.setValueMappingConfig(valueMapping);

        return condition;
    }
} 