package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 数据上报标准的DAO
 * @author: zhangsm
 * @create: 2025-03-07 17:45
 **/
@Slf4j
@Component
public class DataReportDataDao extends AbstractDao {


    /**
     * override getMainAndDetailObjectData
     */
    @Override
    public Map<String/* apiName */, Map<String/* _id */, IObjectData>> getMainAndDetailObjectData(User user,
                                                                                                  String mainObjectApiName, String mainObjectId){
        Map<String, Map<String, IObjectData>> mainAndDetailObjectData = super.getMainAndDetailObjectData(user, mainObjectApiName, mainObjectId);
        //数据处理  tpm活动 拍照需要处理
        if (mainObjectApiName.equals(TPMActivityProofFields.API_NAME)) {
            // 获取主表数据
            IObjectData mainObject = mainAndDetailObjectData.get(mainObjectApiName).get(mainObjectId);
            // 获取从表数据 TPMActivityProofProductDetailObj
            Map<String, IObjectData> productDetailMap = mainAndDetailObjectData.get(TPMActivityProofProductDetailFields.API_NAME);
            // 遍历从表数据
            if (MapUtils.isNotEmpty(productDetailMap)) {
                for (IObjectData detailObject : productDetailMap.values()) {
                    //取图片id
                    String imageId = detailObject.get(TPMActivityProofProductDetailFields.ACTIVITY_PROOF_DISPLAY_IMG_ID, String.class);
                    if (imageId != null) {
                        //取图片对象
                        Object image = Optional.ofNullable(mainAndDetailObjectData.get(TPMActivityProofDisplayImgFields.API_NAME))
                                .map(map -> map.get(imageId))
                                .map(o -> o.get(TPMActivityProofDisplayImgFields.IMAGE, Object.class))
                                .orElse(null);
                        if (image != null) {
                            detailObject.set(DataReport2PublicFieldsConstants.DisplayTypeStandardsFields_Virtual_DISPLAY_PHOTO, image);
                        }
                    }
                }
            }

            //取 物料从对象 TPMActivityProofMaterialDetailObj
            Map<String, IObjectData> materialDetailMap = mainAndDetailObjectData.get(TPMActivityProofMaterialDetailFields.API_NAME);
            //判空
            if (MapUtils.isNotEmpty(materialDetailMap)) {
                for (IObjectData materialDetailObject : materialDetailMap.values()) {
                    //取图片id
                    String imageId = materialDetailObject.get(TPMActivityProofMaterialDetailFields.ACTIVITY_PROOF_DISPLAY_IMG_ID, String.class);
                    if (imageId != null) {
                        //取图片对象
                        Object image = Optional.ofNullable(mainAndDetailObjectData.get(TPMActivityProofDisplayImgFields.API_NAME))
                                .map(map -> map.get(imageId))
                                .map(o -> o.get(TPMActivityProofDisplayImgFields.IMAGE, Object.class))
                                .orElse(null);
                        if (image != null) {
                            materialDetailObject.set(DataReport2PublicFieldsConstants.DisplayTypeStandardsFields_Virtual_DISPLAY_PHOTO, image);
                        }
                    }
                }
            }
            //取 TPMActivityProofDetailFields.API_NAME
            Map<String, IObjectData> tmpActivityProofDetailMap = mainAndDetailObjectData.get(TPMActivityProofDetailFields.API_NAME);
            //判空
            if (MapUtils.isNotEmpty(tmpActivityProofDetailMap)) {
                // 费用项目列表
                List<String> expenseItemIds = Lists.newArrayList();
                for (IObjectData detailObject : tmpActivityProofDetailMap.values()) {
                    //取 费用项目id
                    String expenseItemId = detailObject.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, String.class);
                    if (expenseItemId!= null) {
                        expenseItemIds.add(expenseItemId);
                    }
                }
                Map<String, String> expenseItemMap = null;
                //查 费用项目列表
                if (expenseItemIds.size() > 0) {
                    // 查费用项目列表
                    SearchQuery searchQuery = SearchQuery.builder().in(BaseField.id.getApiName(),expenseItemIds).build();
                    List<IObjectData> expenseItems = getAllIObjectDataListByQuery(user, searchQuery, TPMActivityItemFields.API_NAME);
                    // 遍历费用项目列表
                    if (CollectionUtils.isNotEmpty(expenseItems)) {
                        //list 转 map key 为 id value 为 陈列项目id
                        expenseItemMap = expenseItems.stream()
                                // 过滤掉DISPLAY_PROJECTS_ID为null的项目，防止NullPointerException
                                .filter(o -> o.get(TPMActivityItemFields.DISPLAY_PROJECTS_ID, String.class) != null)
                                .collect(java.util.stream.Collectors.toMap(IObjectData::getId, o -> o.get(TPMActivityItemFields.DISPLAY_PROJECTS_ID, String.class)));
                    }
                }
                // 遍历从表数据
                if (MapUtils.isNotEmpty(tmpActivityProofDetailMap) && MapUtils.isNotEmpty(expenseItemMap)) {
                    for (IObjectData detailObject : tmpActivityProofDetailMap.values()) {
                        //取 费用项目id
                        String expenseItemId = detailObject.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, String.class);
                        //取 陈列项目id
                        String displayProjectId = expenseItemMap.get(expenseItemId);
                        if (StringUtils.isNotBlank(displayProjectId)) {
                            //赋值 用TMP_PROJECT_STANDARD_FIELD
                            detailObject.set(DataReport2PublicFieldsConstants.TPM_PROJECT_STANDARD_FIELD, displayProjectId);
                        }
                    }
                }
            }
        }
        return mainAndDetailObjectData;
    }

}
