package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.exception.CheckinsErrorCode;
import com.facishare.crm.fmcg.wq.exception.CheckinsException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DisplayProjectsBulkInvalidAction extends StandardBulkInvalidAction {
    @Override
    protected void before(Arg arg) {
        //不支持
        throw new CheckinsException(CheckinsErrorCode.UNSUPPORTED);
    }
}

