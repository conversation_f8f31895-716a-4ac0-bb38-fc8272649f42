package com.facishare.crm.fmcg.wq.rule.model.mapping.base;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Aggregation Configuration
 * <p>
 * Defines how multiple values should be aggregated when processing lists of data.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AggregationConfig {
    /**
     * Type of aggregation to perform
     *
     * @see AggregationType
     */
    private AggregationType type;

    /**
     * Fields to group by before aggregation
     */
    private List<String> groupByFields;

    /**
     * Create a simple aggregation configuration
     *
     * @param type Aggregation type to use
     * @return The configured aggregation
     */
    public static AggregationConfig create(AggregationType type) {
        return AggregationConfig.builder()
                .type(type)
                .build();
    }
} 