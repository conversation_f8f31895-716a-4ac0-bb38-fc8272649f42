package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.report.model.DisplayReportDetail;
import com.facishare.crm.fmcg.wq.report.model.ReportType;
import com.facishare.crm.fmcg.wq.report.service.DisplayDistrAchSummaryService;
import com.facishare.crm.fmcg.wq.report.service.DisplayReportDetailService;
import com.facishare.crm.fmcg.wq.report.service.DistributionReportDetailService;
import com.facishare.appserver.checkins.model.common.IdAndName;
import com.facishare.crm.fmcg.wq.constants.DisplayDistrAchSummaryFields;
import com.facishare.crm.fmcg.wq.model.RestResult;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/03/20/ 17:48
 **/
public class DisplayDistrAchSummaryDisplayReportDetailController extends PreDefineController<DisplayReportDetail.Arg, RestResult<DisplayReportDetail.Result>> {

    private DisplayDistrAchSummaryService displayDistrAchSummaryService = SpringUtil.getContext().getBean(DisplayDistrAchSummaryService.class);
    private DisplayReportDetailService displayReportDetailService = SpringUtil.getContext()
            .getBean(DisplayReportDetailService.class);
    private DistributionReportDetailService distributionReportDetailService = SpringUtil.getContext()
            .getBean(DistributionReportDetailService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected RestResult<DisplayReportDetail.Result> doService(DisplayReportDetail.Arg arg) {
        DisplayReportDetail.Result result = new DisplayReportDetail.Result();
        checkArgs(arg);
        ReportType reportType = StringUtils.isNotBlank(arg.getDataId()) ? ReportType.ACTIVITY : ReportType.DISPLAY;
        String tenantId = controllerContext.getTenantId();
        User systemUser = User.systemUser(tenantId);
        // 根据报告类型获取汇总数据
        IdAndName idAndName = displayDistrAchSummaryService.buildReportIdAndApiName(systemUser, reportType, arg.getDataId(), arg.getReportApiName(), arg.getActionId(), arg.getCheckinsId());
        List<IObjectData> objectDataList = displayDistrAchSummaryService.getDataByReportType(systemUser, reportType, idAndName);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return RestResult.<DisplayReportDetail.Result>builder().code(-1).message("no data").data(result).build();
        }
        if (arg.isShow == 1) {
            // 通过业务类型过滤
            IObjectData objectData = objectDataList.stream()
                    .filter(item -> item.getRecordType().equals(reportType.getRecord()))
                    .findFirst().orElse(null);
            if(Objects.nonNull(objectData)) {
                result.setDisplayInfos(displayReportDetailService.buildReportData(systemUser, objectData));
                result.setIsShow(1);
            }
        }
        if (arg.isMustShow == 1) {
            IObjectData objectData = objectDataList.stream()
                    .filter(item -> item.getRecordType().equals(DisplayDistrAchSummaryFields.RECORD_DISTRIBUTION))
                    .findFirst().orElse(null);
            if(Objects.nonNull(objectData)) {
                result.setDistributionInfos(distributionReportDetailService.buildReportData(systemUser, objectData.getId()));
                result.setIsMustShow(1);
            }
        }

        return RestResult.<DisplayReportDetail.Result>builder().code(0).message("success").data(result).build();
    }

    private void checkArgs(DisplayReportDetail.Arg arg){
        if(StringUtils.isBlank(arg.getReportType())){
            throw new ValidateException("参数错误"); //ignoreI18n
        }
        if(StringUtils.isBlank(arg.getCheckinsId()) || StringUtils.isBlank(arg.getActionId())){
            throw new ValidateException("参数错误"); //ignoreI18n
        }
    }

}
