package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.constants.SuccessfulStoreRangeFields;
import com.facishare.crm.fmcg.wq.dao.DataReportResultDao;
import com.facishare.crm.fmcg.wq.model.RestResult;
import com.facishare.crm.fmcg.wq.rule.converter.AchievementResultConverter;
import com.facishare.crm.fmcg.wq.rule.model.AchievementResult;
import com.facishare.crm.fmcg.wq.rule.service.DataProcessService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-18 16:08
 **/

public class DisplayDistrAchSummaryDelByDataController extends FmcgIdempotentPreDefineController<DisplayDistrAchSummaryDelByDataController.Arg, RestResult<List<String>>> {

    DataReportResultDao dataReportResultDao = SpringUtil.getContext().getBean(DataReportResultDao.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected RestResult<List<String>> doServiceIdempotent(@Valid Arg request) {
            User user = User.systemUser(controllerContext.getTenantId());
        List<IObjectData> resultsByBusinessId = dataReportResultDao.getMainByBusinessId(user, request.getBusinessDataApiName(), request.getBusinessDataId(), request.getRecordType());
        if (CollectionUtils.isEmpty(resultsByBusinessId)){
            return RestResult.<List<String>>builder().build();
        }
        for (IObjectData iObjectData : resultsByBusinessId) {
            dataReportResultDao.deleteByDisplayDistrAchSummaryObj(user,iObjectData);
        }
        List<String> collect = resultsByBusinessId.stream().map(IObjectData::getId).distinct().collect(Collectors.toList());
        return RestResult.<List<String>>builder().data(collect).build();
    }


    @Override
    protected String getIdempotentKey(Arg arg) {
        return controllerContext.getTenantId() + controllerContext.getMethodName() + JSON.toJSONString(arg).hashCode();
    }

    @Data
    public static class Arg implements Serializable {
        /**
         * 业务单据数据
         */
        @NotBlank(message = "businessDataApiName不能为空: 业务数据主对象数据apiName")
        private String businessDataApiName;
        @NotBlank(message = "businessDataId不能为空: 业务数据主对象数据id 唯一标识,重复会覆盖")
        private String businessDataId;
//        @NotBlank(message = "recordType不能为空: 记录类型")
        //仅支持 陈列报告 (default__c)
        //         铺货报告 (distribution_report__c)
        //         活动报告 (activity_evidence_report__c)
//        @Pattern(regexp = "default__c|distribution_report__c|activity_evidence_report__c", message = "recordType仅支持default__c(陈列报告)|distribution_report__c(铺货报告)|activity_evidence_report__c(活动报告)")
        private String recordType = "default__c";
    }
}
