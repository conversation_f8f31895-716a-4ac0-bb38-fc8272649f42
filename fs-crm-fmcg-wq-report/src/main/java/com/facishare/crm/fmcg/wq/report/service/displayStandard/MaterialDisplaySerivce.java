package com.facishare.crm.fmcg.wq.report.service.displayStandard;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.report.model.DisplaySimpleInfo;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.metadata.api.DBRecord;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import com.facishare.crm.fmcg.wq.report.model.ProjectSettingType;
import com.facishare.crm.fmcg.wq.report.model.DisplayReportDetail;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

@Component
public class MaterialDisplaySerivce extends DisplayStandardService {

    @Override
    public String getDisplayStandardKey() {
        return ProjectSettingType.material.serviceKey;
    }

    @Override
    public List<DisplayReportDetail.ProjectDetailInfo> buildProjectInfo(User systemUser, List<IObjectData> dataList,Map<String,String> displayProjectValueAndColorMap) {
        List<DisplayReportDetail.ProjectDetailInfo> result = Lists.newArrayList();
        // 获取物料陈列标准ID
        Set<String> materialDisplayStandardIds = dataList.stream()
                .map(item -> String.valueOf(item.get(DisplayProjectAchievementFields.MATERIAL_STANDARDS_ID)))
                .collect(Collectors.toSet());

        Map<String, IObjectData> displayItemIdAndDataMap = dataList.stream()
                .collect(Collectors.toMap(item -> String.valueOf(item.get(BaseField.id.getApiName())), item -> item));
        // 查询物料陈列标准 得到规则描述 Map<物料陈列标准ID,物料陈列标准描述>
        Map<String, String> materialDisplayStandardMap = buildMaterialDisplayStandardMap(systemUser,
                materialDisplayStandardIds);

        // 获取树关系
        List<String> existIds = dataList.stream().map(DBRecord::getId).collect(Collectors.toList());
        Map<String,DisplaySimpleInfo> treeMap = buildDisplaySimpleInfo(dataList,existIds);
        Map<String,List<String>> ruleIdAndDataIds = dataList.stream().filter(o->Objects.isNull(o.get(DisplayProjectAchievementFields.CONDITION_PARENT_ID))
                || !existIds.contains(o.get(DisplayProjectAchievementFields.CONDITION_PARENT_ID,String.class))).collect(
                Collectors.groupingBy(k->k.get(DisplayProjectAchievementFields.RULE_GROUP_ID,String.class),Collectors.mapping(DBRecord::getId,Collectors.toList())));
        for (Map.Entry<String, List<String>> stringListEntry : ruleIdAndDataIds.entrySet()) {
            List<String> parentIds = stringListEntry.getValue();
            if(CollectionUtils.isEmpty(parentIds)){
                continue;
            }
            IObjectData displayItemData = displayItemIdAndDataMap.get(parentIds.get(0));
            // 物料陈列标准ID
            String productItemStandardId = String.valueOf(displayItemData.get(DisplayProjectAchievementFields.MATERIAL_STANDARDS_ID));
            DisplayReportDetail.ProjectDetailInfo projectDetailInfo = new DisplayReportDetail.ProjectDetailInfo();
            projectDetailInfo.setProjectBasicsInfos(Lists.newArrayList());
            projectDetailInfo.setRuleDesc(materialDisplayStandardMap.get(productItemStandardId));
            for (String parentId : parentIds) {
                displayItemData = displayItemIdAndDataMap.get(parentId);
                projectDetailInfo.getProjectBasicsInfos().add(buildBaseProjectDetailInfo(displayItemData,
                        ObjectUtils.buildManyRelationValue(displayItemData,DisplayProjectAchievementFields.REQUIRED_MATERIALS__R),
                        ObjectUtils.buildManyRelationValue(displayItemData,DisplayProjectAchievementFields.REQUIRED_MATERIALS_CLASSIFICATION__R),displayProjectValueAndColorMap));
                projectDetailInfo.getProjectBasicsInfos().addAll(buildChildProjectDetailInfo(treeMap.get(parentId).getChildInfos(),displayItemIdAndDataMap,displayProjectValueAndColorMap));
            }
            buildProjectDetailInfoHeadInfo(projectDetailInfo);
            result.add(projectDetailInfo);
        }

        return result;
    }

    private List<DisplayReportDetail.ProjectBasicsInfo> buildChildProjectDetailInfo(List<DisplaySimpleInfo> dataList,
                                                                                    Map<String, IObjectData> displayItemIdAndDataMap,Map<String,String> displayProjectValueAndColorMap) {
        List<DisplayReportDetail.ProjectBasicsInfo> result = Lists.newArrayList();
        for (DisplaySimpleInfo displayItem : dataList) {
            if (CollectionUtils.isNotEmpty(displayItem.getChildInfos())) {
                buildChildProjectDetailInfo(displayItem.getChildInfos(), displayItemIdAndDataMap,displayProjectValueAndColorMap);
            }
            IObjectData displayItemData = displayItemIdAndDataMap.get(displayItem.getDataId());
            result.add(buildBaseProjectDetailInfo(displayItemData,
                    ObjectUtils.buildManyRelationValue(displayItemData,DisplayProjectAchievementFields.REQUIRED_MATERIALS__R),
                    ObjectUtils.buildManyRelationValue(displayItemData,DisplayProjectAchievementFields.REQUIRED_MATERIALS_CLASSIFICATION__R),
                    displayProjectValueAndColorMap));
        }
        return result;
    }

    // Map<产品陈列标准ID,产品陈列标准描述>
    private Map<String, String> buildMaterialDisplayStandardMap(User systemUser, Set<String> materialDisplayStandardIds) {
        SearchQuery searchQuery = SearchQuery.builder().in(BaseField.id.getApiName(),
                materialDisplayStandardIds).build();
        List<IObjectData> materialStandardList = dataReportStandardDao.getAllQueryDataListByQueryWithFields(systemUser,
                searchQuery, 
                MaterialStandardRequiremFields.API_NAME,
                Lists.newArrayList(MaterialStandardRequiremFields.STANDARD_DESCRIPTION)).getData();
        return materialStandardList.stream().filter(o->Objects.nonNull(o.get(MaterialStandardRequiremFields.STANDARD_DESCRIPTION)))
                .collect(Collectors.toMap(item -> String.valueOf(item.get(BaseField.id.getApiName())),
                        item -> (String)item.get(MaterialStandardRequiremFields.STANDARD_DESCRIPTION)));
    }
}
