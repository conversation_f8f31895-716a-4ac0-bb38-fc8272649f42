package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.exception.CheckinsErrorCode;
import com.facishare.crm.fmcg.wq.exception.CheckinsException;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.stream.Collectors;


@Slf4j
public class DisplayProjectsAsyncBulkInvalidAction extends StandardInvalidAction {


    @Override
    protected void before(Arg arg) {
        //不支持
        throw new CheckinsException(CheckinsErrorCode.UNSUPPORTED);
    }


}
