package com.facishare.crm.fmcg.wq.rule.controller;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 字段映射处理测试控制器
 * <p>
 * 提供字段映射处理测试的Web界面
 */
@Controller
@RequestMapping("/test-mapping-process")
public class TestMappingProcessController {

    /**
     * 获取字段映射处理测试页面
     *
     * @return 字段映射处理测试页面内容
     * @throws IOException 如果读取文件失败
     */
    @GetMapping(produces = MediaType.TEXT_HTML_VALUE + ";charset=UTF-8")
    public ResponseEntity<String> getTestMappingProcessPage() throws IOException {
        // 直接读取静态HTML文件并返回内容
        Resource resource = new ClassPathResource("static/html/test-mapping-process.html");
        String htmlContent = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(MediaType.TEXT_HTML_VALUE + ";charset=UTF-8"))
                .body(htmlContent);
    }
} 