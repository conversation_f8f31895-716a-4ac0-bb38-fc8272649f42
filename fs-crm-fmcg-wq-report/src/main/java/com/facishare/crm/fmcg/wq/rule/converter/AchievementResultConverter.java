package com.facishare.crm.fmcg.wq.rule.converter;

import com.alibaba.fastjson.JSONPath;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.FmcgGray;
import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.dao.DataReportResultDao;
import com.facishare.crm.fmcg.wq.rule.engine.RuleExecutionResult;
import com.facishare.crm.fmcg.wq.rule.model.AchievementResult;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 达成结果转换器
 * 将规则执行结果转换为业务对象
 */
@Slf4j
@Component
public class AchievementResultConverter {

    private final DataReportResultDao dataReportResultDao;

    public AchievementResultConverter(DataReportResultDao dataReportResultDao) {
        this.dataReportResultDao = dataReportResultDao;
    }

    /**
     * 生成总结果对象
     * 从 formIdAndGroupIdDetailsMap 中获取数据，根据 calculationRulesAttainment 计算方式计算总体结果
     *
     * @param totalResultId 总结果ID
     * @param result AchievementResult对象
     * @param owner 所有者列表
     * @param formIdAndGroupIdDetailsMap 按陈列形式ID和规则组ID分组的达成详情Map
     * @param calculationRulesAttainment 计算规则达成方式
     * @return 总结果对象
     */
    public IObjectData generateTotalResult(User user, AchievementResult result, List<String> owner, 
            Map<String, Map<String, GroupAchievementDetail>> formIdAndGroupIdDetailsMap, 
            String calculationRulesAttainment) {
        IObjectData totalResult = dataReportResultDao.createBaseObjectData(user.getTenantId(), owner.get(0), DisplayDistrAchSummaryFields.API_NAME);
        
        // 初始化计数器
        int totalGroupCount = 0;
        int totalGroupAchievedCount = 0;
        int totalGroupPartialCount = 0;
        int totalAchievedCount = 0;
        int totalPartialCount = 0;
        // 总未陈列
        int totalGroupAbsenceCount = 0;
        // 产品
        int productGroupCount = 0;
        int productAchievedGroupCount = 0;
        // 产品未陈列
        int productAbsenceCount = 0;
        // 物料
        int materialGroupCount = 0;
        int materialAchievedGroupCount = 0;
        // 物料未陈列
        int materialAbsenceCount = 0;
        // 项目
        int projectGroupCount = 0;
        int projectAchievedCount = 0;
        // 项目未陈列
        int projectAbsenceCount = 0;
        // 陈列形式
        int displaySummaryCount = 0;
        int displaySummaryAchievedCount = 0;
        int displaySummaryPartialCount = 0;
        // 陈列形式未陈列
        int displaySummaryAbsenceCount = 0;
        // 分销
        int distributionCount = 0;
        int distributionAchievedCount = 0;

        // 未达成的陈列形式
        Set<String> summaryDisplayFormatIds = new HashSet<>();
        // 无数据
        Set<String> absenceDisplayFormatIds = result.getSummaryDisplayAchievements().stream()
                .filter(data -> data.get(SummaryDisplayAchievementFields.ACHIEVED_RESULTS).equals(SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_4))
                .map(data -> data.get(SummaryDisplayAchievementFields.RELATED_DISPLAY_ACHIEVEMENT))
                .filter(Objects::nonNull)
                .map(String::valueOf)
                .collect(Collectors.toSet());
        displaySummaryAbsenceCount = absenceDisplayFormatIds.size();
        // 要求陈列形式 从陈列形式的结果里取
        Set<String> requiredDisplayFormatIds = result.getSummaryDisplayAchievements().stream()
                .map(data -> data.get(SummaryDisplayAchievementFields.RELATED_DISPLAY_ACHIEVEMENT))
                .filter(Objects::nonNull)
                .map(String::valueOf)
                .collect(Collectors.toSet());

        // 处理每个陈列形式的规则组
        for (Map.Entry<String, Map<String, GroupAchievementDetail>> formEntry : formIdAndGroupIdDetailsMap.entrySet()) {
            String displayFormId = formEntry.getKey();
            Map<String, GroupAchievementDetail> groupDetails = formEntry.getValue();
            
            // 处理每个规则组
            for (GroupAchievementDetail groupDetail : groupDetails.values()) {
                totalGroupCount++;
                // 产品达成情况
                if (groupDetail.getProductDetail() != null) {
                    productGroupCount++;
                    if (groupDetail.getProductDetail().isAchieved()) {
                        productAchievedGroupCount++;
                    }
                    if (!groupDetail.getProductDetail().isDisplayed()) {
                        productAbsenceCount++;
                    }
                    if (groupDetail.getProductDetail().isPartial()) {
                        totalPartialCount++;
                    }
                    totalAchievedCount += groupDetail.getProductDetail().getAchievedCount();
                }
                
                // 物料达成情况
                if (groupDetail.getMaterialDetail() != null) {
                    materialGroupCount++;
                    if (groupDetail.getMaterialDetail().isAchieved()) {
                        materialAchievedGroupCount++;
                    }
                    if (!groupDetail.getMaterialDetail().isDisplayed()) {
                        materialAbsenceCount++;
                    }
                    if (groupDetail.getMaterialDetail().isPartial()) {
                        totalPartialCount++;
                    }
                    totalAchievedCount += groupDetail.getMaterialDetail().getAchievedCount();
                }
                
                // 项目达成情况
                if (groupDetail.getProjectDetail() != null) {
                    projectGroupCount++;
                    if (groupDetail.getProjectDetail().isAchieved()) {
                        projectAchievedCount++;
                    }
                    if (!groupDetail.getProjectDetail().isDisplayed()) {
                        projectAbsenceCount++;
                    }
                    if (groupDetail.getProjectDetail().isPartial()) {
                        totalPartialCount++;
                    }
                    totalAchievedCount += groupDetail.getProjectDetail().getAchievedCount();
                }

                // 检查是否未陈列
                if (!groupDetail.isDisplayed()) {
                    totalGroupAbsenceCount++;
                }
                if (groupDetail.isAchieved()) {
                    totalGroupAchievedCount++;
                }
                if (groupDetail.isPartial()) {  
                    totalGroupPartialCount++;
                }
            }
            //去掉 default
            if (displayFormId.equals("default")) {
                continue;
            }
            // 根据计算规则达成方式判断陈列形式是否达成
            boolean isFormAchieved = groupDetails.values().stream()
                    .allMatch(GroupAchievementDetail::isAchieved);
            boolean isFormDisplayed = groupDetails.values().stream()
                    .allMatch(GroupAchievementDetail::isDisplayed);
            boolean isFormPartial = groupDetails.values().stream()
                    .anyMatch(GroupAchievementDetail::isPartial);
            // displaySummaryCount++;
            // if (isFormAchieved) {
            //     displaySummaryAchievedCount++;
            // } else {
            //     summaryDisplayFormatIds.add(displayFormId);
            // }
            // if (isFormPartial) {
            //     displaySummaryPartialCount++;
            // }
            if (!isFormAchieved) {
                summaryDisplayFormatIds.add(displayFormId);
            }
        }
        for(IObjectData data : result.getSummaryDisplayAchievements()){
            displaySummaryCount++;
            if (SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_1.equals(data.get(SummaryDisplayAchievementFields.ACHIEVED_RESULTS))) {
                displaySummaryAchievedCount++;
            }
            if (SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_2.equals(data.get(SummaryDisplayAchievementFields.ACHIEVED_RESULTS))) {
                displaySummaryPartialCount++;
            }
        }
        
        // 处理分销达成情况
        List<IObjectData> distributionAchievements = result.getDistributionProductsAchievements();
        if (!CollectionUtils.isEmpty(distributionAchievements)) {
            distributionCount = distributionAchievements.size();
            distributionAchievedCount = (int) distributionAchievements.stream()
                    .filter(a -> DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_1.equals(
                            a.get(DisplayProjectAchievementFields.ACHIEVED_RESULTS)))
                    .count();
        }

        // 设置陈列形式相关字段
        totalResult.set(DisplayDistrAchSummaryFields.ABSENCE_DISPLAY_FORMS, Lists.newArrayList(absenceDisplayFormatIds));
        totalResult.set(DisplayDistrAchSummaryFields.REQUIRED_DISPLAY_FORMS, Lists.newArrayList(requiredDisplayFormatIds));
        List<String> actualDisplayFormatList = new ArrayList<>(requiredDisplayFormatIds);
        actualDisplayFormatList.removeAll(absenceDisplayFormatIds);
        totalResult.set(DisplayDistrAchSummaryFields.ACTUAL_DISPLAY_FORMS, actualDisplayFormatList);
        totalResult.set("summaryDisplayFormatIds", summaryDisplayFormatIds);
        totalResult.set(DisplayDistrAchSummaryFields.STANDARD_ACHIEVEMENT_DATE, System.currentTimeMillis());

        // 设置陈列和分销标准设置方式
        if (CollectionUtils.isNotEmpty(result.getTotalProjectAchievements())) {
            totalResult.set(DisplayDistrAchSummaryFields.METHODS_DISPLAY_STANDARDS,
                    CollectionUtils.isNotEmpty(result.getSummaryDisplayAchievements())
                            ? DisplayDistrAchSummaryFields.METHODS_DISPLAY_STANDARDS_Options_2
                            : DisplayDistrAchSummaryFields.METHODS_DISPLAY_STANDARDS_Options_1);
        }
        if (CollectionUtils.isNotEmpty(result.getDistributionProductsAchievements())) {
            totalResult.set(DisplayDistrAchSummaryFields.METHODS_DISTRIBUTION_STANDARDS,
                    CollectionUtils.isNotEmpty(result.getSummaryDisplayAchievements())
                            ? DisplayDistrAchSummaryFields.METHODS_DISTRIBUTION_STANDARDS_Options_3
                            : DisplayDistrAchSummaryFields.METHODS_DISTRIBUTION_STANDARDS_Options_2);
        }

        // 设置要求标准类型
        List<String> requiredStandardTypes = new ArrayList<>();
        if (productGroupCount > 0) {
            requiredStandardTypes.add(DisplayDistrAchSummaryFields.REQUIRED_STANDARD_TYPES_Options_1);
        }
        if (materialGroupCount > 0) {
            requiredStandardTypes.add(DisplayDistrAchSummaryFields.REQUIRED_STANDARD_TYPES_Options_3);
        }
        if (projectGroupCount > 0) {
            requiredStandardTypes.add(DisplayDistrAchSummaryFields.REQUIRED_STANDARD_TYPES_Options_5);
        }
        totalResult.set(DisplayDistrAchSummaryFields.REQUIRED_STANDARD_TYPES, requiredStandardTypes);

        // 计算各类达成率
        double productAchievementRate = productGroupCount > 0 ? (double) productAchievedGroupCount / productGroupCount : 0;
        double materialAchievementRate = materialGroupCount > 0 ? (double) materialAchievedGroupCount / materialGroupCount : 0;
        double projectAchievementRate = projectGroupCount > 0 ? (double) projectAchievedCount / projectGroupCount : 0;
        double displaySummaryAchievementRate = displaySummaryCount > 0 ? (double) displaySummaryAchievedCount / displaySummaryCount : 0;
        double distributionAchievementRate = distributionCount > 0 ? (double) distributionAchievedCount / distributionCount : 0;

        // 根据计算规则达成方式计算总体达成率
        double overallAchievementRate;
        if (DisplayProjectAchievementFields.WAYS_ACHIEVE_STANDARD_Options_1.equals(calculationRulesAttainment)) {
            // 全部达标：所有类型的达成率都必须为1
            overallAchievementRate = (productAchievementRate + materialAchievementRate + projectAchievementRate + 
                    displaySummaryAchievementRate + distributionAchievementRate) / 5;
        } else if (DisplayProjectAchievementFields.WAYS_ACHIEVE_STANDARD_Options_2.equals(calculationRulesAttainment)) {
            // 任一达标：取最高达成率
            overallAchievementRate = Math.max(Math.max(Math.max(productAchievementRate, materialAchievementRate), 
                    projectAchievementRate), Math.max(displaySummaryAchievementRate, distributionAchievementRate));
        } else {
            // 默认全部达标
            overallAchievementRate = (productAchievementRate + materialAchievementRate + projectAchievementRate + 
                    displaySummaryAchievementRate + distributionAchievementRate) / 5;
        }

        // 设置各类统计数据
        totalResult.set("product_standard_count", productGroupCount);
        totalResult.set("product_achieved_count", productAchievedGroupCount);
        totalResult.set("product_achievement_rate", productAchievementRate);
        totalResult.set("material_standard_count", materialGroupCount);
        totalResult.set("material_achieved_count", materialAchievedGroupCount);
        totalResult.set("material_achievement_rate", materialAchievementRate);
        totalResult.set("project_standard_count", projectGroupCount);
        totalResult.set("project_achieved_count", projectAchievedCount);
        totalResult.set("project_achievement_rate", projectAchievementRate);
        totalResult.set("display_form_count", displaySummaryCount);
        totalResult.set("display_form_achieved_count", displaySummaryAchievedCount);
        totalResult.set("display_form_achievement_rate", displaySummaryAchievementRate);
        totalResult.set("distribution_count", distributionCount);
        totalResult.set("distribution_achieved_count", distributionAchievedCount);
        totalResult.set("distribution_achievement_rate", distributionAchievementRate);
        totalResult.set("overall_achievement_rate", overallAchievementRate);

        // 设置总体达成状态
        String achievedStatus = null;
        // 根据是否有陈列形式来决定不同的达成状态计算逻辑
        if (displaySummaryCount > 0) {
            // 有陈列形式的情况
            if (SuccessfulStoreRangeFields.CALCULATION_RULES_ATTAINMENT_Options_all_attainment.equals(calculationRulesAttainment)) {
                // 计算规则：所有陈列形式必须达标
                if (displaySummaryAchievedCount >= displaySummaryCount) {
                    // 所有陈列形式都达标
                    achievedStatus = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_1;
                } else if (displaySummaryAchievedCount > 0 || displaySummaryPartialCount > 0) {
                    // 部分陈列形式达标或部分达标
                    achievedStatus = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_2;
                } else {
                    // 所有陈列形式都未达标
                    achievedStatus = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_3;
                }
            } else if (SuccessfulStoreRangeFields.CALCULATION_RULES_ATTAINMENT_Options_all_display_attainment.equals(calculationRulesAttainment)) {
                // 计算规则：所有陈列形式必须已陈列
                if (displaySummaryCount - displaySummaryAbsenceCount > 0) {
                    // 存在已陈列的陈列形式时才计算达标状态
                    if (displaySummaryAchievedCount >= (displaySummaryCount-displaySummaryAbsenceCount)) {
                        // 已陈列的陈列形式全部达标
                        achievedStatus = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_1;
                    } else if (displaySummaryAchievedCount > 0 || displaySummaryPartialCount > 0) {
                        // 已陈列的陈列形式部分达标
                        achievedStatus = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_2;
                    }
                }
                // 如果没有已陈列的陈列形式或未设置状态，则标记为未达标
                if (achievedStatus == null) {
                    achievedStatus = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_3;
                }
            }
        } else {
            // 无陈列形式的情况
            if (distributionCount > 0) {
                // 有分销要求时，根据分销达成情况判断
                if (distributionAchievedCount >= distributionCount) {
                    // 分销全部达标
                    achievedStatus = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_1;
                } else {
                    // 分销未达标
                    achievedStatus = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_3;
                }
            }
            if (totalGroupCount > 0) {
                if (SuccessfulStoreRangeFields.CALCULATION_RULES_ATTAINMENT_Options_all_attainment.equals(calculationRulesAttainment)) {
                    // 计算规则：所有项目必须达标
                    if (totalGroupAchievedCount >= totalGroupCount) {
                        // 所有项目都达标
                        achievedStatus = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_1;
                    } else if (totalAchievedCount > 0 || totalPartialCount > 0) {
                        // 部分项目达标或部分达标
                        achievedStatus = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_2;
                    } else {
                        // 所有项目都未达标
                        achievedStatus = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_3;
                    }
                } else if (SuccessfulStoreRangeFields.CALCULATION_RULES_ATTAINMENT_Options_all_display_attainment.equals(calculationRulesAttainment)) {
                    // 计算规则：已陈列的项目必须达标
                    if (totalGroupCount - totalGroupAbsenceCount > 0) {
                        // 存在已陈列的项目时才计算达标状态
                        if (totalGroupAchievedCount >= (totalGroupCount - totalGroupAbsenceCount)) {
                            // 已陈列的项目全部达标
                            achievedStatus = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_1;
                        } else if (totalAchievedCount > 0 || totalPartialCount > 0) {
                            // 已陈列的项目部分达标
                            achievedStatus = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_2;
                        }
                    }
                    // 如果没有已陈列的项目或未设置状态，则标记为未达标
                    if (achievedStatus == null) {
                        achievedStatus = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_3;
                    }
                }
            }
            // 如果没有任何项目且未设置状态，则标记为未达标
            if (achievedStatus == null) {
                achievedStatus = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_3;
            }
        }
        // 设置总体达成状态
        totalResult.set(DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS, achievedStatus);

        // 设置达成项和未达成项总结
        List<String> achievementSummary = new ArrayList<>();
        List<String> summaryIssues = new ArrayList<>();

        // 产品项目达成情况
        if (productGroupCount > 0) {
            if (productAchievedGroupCount >= productGroupCount) {
                achievementSummary.add(DisplayDistrAchSummaryFields.ACHIEVED_RESULTS_Options_5);
            } else {
                summaryIssues.add(DisplayDistrAchSummaryFields.SUMMARY_ISSUES_Options_5); // 产品项目未达标
            }
        }

        // 物料项目达成情况
        if (materialGroupCount > 0) {
            if (materialAchievedGroupCount >= materialGroupCount) {
                achievementSummary.add(DisplayDistrAchSummaryFields.ACHIEVED_RESULTS_Options_6);
            } else {
                summaryIssues.add(DisplayDistrAchSummaryFields.SUMMARY_ISSUES_Options_3); // 物料项目未达标
            }
        }

        // 整体项目达成情况
        if (projectGroupCount > 0) {
            if (projectAchievedCount >= projectGroupCount) {
                achievementSummary.add(DisplayDistrAchSummaryFields.ACHIEVED_RESULTS_Options_3);
            } else if (projectAchievedCount > 0) {
                achievementSummary.add(DisplayDistrAchSummaryFields.ACHIEVED_RESULTS_Options_4); // 项目部分达标
                summaryIssues.add(DisplayDistrAchSummaryFields.SUMMARY_ISSUES_Options_4); // 项目未达标
            } else {
                 summaryIssues.add(DisplayDistrAchSummaryFields.SUMMARY_ISSUES_Options_4); // 项目未达标
            }
        }

        // 陈列形式达成情况
        if (displaySummaryCount > 0) {
            if (displaySummaryAchievedCount >= displaySummaryCount) {
                achievementSummary.add(DisplayDistrAchSummaryFields.ACHIEVED_RESULTS_Options_1);
            } else if (displaySummaryAchievedCount > 0) {
                achievementSummary.add(DisplayDistrAchSummaryFields.ACHIEVED_RESULTS_Options_2); // 部分陈列形式达标
            }
        }
        // 分销产品达成情况
        if (distributionCount > 0) {
            if (distributionAchievedCount >= distributionCount) {
                achievementSummary.add(DisplayDistrAchSummaryFields.ACHIEVED_RESULTS_Options_7);
            } else {
                summaryIssues.add(DisplayDistrAchSummaryFields.SUMMARY_ISSUES_Options_2); // 分销产品未达标
            }
        }

        // 添加未陈列的问题
        if (CollectionUtils.isNotEmpty(absenceDisplayFormatIds)) {
            summaryIssues.add(DisplayDistrAchSummaryFields.SUMMARY_ISSUES_Options_1); // 陈列形式未陈列
        }

        // 设置达成项和未达成项
        if (!achievementSummary.isEmpty()) {
            totalResult.set(DisplayDistrAchSummaryFields.ACHIEVED_RESULTS, achievementSummary);
        }
        if (!summaryIssues.isEmpty()) {
            totalResult.set(DisplayDistrAchSummaryFields.SUMMARY_ISSUES, summaryIssues);
        }

        result.setDisplayDistrAchSummary(totalResult);
        return totalResult;
    }
    
    /**
     * String ruleGroupId =
     * data.get(TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID,
     * String.class);
     * ruleGroupApiName = TPMActivityAgreementDetailFields.API_NAME;
     * if (StringUtils.isBlank(ruleGroupId)) {
     * String activityItemId =
     * data.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, String.class);
     * ruleGroupApiName = TPMActivityDetailFields.API_NAME;
     * ruleGroupId = activityItemId;
     * }
     * 
     * @param data
     * @return
     */
    public Pair<String, String> getRuleGroupIdAndApiNameFormTPM(IObjectData data){
        String ruleGroupId = data.get(TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID, String.class);
        String ruleGroupApiName = TPMActivityAgreementDetailFields.API_NAME;
        if (StringUtils.isBlank(ruleGroupId)) {
            String activityItemId = data.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, String.class);
            ruleGroupApiName = TPMActivityDetailFields.API_NAME;
            ruleGroupId = activityItemId;
        }
        if (StringUtils.isBlank(ruleGroupId) || ruleGroupId == null) {
            return null;
        }
        return Pair.of(ruleGroupId, ruleGroupApiName);
    }
    /**
     * 根据活动项目  生成陈列达成结果
     *
     * @param user
     * @param data TPMActivityProofDetailObj
     * @param formIdAndGroupIdDetailsMap
     * @param productAndMaterialGroup
     * @return
     */
    public List<IObjectData> convertToDisplayAchievementByActivityItem(User user, IObjectData data, Map<String, Map<String, GroupAchievementDetail>> formIdAndGroupIdDetailsMap, Map<String, List<IObjectData>> productAndMaterialGroup) {
        // 达成状态
        String displayAchievementStatus = null;
        // 空 的当做是未陈列
        if (data.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class) == null) {
//            return null;
            data.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_not_display);
        }

        // 陈列形式id
        String displayFormId = Optional.ofNullable(data.get(TPMActivityProofDetailFields.DISPLAY_FORM_ID)).filter(Objects::nonNull).map(Object::toString).filter(StringUtils::isNotBlank).orElse("default");
        String ruleGroupApiName = null;
        // 规则组id
        Pair<String, String> ruleGroupInfo = getRuleGroupIdAndApiNameFormTPM(data);
        if (ruleGroupInfo == null) {
            return null;
        }
        String ruleGroupId = ruleGroupInfo.getKey();
        ruleGroupApiName = ruleGroupInfo.getValue();
        if (StringUtils.isBlank(ruleGroupId)|| ruleGroupId == null) {
            return  null;
        }

        // 获取或创建 GroupAchievementDetail
        Map<String, GroupAchievementDetail> groupDetails = formIdAndGroupIdDetailsMap.computeIfAbsent(displayFormId, k -> new HashMap<>());
        String finalRuleGroupId = ruleGroupId;
        boolean projectAchieved = getDoubleValue(data, TPMActivityProofDetailFields.AMOUNT) - getDoubleValue(data, TPMActivityProofDetailFields.PROOF_DETAIL_AMOUNT_STANDARD) >= 0;
        boolean isDisplayed = !data.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class,
                TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_not_display).equals(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_not_display);
        if (!isDisplayed && projectAchieved) {
            // 默认未陈列，且项目达标，设置为为达标。兼容数据问题 TODO
            projectAchieved = false;
        }
        ProjectAchievementDetail projectAchievementDetail = ProjectAchievementDetail.builder()
                .isAchieved(projectAchieved)
                //不直接取状态，改成2个数量对比
                .achievedCount(projectAchieved ? 1 : 0)
                .totalCount(1)
                .isDisplayed(isDisplayed)
                .isPendingAudit(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_pending_approval.equals(data.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class)))
                .build();
        GroupAchievementDetail groupDetail = groupDetails.computeIfAbsent(ruleGroupId, k -> GroupAchievementDetail.builder()
                .ruleGroupId(finalRuleGroupId)
                .displayFormId(displayFormId)
                .projectDetail(projectAchievementDetail)
                .build());
        if (groupDetail.getProjectDetail() == null) {
            groupDetail.setProjectDetail(projectAchievementDetail);
        } // else 暂时不处理
        
        // 根据系统判断状态设置达成状态
        if (data.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class).equals(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_pending_approval)) {
            //待审核
            displayAchievementStatus = DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_5;
            groupDetail.setPendingAudit(true);
            groupDetail.setTotalCount(groupDetail.getTotalCount() + 1);
            // totalCount > 0 ? (double) achievedCount / totalCount : 0
//            groupDetail.setAchievementRate(groupDetail.getTotalCount() > 0 ? (double) groupDetail.getAchievedCount() / groupDetail.getTotalCount() : 0);
        } else if (data.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class).equals(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_pass)) {
            //达标
            groupDetail.setAchieved(true);
            groupDetail.setDisplayed(true);
            groupDetail.setAchievedCount(groupDetail.getAchievedCount() + 1);
            displayAchievementStatus = DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_1;
//            if (groupDetail.isAchieved() ) {
//            }else{
//                groupDetail.setAchieved(false);
//                groupDetail.setDisplayed(false);
//                if (groupDetail.getAchievedCount() > 0){
//                    //部分达标
//                    displayAchievementStatus = DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_2;
//                }else {
//                    //未达标
//                    displayAchievementStatus = DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_0;
//                }
//            }

            groupDetail.setTotalCount(groupDetail.getTotalCount() + 1);
//            groupDetail.setAchievementRate(groupDetail.getTotalCount() > 0 ? (double) groupDetail.getAchievedCount() / groupDetail.getTotalCount() : 0);
        } else if (data.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class).equals(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_partial_pass)) {
            //部分达标
            displayAchievementStatus = DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_2;
            if (FmcgGray.Checkins.EI.isPartialDisplayedNotAchieved.gray(user.getTenantId())){
                displayAchievementStatus = DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_0;
            }
            groupDetail.setAchieved(false);
            groupDetail.setDisplayed(true);
            groupDetail.setPartialCount(groupDetail.getPartialCount() + 1);
            groupDetail.setTotalCount(groupDetail.getTotalCount() + 1);
//            groupDetail.setAchievementRate(groupDetail.getTotalCount() > 0 ? (double) groupDetail.getAchievedCount() / groupDetail.getTotalCount() : 0);
        } else if (data.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class).equals(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_fail)) {
            //不达标
            displayAchievementStatus = DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_0;
            groupDetail.setAchieved(false);
            groupDetail.setDisplayed(true);
            groupDetail.setTotalCount(groupDetail.getTotalCount() + 1);
//            groupDetail.setAchievementRate(groupDetail.getTotalCount() > 0 ? (double) groupDetail.getAchievedCount() / groupDetail.getTotalCount() : 0);
        }else if (data.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class).equals(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS_Options_not_display)) {
            //未陈列
            displayAchievementStatus = DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_4;
            if (FmcgGray.Checkins.EI.isPartialDisplayedNotAchieved.gray(user.getTenantId())){
                displayAchievementStatus = DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_0;
            }
            groupDetail.setAchieved(false);
            groupDetail.setTotalCount(groupDetail.getTotalCount() + 1);
//            groupDetail.setAchievementRate(groupDetail.getTotalCount() > 0 ? (double) groupDetail.getAchievedCount() / groupDetail.getTotalCount() : 0);
        }
        List<IObjectData> result = new ArrayList<>();
        //达成结果
        IObjectData displayAchievement = getDisplayProjectAchievement(user, data, productAndMaterialGroup, ruleGroupApiName, ruleGroupId, displayAchievementStatus, displayFormId, groupDetail);
        result.add(displayAchievement);
        // 取 data 系统判定方式
        String systemJudgmentMode = data.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_MODE__C, String.class);
        if (StringUtils.isNotBlank(systemJudgmentMode)) {
             if (systemJudgmentMode.equals(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_MODE__C_Options_0)){
                 //默认不处理
             }else if (systemJudgmentMode.equals(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_MODE__C_Options_1)){
                 //衡量标准+标准
//                 再生成一个衡量标准的数据
                 IObjectData displayAchievement2 = ObjectDataExt.of(displayAchievement).copy();
                 String parentId = displayAchievement.getId();
                 displayAchievement2.setId(new ObjectId().toHexString());
                 //取活动项目
                 String activityDetailId = data.get(TPMActivityProofDetailFields.ACTIVITY_DETAIL_ID, String.class);
                 if (StringUtils.isNotBlank(activityDetailId)){
                     //取活动项目对象
                     IObjectData activityDetail = dataReportResultDao.getById(user.getTenantId(), TPMActivityDetailFields.API_NAME, activityDetailId);
                     if (activityDetail != null){
                         //获取衡量标准
                        String displayProjectJudgmentStandard = activityDetail.get(TPMActivityDetailFields.DISPLAY_PROJECT_JUDGMENT_STANDARD__C, String.class);
                        if (StringUtils.isNotBlank(displayProjectJudgmentStandard)){
                            IObjectData displayProjectJudgmentStandardObj = dataReportResultDao.getById(user.getTenantId(), DisplayProjectJudgmentStandardFields.API_NAME, displayProjectJudgmentStandard);
                            if (displayProjectJudgmentStandardObj != null){
                                //陈列项目
                                displayAchievement2.set(DisplayProjectAchievementFields.PROJECT_NAME, displayProjectJudgmentStandardObj.get(DisplayProjectJudgmentStandardFields.DISPLAY_PROJECT, String.class));
                                //达标值
                                displayAchievement2.set(DisplayProjectAchievementFields.REQUIRED_PROJECT_QUANTITY, displayProjectJudgmentStandardObj.get(DisplayProjectJudgmentStandardFields.TARGET_VALUE, String.class));
                                //未达标值
                                displayAchievement2.set(DisplayProjectAchievementFields.UNMET_PROJECT_COUNT__C, displayProjectJudgmentStandardObj.get(DisplayProjectJudgmentStandardFields.UNMET_VALUE, String.class));
                                //达成父条件
                                displayAchievement2.set(DisplayProjectAchievementFields.CONDITION_PARENT_ID, parentId);
                                result.add(displayAchievement2);
                            }
                        }
                     }
                 }
             }else if (systemJudgmentMode.equals(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_MODE__C_Options_2)){
                 //只衡量标准
                 displayAchievement.set(DisplayProjectAchievementFields.WAYS_ACHIEVE_STANDARD, DisplayProjectAchievementFields.WAYS_ACHIEVE_STANDARD_Options_2);
                 //取活动项目
                 String activityDetailId = data.get(TPMActivityProofDetailFields.ACTIVITY_DETAIL_ID, String.class);
                 if (StringUtils.isNotBlank(activityDetailId)){
                     //取活动项目对象
                     IObjectData activityDetail = dataReportResultDao.getById(user.getTenantId(), TPMActivityDetailFields.API_NAME, activityDetailId);
                     if (activityDetail != null){
                         //获取衡量标准
                         String displayProjectJudgmentStandard = activityDetail.get(TPMActivityDetailFields.DISPLAY_PROJECT_JUDGMENT_STANDARD__C, String.class);
                         if (StringUtils.isNotBlank(displayProjectJudgmentStandard)){
                             IObjectData displayProjectJudgmentStandardObj = dataReportResultDao.getById(user.getTenantId(), DisplayProjectJudgmentStandardFields.API_NAME, displayProjectJudgmentStandard);
                             if (displayProjectJudgmentStandardObj != null){
                                 //陈列项目
                                 displayAchievement.set(DisplayProjectAchievementFields.PROJECT_NAME, displayProjectJudgmentStandardObj.get(DisplayProjectJudgmentStandardFields.DISPLAY_PROJECT, String.class));
                                 //达标值
                                 displayAchievement.set(DisplayProjectAchievementFields.REQUIRED_PROJECT_QUANTITY, displayProjectJudgmentStandardObj.get(DisplayProjectJudgmentStandardFields.TARGET_VALUE, String.class));
                                 //未达标值
                                 displayAchievement.set(DisplayProjectAchievementFields.UNMET_PROJECT_COUNT__C, displayProjectJudgmentStandardObj.get(DisplayProjectJudgmentStandardFields.UNMET_VALUE, String.class));
                             }
                         }
                     }
                 }
             }
        }
        return result;
    }

    @NotNull
    private IObjectData getDisplayProjectAchievement(User user, IObjectData data, Map<String, List<IObjectData>> productAndMaterialGroup, String ruleGroupApiName, String ruleGroupId, String displayAchievementStatus, String displayFormId, GroupAchievementDetail groupDetail) {
        // 创建陈列达成结果对象
        IObjectData displayAchievement = dataReportResultDao.createBaseObjectData(user.getTenantId(), null, DisplayProjectAchievementFields.API_NAME);
        //设置陈列项目
        displayAchievement.set(DisplayProjectAchievementFields.PROJECT_NAME, data.get(DataReport2PublicFieldsConstants.TPM_PROJECT_STANDARD_FIELD, String.class));
        // 设置陈列达成结果字段
        displayAchievement.set(DisplayProjectAchievementFields.RULE_GROUP_APINAME, ruleGroupApiName);
        displayAchievement.set(DisplayProjectAchievementFields.RULE_GROUP_ID, ruleGroupId);
        displayAchievement.set(DisplayProjectAchievementFields.ACHIEVED_RESULTS, displayAchievementStatus);
        //要求项目数量
        displayAchievement.set(DisplayProjectAchievementFields.REQUIRED_PROJECT_QUANTITY, data.get(TPMActivityProofDetailFields.PROOF_DETAIL_AMOUNT_STANDARD, String.class));
        //实际项目数量
        displayAchievement.set(DisplayProjectAchievementFields.ACTUAL_PROJECT_QUANTITY, data.get(TPMActivityProofDetailFields.AMOUNT, String.class));
        //差异值
        displayAchievement.set(DisplayProjectAchievementFields.DIFFERENCE_QUANTITY,calculateDifference(
                getDoubleValue(displayAchievement, DisplayProjectAchievementFields.REQUIRED_PROJECT_QUANTITY),
                getDoubleValue(displayAchievement, DisplayProjectAchievementFields.ACTUAL_PROJECT_QUANTITY)));
        //陈列形式
        displayAchievement.set(DisplayProjectAchievementFields.RELATED_DISPLAY_ACHIEVEMENT, displayFormId);
        //类型
        displayAchievement.set(DisplayProjectAchievementFields.SETTING_TYPE, DisplayProjectAchievementFields.SETTING_TYPE_Options_1);

        // 设置达成项和未达成项总结
        List<String> achievementSummary = new ArrayList<>();
        List<String> summaryIssues = new ArrayList<>();

        // 产品项目达成情况
        if (groupDetail.getProductDetail() != null) {
            if (groupDetail.getProductDetail().isAchieved()) {
                achievementSummary.add(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_4); // 产品项目达标
            } else {
//                summaryIssues.add(DisplayProjectAchievementFields.SUMMARY_ISSUES_Options_1); // 产品项目未达标
            }
        }

        // 物料项目达成情况
        if (groupDetail.getMaterialDetail() != null) {
            if (groupDetail.getMaterialDetail().isAchieved()) {
                achievementSummary.add(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_3); // 物料项目达标
            } else {
                summaryIssues.add(DisplayProjectAchievementFields.SUMMARY_ISSUES_Options_4); // 物料项目未达标
            }
        }

        // 项目达成情况
        if (groupDetail.getProjectDetail() != null) {
            if (groupDetail.getProjectDetail().isAchieved()) {
//                achievementSummary.add(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_1); // 全部项目达标
            } else if (groupDetail.getProjectDetail().getAchievedCount() > 0) {
//                achievementSummary.add(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_2); // 部分项目达标
//                summaryIssues.add(DisplayProjectAchievementFields.SUMMARY_ISSUES_Options_4); // 项目未达标
            } else {
//                summaryIssues.add(DisplayProjectAchievementFields.SUMMARY_ISSUES_Options_4); // 项目未达标
            }
        }

        // 设置达成项和未达成项
        if (!achievementSummary.isEmpty()) {
            displayAchievement.set(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY, achievementSummary);
        }
        if (!summaryIssues.isEmpty()) {
            displayAchievement.set(DisplayProjectAchievementFields.SUMMARY_ISSUES, summaryIssues);
        }

        // 给产品结果设置父条件
        List<IObjectData> productAndMatList = productAndMaterialGroup.get(ruleGroupId);
        if (CollectionUtils.isNotEmpty(productAndMatList)) {
            for (IObjectData activityItemAchievement : productAndMatList) {
                activityItemAchievement.set(DataReport2PublicFieldsConstants.CONDITION_PARENT_ID, displayAchievement.getId());
            }
        }
        // 设置陈列照片
        displayAchievement.set(DataReport2PublicFieldsConstants.DisplayTypeStandardsFields_Virtual_DISPLAY_PHOTO,getDistinctDisplayPhotos(Lists.newArrayList(data)));
        return displayAchievement;
    }


    /**
     * 转换为陈列项目达成结果
     * 使用扩展的RuleExecutionResult包含所有需要的数据
     * 
     * @param executionResult 规则执行结果
     * @return 陈列项目达成结果
     */
    public IObjectData convertToDisplayProjectAchievement(User user,
                                                          RuleExecutionResult executionResult, String standardObjectApiName) {

        if (executionResult == null) {
            log.warn("无法转换陈列项目达成结果，executionResult为空");
            return null;
        }

        // 从RuleExecutionResult获取standardData和reportData
        IObjectData standardData = executionResult.getStandardData();
        IObjectData reportData = executionResult.getReportData();

        if (standardData == null) {
            log.warn("无法转换陈列项目达成结果，standardData或reportData为空");
            return null;
        }
        // 如果reportData为空，则创建一个空的HashMap
        if (reportData == null) {
            reportData = new ObjectData();
        }
        if (MustDistributeProductsFields.API_NAME.equals(standardObjectApiName)) {
            // 铺货 比分销售
            return convertToMustDistributeProductsAchievement(user,executionResult, standardObjectApiName,
                    standardData, reportData);
        } else {
            // 陈列
            return convertToDisplayProjectAchievement(user,executionResult, standardObjectApiName,
                    standardData, reportData);
        }
    }
    /**
     * 计算陈列形式达成结果
     *
     * @param user 用户信息
     * @param sourceObjMap 源对象API名称映射到上报数据列表的Map
     * @param standardDataMap 标准API名称映射到标准数据列表的Map
     * @param groupDetailsMap 按陈列形式ID分组的达成详情Map
     * @return 陈列形式达成结果列表
     */
    public List<IObjectData> convertDisplayFormAchievements(
            User user,
            Map<String, Map<String, IObjectData>> sourceObjMap,
            Map<String, Map<String, IObjectData>> standardDataMap,
            Map<String, Map<String, GroupAchievementDetail>> groupDetailsMap) {
        List<IObjectData> displayFormAchievements = new ArrayList<>();
        Map<String, IObjectData> displayTypeStandards = standardDataMap.get(DisplayTypeStandardsFields.API_NAME);
        
        // 如果标准数据为空，直接返回空列表
        if (displayTypeStandards == null || displayTypeStandards.isEmpty()) {
//            log.warn("陈列形式标准数据为空，无法计算陈列形式达成结果");
            return displayFormAchievements;
        }

        // 已经处理过的形式id
        Set<String> processedDisplayFormIds = new HashSet<>();
        //额外的陈列形式图片
        Map<String, List<Object>/*图片*/> extraDisplayFormPhotos = new HashMap<>();
        //获取陈列形式上报明细 DisplayFormatDetailsObj
        if (MapUtils.isNotEmpty(sourceObjMap.get(DisplayFormatDetailsFields.API_NAME))) {
            sourceObjMap.get(DisplayFormatDetailsFields.API_NAME).values().forEach(standardData -> {
                //获取陈列形式id
                String displayFormId = standardData.get(DisplayFormatDetailsFields.DISPLAY_FORMAT,String.class);
                //获取照片
                List<Object> photos = standardData.get(DisplayFormatDetailsFields.PHOTO,List.class);
                //如果照片不为空，添加到额外的陈列形式图片中
                if (CollectionUtils.isNotEmpty(photos) && StringUtils.isNotBlank(displayFormId)) {
                    extraDisplayFormPhotos.computeIfAbsent(displayFormId, k -> new ArrayList<>()).addAll(photos);
                }
            });
        }

        // 处理每个陈列形式标准
        for (IObjectData displayTypeStandard : displayTypeStandards.values()) {
            // 获取陈列形式id
            String displayTypeId = (String) displayTypeStandard.get(DisplayTypeStandardsFields.DISPLAY_FORM);
            if (StringUtils.isBlank(displayTypeId) || processedDisplayFormIds.contains(displayTypeId)) {
                continue;
            }

            // 标记当前形式id为已处理
            processedDisplayFormIds.add(displayTypeId);

            // 获取当前陈列形式对应的规则组详情
            Map<String, GroupAchievementDetail> formGroupDetails = groupDetailsMap.getOrDefault(displayTypeId, new HashMap<>());
            // 生成陈列形式达成结果
            IObjectData formResult = calculateDisplayFormResultByGroupDetails(user, displayTypeId, displayTypeStandard, formGroupDetails,extraDisplayFormPhotos);
            if (formResult != null) {
                displayFormAchievements.add(formResult);
            }
        }

        return displayFormAchievements;
    }

    /**
     * 计算陈列形式达成结果
     *
     * @param user                   用户信息
     * @param displayFormId          陈列形式ID
     * @param displayTypeStandard    陈列形式标准数据
     * @param groupDetails           按规则组ID分组的达成详情Map
     * @param extraDisplayFormPhotos
     * @return 陈列形式达成结果
     */
    private IObjectData calculateDisplayFormResultByGroupDetails(
            User user,
            String displayFormId,
            IObjectData displayTypeStandard,
            Map<String, GroupAchievementDetail> groupDetails, Map<String, List<Object>> extraDisplayFormPhotos) {
        IObjectData result = dataReportResultDao.createBaseObjectData(user.getTenantId(), null, SummaryDisplayAchievementFields.API_NAME);
        
        // 设置基本信息
        result.set(BaseField.id.getApiName(), new ObjectId().toString());
        result.setDescribeApiName(SummaryDisplayAchievementFields.API_NAME);
        result.set(SummaryDisplayAchievementFields.RELATED_DISPLAY_ACHIEVEMENT, displayFormId);
        result.set(SummaryDisplayAchievementFields.REQUIRED_LOCATION,
                displayTypeStandard.get(DisplayTypeStandardsFields.DISPLAY_POSITION));
        result.set(SummaryDisplayAchievementFields.DEFINE_LAYER,
                displayTypeStandard.get(DisplayTypeStandardsFields.DEFINE_LAYER));
        result.set(SummaryDisplayAchievementFields.RULE_GROUP_ID,
                displayTypeStandard.get(DisplayTypeStandardsFields.SUCCESSFUL_STORE));
        result.set("standard_explanation",
                displayTypeStandard.get(DisplayTypeStandardsFields.EXPLANATION_PROJECT_STANDA));
        result.set("schematic_diagram",
                displayTypeStandard.get(DisplayTypeStandardsFields.STANDARD_SCHEMATIC_DIAGRAM));

        // 计算总体达成情况
        int totalGroupCount = groupDetails.size();
        int totalAchievedGroupCount = 0;
        int totalAchievedCount = 0;
        int totalPartialCount = 0;
        int productGroupCount = 0;
        int productAchievedGroupCount = 0;
        int materialGroupCount = 0;
        int materialAchievedGroupCount = 0;
        int projectGroupCount = 0;
        int projectAchievedGroupCount = 0;
        // 未陈列
        int notDisplayGroupCount = 0;

        List<Object> displayPhotos = new ArrayList<>();
        // 处理每个规则组的达成情况
        for (GroupAchievementDetail groupDetail : groupDetails.values()) {
            // 未陈列
            if (!groupDetail.isDisplayed()) {
                notDisplayGroupCount++;

            }
            // 产品达成情况
            if (groupDetail.getProductDetail() != null) {
                productGroupCount++;
                if (groupDetail.getProductDetail().isAchieved()) {
                    productAchievedGroupCount++;
                }
                totalAchievedCount += groupDetail.getProductDetail().getAchievedCount();
                totalPartialCount += groupDetail.getProductDetail().getPartialCount();
            }
            
            // 物料达成情况
            if (groupDetail.getMaterialDetail() != null) {
                materialGroupCount++;
                if (groupDetail.getMaterialDetail().isAchieved()) {
                    materialAchievedGroupCount++;
                }
                totalAchievedCount += groupDetail.getMaterialDetail().getAchievedCount();
                totalPartialCount += groupDetail.getMaterialDetail().getPartialCount();
            }
            
            // 项目达成情况
            if (groupDetail.getProjectDetail() != null) {
                projectGroupCount++;
                if (groupDetail.getProjectDetail().isAchieved()) {
                    projectAchievedGroupCount++;
                }
                totalAchievedCount += groupDetail.getProjectDetail().getAchievedCount();
                totalPartialCount += groupDetail.getProjectDetail().getPartialCount();
            }
            
            // 整体达成情况
            if (groupDetail.isAchieved()) {
                totalAchievedGroupCount++;
            }
            //null
            if (CollectionUtils.isEmpty(groupDetail.getDisplayPhotos())) {
                continue;
            }
            displayPhotos.addAll(groupDetail.getDisplayPhotos());
        }
        if (CollectionUtils.isNotEmpty(extraDisplayFormPhotos.get(displayFormId))) {
            displayPhotos.addAll(extraDisplayFormPhotos.get(displayFormId));
        }
        // 设置陈列照片
        result.set(SummaryDisplayAchievementFields.DISPLAY_PHOTO, getFilenameDistinctList(displayPhotos));
        // 计算总体达成率
        double achievementRate = totalGroupCount > 0 ? (double) totalAchievedGroupCount / totalGroupCount : 0;
        result.set("achievement_rate", achievementRate);

        // 设置达成状态
        String achievedStatus;
        if (notDisplayGroupCount >= totalGroupCount) {
            achievedStatus = SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_4; // 未陈列
            if (FmcgGray.Checkins.EI.isPartialDisplayedNotAchieved.gray(user.getTenantId())){
                achievedStatus = SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_3;
            }
        } else if (achievementRate >= 1.0) {
            achievedStatus = SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_1; // 达标
//        } else if (achievementRate > 0) {
        } else if (totalAchievedCount > 0 || totalPartialCount > 0) {
            achievedStatus = SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_2; // 部分达标
            if (FmcgGray.Checkins.EI.isPartialDisplayedNotAchieved.gray(user.getTenantId())){
                achievedStatus = SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_3;
            }
        } else {
            achievedStatus = SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_3; // 未达标
        }
        result.set(SummaryDisplayAchievementFields.ACHIEVED_RESULTS, achievedStatus);

        // 设置达成项和未达成项总结
        List<String> achievementSummary = new ArrayList<>();
        List<String> summaryIssues = new ArrayList<>();

        // 产品项目达成情况
        if (productGroupCount > 0) {
            if (productAchievedGroupCount >= productGroupCount) {
                achievementSummary.add(SummaryDisplayAchievementFields.ACHIEVEMENT_SUMMARY_Options_4); // 产品项目达标
            } else {
                summaryIssues.add(SummaryDisplayAchievementFields.SUMMARY_ISSUES_Options_1); // 产品项目未达标
            }
        }

        // 物料项目达成情况
        if (materialGroupCount > 0) {
            if (materialAchievedGroupCount >= materialGroupCount) {
                achievementSummary.add(SummaryDisplayAchievementFields.ACHIEVEMENT_SUMMARY_Options_3); // 物料项目达标
            } else {
                summaryIssues.add(SummaryDisplayAchievementFields.SUMMARY_ISSUES_Options_3); // 物料项目未达标
            }
        }

        // 项目达成情况
        if (projectGroupCount > 0) {
            if (projectAchievedGroupCount >= projectGroupCount) {
                achievementSummary.add(SummaryDisplayAchievementFields.ACHIEVEMENT_SUMMARY_Options_1); // 全部项目达标
            } else if (projectAchievedGroupCount > 0){
                achievementSummary.add(SummaryDisplayAchievementFields.ACHIEVEMENT_SUMMARY_Options_2); // 部分项目达标
            }
        }

        // 设置达成项和未达成项
        if (!achievementSummary.isEmpty()) {
            result.set(SummaryDisplayAchievementFields.ACHIEVEMENT_SUMMARY, achievementSummary);
        }
        if (!summaryIssues.isEmpty()) {
            result.set(SummaryDisplayAchievementFields.SUMMARY_ISSUES, summaryIssues);
        }

        return result;
    }

    /**
     * 计算陈列形式是否达成
     * 参考generateTotalResult中的任意达成逻辑
     *
     * @param standardGroups 按标准ID分组的成果
     * @return 是否达成
     */
    public boolean calculateFormAchieved(Map<String, List<IObjectData>> standardGroups) {
        // 如果没有标准组，返回未达成
        if (standardGroups.isEmpty()) {
            return false;
        }

        // 记录达成的标准组数量
        int totalGroupCount = standardGroups.size();
        int achievedGroupCount = 0;

        // 遍历每个标准组
        for (List<IObjectData> groupAchievements : standardGroups.values()) {
            if (CollectionUtils.isEmpty(groupAchievements)) {
                continue;
            }

            // 获取达成标准方式
            String waysAchieveStandard = null;
            if (groupAchievements.get(0).containsField(DisplayProjectAchievementFields.WAYS_ACHIEVE_STANDARD)) {
                waysAchieveStandard = String.valueOf(
                        groupAchievements.get(0).get(DisplayProjectAchievementFields.WAYS_ACHIEVE_STANDARD));
            }

            boolean isGroupAchieved = isGroupAchievedByStandard(
                    groupAchievements,
                    waysAchieveStandard,
                    DisplayProjectAchievementFields.ACHIEVED_RESULTS,
                    DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_1
            );

            if (isGroupAchieved) {
                achievedGroupCount++;
            }
        }

        // 计算总体达成率 - 如果所有组都达成，则整体达成
        return achievedGroupCount == totalGroupCount;
    }
    /**
     * 判断单个分组是否达成，根据达成标准方式确定判断逻辑
     *
     * @param groupAchievements 一个分组内的所有成果
     * @param waysAchieveStandard 达成标准方式
     * @param achievedResultsField 达成结果字段名
     * @param achievedResultsValue 达成结果值
     * @return 分组是否达成
     */
    public boolean isGroupAchievedByStandard(
            List<IObjectData> groupAchievements,
            String waysAchieveStandard,
            String achievedResultsField,
            String achievedResultsValue) {

        if (CollectionUtils.isEmpty(groupAchievements)) {
            return false;
        }

        // 根据达成标准方式判断组是否达成
        if (StringUtils.isBlank(waysAchieveStandard) ||
                DisplayProjectAchievementFields.WAYS_ACHIEVE_STANDARD_Options_1.equals(waysAchieveStandard)) {
            // 全都达标：所有项目都必须达成
            return groupAchievements.stream()
                    //parentId 为空的才算
                    .filter(o -> StringUtils.isBlank(o.get(DataReport2PublicFieldsConstants.CONDITION_PARENT_ID,String.class)))
                    .allMatch(a -> achievedResultsValue.equals(a.get(achievedResultsField)));
        } else if (DisplayProjectAchievementFields.WAYS_ACHIEVE_STANDARD_Options_2.equals(waysAchieveStandard)) {
            // 任一达标：只要有一个项目达成即可
            return groupAchievements.stream()
                    .filter(o->StringUtils.isBlank(o.get(DataReport2PublicFieldsConstants.CONDITION_PARENT_ID,String.class)))
                    .anyMatch(a -> achievedResultsValue.equals(a.get(achievedResultsField)));
        } else {
            // 默认为全都达标
            return groupAchievements.stream()
                    .filter(o->StringUtils.isBlank(o.get(DataReport2PublicFieldsConstants.CONDITION_PARENT_ID,String.class)))
                    .allMatch(a -> achievedResultsValue.equals(a.get(achievedResultsField)));
        }
    }


    /**
     * 转换为陈列项目达成结果
     * 原始方法，保持向后兼容性
     */
    private IObjectData convertToDisplayProjectAchievement(
            User user,
            RuleExecutionResult ruleExecutionResult, String standardApiName, IObjectData standardData,
            IObjectData reportData) {
        IObjectData achievement = dataReportResultDao.createBaseObjectData(user.getTenantId(),null,DisplayProjectAchievementFields.API_NAME);
        // 设置级别
        String standardLevel = getStringValue(standardData, DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL);
        if (DataReport2PublicFieldsConstants.LEVEL_Options_anyLevel.equals(standardLevel)) {
           achievement.set(DisplayProjectAchievementFields.LEVEL, getListObjIndexValue(reportData, DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL,0));
        }else{
            achievement.set(DisplayProjectAchievementFields.LEVEL,
                    standardLevel);
        }
        //关联详情标准id
        achievement.set(DataReport2PublicFieldsConstants.STANDARD_DETAIL_ID,standardData.getId());
        // 设置关联陈列
        achievement.set(DisplayProjectAchievementFields.RELATED_DISPLAY_ACHIEVEMENT,
                getStringValue(standardData,
                        DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT));

        // 设置达成结果
        // 增加未陈列 如果ruleExecutionResult里的filterLIst为空
        if (ruleExecutionResult.isSuccess()) {
            achievement.set(DisplayProjectAchievementFields.ACHIEVED_RESULTS, DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_1); // 达标
        } else if (!ruleExecutionResult.hasDisplay()) {
            achievement.set(DisplayProjectAchievementFields.ACHIEVED_RESULTS, DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_4); // 未陈列
            if (FmcgGray.Checkins.EI.isPartialDisplayedNotAchieved.gray(user.getTenantId())){
                achievement.set(DisplayProjectAchievementFields.ACHIEVED_RESULTS, DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_0); // 未陈列
            }
        } else if (ruleExecutionResult.hasSubConditions() && CollectionUtils.isNotEmpty(ruleExecutionResult.getFilteredDataList())) {
            achievement.set(DisplayProjectAchievementFields.ACHIEVED_RESULTS, DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_2); // 部分达标
            if (FmcgGray.Checkins.EI.isPartialDisplayedNotAchieved.gray(user.getTenantId())){
                achievement.set(DisplayProjectAchievementFields.ACHIEVED_RESULTS, DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_0); // 未达标
            }
        } else {
            achievement.set(DisplayProjectAchievementFields.ACHIEVED_RESULTS, DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_0); // 未达标
        }


        // 规则信息
        // apiName
        achievement.set(DisplayProjectAchievementFields.RULE_GROUP_APINAME,
                getStringValue(standardData, DataReport2PublicFieldsConstants.RULE_GROUP_DATA_DETAIL_APINAME));
        // dataId
        achievement.set(DisplayProjectAchievementFields.RULE_GROUP_ID,
                getStringValue(standardData, DataReport2PublicFieldsConstants.RULE_GROUP_DATA_DETAIL_ID));

        // 达标方式
        achievement.set(DisplayProjectAchievementFields.WAYS_ACHIEVE_STANDARD,
                getStringValue(standardData, DataReport2PublicFieldsConstants.ACHIEVEMENT_WAY));

        if (ProductItemStandardDetailFields.API_NAME.equals(standardApiName)) {
            setDisplayProductAchievement(ruleExecutionResult, standardData, reportData, achievement);
        } else if (MaterialStandardDetailsFields.API_NAME.equals(standardApiName)) {
            setDisplayMaterialAchievement(ruleExecutionResult, standardData, reportData, achievement);
        } else if (ProjectStandardsFields.API_NAME.equals(standardApiName)) {
            setDisplayProjectOverallAchievement(ruleExecutionResult, standardData, reportData, achievement);
        }

        // 数据上报项目字段
        String reportDataKey = standardData.get(DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_KEY, String.class);

        achievement.set(DisplayProjectAchievementFields.REQUIRED_PROJECT_QUANTITY,
                getDoubleValue(standardData, reportDataKey));
        achievement.set(DisplayProjectAchievementFields.ACTUAL_PROJECT_QUANTITY,
                getDoubleValue(reportData, reportDataKey));
        achievement.set(DisplayProjectAchievementFields.DIFFERENCE_QUANTITY, calculateDifference(
                getDoubleValue(achievement, DisplayProjectAchievementFields.REQUIRED_PROJECT_QUANTITY),
                getDoubleValue(achievement, DisplayProjectAchievementFields.ACTUAL_PROJECT_QUANTITY)));


        // 设置达成项和未达成项
        List<String> achievementSummary = new ArrayList<>();
        List<String> summaryIssues = new ArrayList<>();
        if (ruleExecutionResult.isSuccess()){
            achievementSummary.add(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_1);
        }
        //层数达标情况
        addLevelDone(ruleExecutionResult.isSuccess(), achievement, achievementSummary, summaryIssues);
        // //产品达标情况 要求产品不为空
        // if (achievement.get(DisplayProjectAchievementFields.REQUIRED_PRODUCTS) != null) {
        //     addProductDone( achievement.get(DisplayProjectAchievementFields.ABSENCE_PRODUCTS) == null, achievement, achievementSummary, summaryIssues);
        // }
        // //产品分类达标情况
        // if (achievement.get(DisplayProjectAchievementFields.REQUIRED_PRODUCTS_CLASSIFICATION) != null) {
        //     addProductClassificationDone(achievement.get(DisplayProjectAchievementFields.ABSENCE_PRODUCTS_CLASSIFICATION) == null, achievementSummary, summaryIssues);
        // }

        // 改成 产品和产品分类不为空
        if (achievement.get(DisplayProjectAchievementFields.REQUIRED_PRODUCTS) != null || achievement.get(DisplayProjectAchievementFields.REQUIRED_PRODUCTS_CLASSIFICATION) != null) {
            addProductDone(ruleExecutionResult.isSuccess(), achievement, achievementSummary, summaryIssues);
        }
        // //物料达标情况 要求物料不为空
        // if (achievement.get(DisplayProjectAchievementFields.REQUIRED_MATERIALS) != null) {
        //     addMatDone(achievement.get(DisplayProjectAchievementFields.ABSENCE_MATERIALS) == null, achievementSummary, summaryIssues);
        // }
        // //物料分类达标情况
        // if (achievement.get(DisplayProjectAchievementFields.REQUIRED_MATERIALS_CLASSIFICATION) != null) {
        //     addMatClassificationDone(achievement.get(DisplayProjectAchievementFields.ABSENCE_MATERIALS_CLASSIFICATION) == null, achievementSummary, summaryIssues);
        // }

        // 改成 物料和物料分类不为空
        if (achievement.get(DisplayProjectAchievementFields.REQUIRED_MATERIALS) != null || achievement.get(DisplayProjectAchievementFields.REQUIRED_MATERIALS_CLASSIFICATION) != null) {
            addMatDone(ruleExecutionResult.isSuccess(), achievementSummary, summaryIssues);
        }


        //项目数达标 如果有差异值且目标值不= 0 才 计算达成 差异值>=0 达成
        if ((getDoubleValue(achievement, DisplayProjectAchievementFields.REQUIRED_PROJECT_QUANTITY) > 0 || getDoubleValue(achievement, DisplayProjectAchievementFields.ACTUAL_PROJECT_QUANTITY) > 0) && achievement.get(DisplayProjectAchievementFields.DIFFERENCE_QUANTITY) != null ) {
            addItemQuantityDone(getDoubleValue(achievement, DisplayProjectAchievementFields.DIFFERENCE_QUANTITY) >= 0, achievement, achievementSummary, summaryIssues);
        }
        // 设置达成/未达成项
        if (!achievementSummary.isEmpty()) {
            achievement.set(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY, achievementSummary);
        }
        if (!summaryIssues.isEmpty()) {
            achievement.set(DisplayProjectAchievementFields.SUMMARY_ISSUES, summaryIssues);
        }
        // 陈列照
        achievement.set(DataReport2PublicFieldsConstants.DisplayTypeStandardsFields_Virtual_DISPLAY_PHOTO,getDistinctDisplayPhotos(ruleExecutionResult.getFilteredDataList()));
        return achievement;
    }

    /**
     * 设置整体项目结果
     */
    private void setDisplayProjectOverallAchievement(RuleExecutionResult ruleExecutionResult,
            IObjectData standardData, IObjectData reportData, IObjectData achievement) {
        // 设置项目名称
        achievement.set(DisplayProjectAchievementFields.PROJECT_NAME,
                getStringValue(standardData, ProjectStandardsFields.DISPLAY_PROJECT));
        // 设置类型为整体陈列
        achievement.set(DisplayProjectAchievementFields.SETTING_TYPE,
                DisplayProjectAchievementFields.SETTING_TYPE_Options_1);
    }

    private void addItemQuantityDone(boolean isSuccess, IObjectData achievement, List<String> achievementSummary, List<String> summaryIssues) {
//        if (isSuccess) {
//            achievementSummary.add(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_6);
//        }else{
//            summaryIssues.add(DisplayProjectAchievementFields.SUMMARY_ISSUES_Options_3);
//        }
    }

    private void addMatClassificationDone(boolean isSuccess, List<String> achievementSummary, List<String> summaryIssues) {
        //TODO 物料分类达标情况
        // if (isSuccess) {
        //     achievementSummary.add(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_5);
        // } else {
        //     summaryIssues.add(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_5);
        // }
    }

    private void addProductClassificationDone(boolean isSuccess, List<String> achievementSummary, List<String> summaryIssues) {
      //TODO 产品分类达标情况
        // if (isSuccess) {
        //     achievementSummary.add(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_5);
        // } else {
        //     summaryIssues.add(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_5);
        // }
    }

    /**
     * 转换为产品维度的陈列项目达成结果
     */
    private IObjectData setDisplayProductAchievement(
            RuleExecutionResult ruleExecutionResult, IObjectData standardData, IObjectData reportData,
            IObjectData achievement) {
        // 设置项目名称
        achievement.set(DisplayProjectAchievementFields.PROJECT_NAME,
                getStringValue(standardData, ProductItemStandardDetailFields.DISPLAY_PROJECT));
        // 设置类型为产品陈列
        achievement.set(DisplayProjectAchievementFields.SETTING_TYPE,
                DisplayProjectAchievementFields.SETTING_TYPE_Options_2);
        // 设置实际产品和要求产品
        List<Object> requiredProducts = getListObjValue(standardData, ProductItemStandardDetailFields.PRODUCT_NAME);

        // 要求产品
        achievement.set(DisplayProjectAchievementFields.REQUIRED_PRODUCTS, requiredProducts);

        // 实际产品
        List<Object> actualProducts = getListObjValue(reportData, ProductItemStandardDetailFields.PRODUCT_NAME);
        achievement.set(DisplayProjectAchievementFields.ACTUAL_PRODUCTS, actualProducts);

        // 计算缺少产品
        achievement.set(DisplayProjectAchievementFields.ABSENCE_PRODUCTS, calculateAbsenceList(
                requiredProducts, actualProducts));

        // 设置实际产品分类和要求产品分类
        List<Object> requiredProductsClassification = getListObjValue(standardData,
                ProductItemStandardDetailFields.PRODUCT_CATEGORIZATION);
        List<Object> actualProductsClassification = getListObjValue(reportData,
                ProductItemStandardDetailFields.PRODUCT_CATEGORIZATION);
        achievement.set(DisplayProjectAchievementFields.REQUIRED_PRODUCTS_CLASSIFICATION,
                requiredProductsClassification);
        achievement.set(DisplayProjectAchievementFields.ACTUAL_PRODUCTS_CLASSIFICATION,
                actualProductsClassification);
        achievement.set(DisplayProjectAchievementFields.ABSENCE_PRODUCTS_CLASSIFICATION, calculateAbsenceList(
                requiredProductsClassification, actualProductsClassification));

        // 产品标准ID
        achievement.set(DisplayProjectAchievementFields.PRODUCT_STANDARDS_ID,
                getStringValue(standardData, ProductItemStandardDetailFields.PRODUCT_ITEM));
        return achievement;
    }

    private void addProductDone(boolean isSuccess, IObjectData achievement, List<String> achievementSummary, List<String> summaryIssues) {
        if (isSuccess) {
            achievementSummary.add(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_4);
        }
    }

    /**
     * 转换为物料维度的陈列项目达成结果
     */
    private IObjectData setDisplayMaterialAchievement(
            RuleExecutionResult ruleExecutionResult, IObjectData standardData, IObjectData reportData,
            IObjectData achievement) {

        // 设置项目名称
        achievement.set(DisplayProjectAchievementFields.PROJECT_NAME,
                getStringValue(standardData, MaterialStandardDetailsFields.DISPLAY_PROJECT));

        // 设置类型为物料陈列
        achievement.set(DisplayProjectAchievementFields.SETTING_TYPE,
                DisplayProjectAchievementFields.SETTING_TYPE_Options_3);
        // 设置实际物料和要求物料
        List<Object> requiredMaterials = getListObjValue(standardData, MaterialStandardDetailsFields.MATERIAL_NAMES);
        if (requiredMaterials != null && !requiredMaterials.isEmpty()) {
            // 要求物料
            achievement.set(DisplayProjectAchievementFields.REQUIRED_MATERIALS, requiredMaterials);

            // 实际物料
            List<Object> actualMaterials = getListObjValue(reportData, MaterialStandardDetailsFields.MATERIAL_NAMES);
            achievement.set(DisplayProjectAchievementFields.ACTUAL_MATERIALS, actualMaterials);

            // 计算缺少物料
            achievement.set(DisplayProjectAchievementFields.ABSENCE_MATERIALS, calculateAbsenceList(
                    requiredMaterials, actualMaterials));

            // 设置物料分类
            achievement.set(DisplayProjectAchievementFields.REQUIRED_MATERIALS_CLASSIFICATION,
                    getListObjValue(standardData, MaterialStandardDetailsFields.MATERIAL_CATEGORY).stream().map(o->o.toString()).collect(Collectors.joining(",")));
            achievement.set(DisplayProjectAchievementFields.ACTUAL_MATERIALS_CLASSIFICATION,
                    getListObjValue(reportData, MaterialStandardDetailsFields.MATERIAL_CATEGORY).stream().map(o->o.toString()).collect(Collectors.joining(",")));
            achievement.set(DisplayProjectAchievementFields.ABSENCE_MATERIALS_CLASSIFICATION, calculateAbsenceList(
                    getListObjValue(standardData, MaterialStandardDetailsFields.MATERIAL_CATEGORY),
                    getListObjValue(reportData, MaterialStandardDetailsFields.MATERIAL_CATEGORY)).stream().map(o->o.toString()).collect(Collectors.joining(",")));

            // 物料标准ID
            achievement.set(DisplayProjectAchievementFields.MATERIAL_STANDARDS_ID,
                    getStringValue(standardData, MaterialStandardDetailsFields.MATERIAL_STANDARD));
        }
        return achievement;
    }

    private void addMatDone(boolean isSuccess, List<String> achievementSummary, List<String> summaryIssues) {
        // 设置达成/未达成项
        if (isSuccess) {
            // 有物料达标
            achievementSummary.add(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_3);
        } else {
            // 有物料未达标
            summaryIssues.add(DisplayProjectAchievementFields.SUMMARY_ISSUES_Options_4);
        }
    }

    private void addLevelDone(  boolean isSuccess, IObjectData achievement, List<String> achievementSummary, List<String> summaryIssues) {
//        if (isSuccess) {
//            if (achievement.get(DisplayProjectAchievementFields.LEVEL) != null
//                    && !DisplayProjectAchievementFields.LEVEL_Options_0
//                            .equals(achievement.get(DisplayProjectAchievementFields.LEVEL))) {
//                achievementSummary.add(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_7);
//            }
//        } else {
//            if (achievement.get(DisplayProjectAchievementFields.LEVEL) != null
//                    && !DisplayProjectAchievementFields.LEVEL_Options_0
//                            .equals(achievement.get(DisplayProjectAchievementFields.LEVEL,String.class))) {
//                summaryIssues.add(DisplayProjectAchievementFields.SUMMARY_ISSUES_Options_2);
//            }
//        }

        if (isSuccess) {
            if (achievement.get(DisplayProjectAchievementFields.LEVEL) != null
                    && DataReport2PublicFieldsConstants.LEVEL_Options_anyLevel
                    .equals(achievement.get(DisplayProjectAchievementFields.LEVEL))) {
                achievementSummary.add(DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_7);
            }
        } else {
            if (achievement.get(DisplayProjectAchievementFields.LEVEL) != null
                    && DataReport2PublicFieldsConstants.LEVEL_Options_anyLevel
                    .equals(achievement.get(DisplayProjectAchievementFields.LEVEL,String.class))) {
                summaryIssues.add(DisplayProjectAchievementFields.SUMMARY_ISSUES_Options_2);
            }
        }
    }


    // 工具方法
//
//    private String determineAchievementSummary(RuleExecutionResult executionResult) {
//        if (executionResult.isSuccess()) {
//            return DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_1; // 达标
//        }
//        // 根据条件结果判断部分达标情况
//        long successCount = 0;
//        if (executionResult.getConditionResults() != null) {
//            successCount = executionResult.getConditionResults().stream()
//                    .filter(ConditionExecutionResult::isSuccess)
//                    .count();
//        }
//        return successCount > 0
//                ? DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_2 // 部分达标
//                : DisplayProjectAchievementFields.ACHIEVEMENT_SUMMARY_Options_other; // 其他
//    }

//    private List<String> determineSummaryIssues(RuleExecutionResult executionResult) {
//        if (executionResult.getConditionResults() == null) {
//            return new ArrayList<>();
//        }
//        return executionResult.getConditionResults().stream()
//                .filter(r -> !r.isSuccess())
//                .map(this::mapConditionToSummaryIssue)
//                .filter(Objects::nonNull)
//                .collect(Collectors.toList());
//    }

//    private String mapConditionToSummaryIssue(ConditionExecutionResult result) {
//        // 根据条件名称映射到对应的问题类型
//        String conditionName = result.getConditionName();
//        if ("SKU数量".equals(conditionName)) {
//            return DisplayProjectAchievementFields.SUMMARY_ISSUES_Options_1;
//        } else if ("层数".equals(conditionName)) {
//            return DisplayProjectAchievementFields.SUMMARY_ISSUES_Options_2;
//        } else if ("排面数".equals(conditionName)) {
//            return DisplayProjectAchievementFields.SUMMARY_ISSUES_Options_3;
//        } else if ("物料".equals(conditionName)) {
//            return DisplayProjectAchievementFields.SUMMARY_ISSUES_Options_4;
//        } else {
//            return DisplayProjectAchievementFields.SUMMARY_ISSUES_Options_other;
//        }
//    }
    private Object getListObjIndexValue(IObjectData data, String key, int index) {
        List<Object> listObjValue = getListObjValue(data, key);
        if(CollectionUtils.isEmpty(listObjValue) || listObjValue.size()<=index){
            return null;
        }
        return listObjValue.get(index);
    }
    @SuppressWarnings("unchecked")
    private List<Object> getListObjValue(IObjectData data, String key) {
        if (data == null || key == null) {
            return new ArrayList<>();
        }

        Object value = data.get(key);
        List<Object> result = new ArrayList<>();

        if (value == null) {
            return result;
        }

        // 递归处理嵌套数组
        addToObjResultList(value, result);

        // 去重
        return result.stream().distinct().collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    private void addToObjResultList(Object value, List<Object> result) {
        if (value instanceof List) {
            // 处理List类型，可能是多维数组
            List<Object> listValue = (List<Object>) value;
            for (Object item : listValue) {
                // 递归处理每个元素
                addToObjResultList(item, result);
            }
        } else if (value instanceof String) {
            // 尝试通过fastjson解析字符串为数组
            try {
                String strValue = (String) value;
                if (strValue.startsWith("[") && strValue.endsWith("]")) {
                    List<Object> parsedList = com.alibaba.fastjson.JSON.parseArray(strValue);
                    for (Object item : parsedList) {
                        // 递归处理解析后的每个元素
                        addToObjResultList(item, result);
                    }
                } else {
                    // 单个字符串值，添加到结果中
                    result.add(value);
                }
            } catch (Exception e) {
                // 解析失败，将原始字符串添加到结果中
                result.add(value);
            }
        } else if (value != null) {
            // 其他类型转为字符串
            result.add(value);
        }
    }

    private String getStringValue(IObjectData data, String key) {
        if (data == null || key == null) {
            return null;
        }
        Object value = data.get(key);
        return value != null ? value.toString() : null;
    }

    private double getDoubleValue(IObjectData data, String key) {
        if (data == null || key == null) {
            return 0.0; // Return 0.0 for invalid inputs
        }
        Object value = data.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue(); // Return as double
        } else if (value instanceof String) {
            try {
                return Double.parseDouble((String) value); // Parse string to double
            } catch (NumberFormatException e) {
                log.warn("无法将字符串转换为双精度浮点数: {}", value);
            }
        }
        return 0.0; // Return 0.0 if conversion is not possible
    }

    private List<Object> calculateAbsenceList(List<Object> required, List<Object> actual) {
        if (required == null) {
            return new ArrayList<>();
        }
        if (actual == null) {
            return new ArrayList<>(required);
        }
        return required.stream()
                .filter(item -> !actual.contains(item))
                .collect(Collectors.toList());
    }

    private Double calculateDifference(Double required, Double actual) {
        if (required == null || actual == null) {
            return null;
        }
        return  actual - required;
    }

    private Integer calculateAchievedQuantity(List<Object> required, List<Object> actual) {
        if (required == null || actual == null) {
            return 0;
        }
        return (int) required.stream()
                .filter(actual::contains)
                .count();
    }

    /**
     * 转换为铺货产品达成结果
     * 针对必须铺货产品的标准评估结果转换
     */
    private IObjectData convertToMustDistributeProductsAchievement(
            User user,
            RuleExecutionResult ruleExecutionResult, String standardObjectApiName,
            IObjectData standardData, IObjectData reportData) {

        IObjectData achievement = dataReportResultDao.createBaseObjectData(user.getTenantId(),null,DistributionProductsAchievedFields.API_NAME);
        // 设置分销状态
        achievement.set(DistributionProductsAchievedFields.STATUS, ruleExecutionResult.isSuccess()
                ? DistributionProductsAchievedFields.STATUS_Options_1 // 已分销
                : DistributionProductsAchievedFields.STATUS_Options_0); // 未分销
        //关联详情标准id
        achievement.set(DataReport2PublicFieldsConstants.STANDARD_DETAIL_ID,standardData.getId());
        // 设置标准字段
        achievement.set(DistributionProductsAchievedFields.DISPLAY_FORMAT,
                getStringValue(standardData, MustDistributeProductsFields.DISPLAY_FORMAT));
        achievement.set(DistributionProductsAchievedFields.PRODUCT_CATEGORY,
                getStringValue(standardData, MustDistributeProductsFields.PRODUCT_CATEGORY));
        //产品分类多选
        List<Object> productCategories = getListObjValue(standardData, MustDistributeProductsFields.PRODUCT_CATEGORIES);
        //加入单选
        if (CollectionUtils.isEmpty(productCategories)) {
            productCategories = new ArrayList<>();
            if (StringUtils.isNotBlank(getStringValue(standardData, MustDistributeProductsFields.PRODUCT_CATEGORY))) {
                productCategories.add(getStringValue(standardData, MustDistributeProductsFields.PRODUCT_CATEGORY));
            }
        } else {
            if (StringUtils.isNotBlank(getStringValue(standardData, MustDistributeProductsFields.PRODUCT_CATEGORY)) &&
                    !productCategories.contains(getStringValue(standardData, MustDistributeProductsFields.PRODUCT_CATEGORY))) {
                productCategories.add(getStringValue(standardData, MustDistributeProductsFields.PRODUCT_CATEGORY));
            }
        }
        achievement.set(DistributionProductsAchievedFields.PRODUCT_CATEGORIES, productCategories);

        achievement.set(DistributionProductsAchievedFields.RELATED_STANDARD,
                getStringValue(standardData, BaseField.id.getApiName()));

        // 设置产品范围
        // 要求的产品范围
        List<Object> requiredProducts = new ArrayList<>();
        List<Object> productMultiple = getListObjValue(standardData, MustDistributeProductsFields.PRODUCT_MULTIPLE);
        if (productMultiple != null && !productMultiple.isEmpty()) {
            requiredProducts.addAll(productMultiple);
        }
        String singleProduct = getStringValue(standardData, MustDistributeProductsFields.PRODUCT);
        if (singleProduct != null && !singleProduct.isEmpty()) {
            requiredProducts.add(singleProduct);
        }
        achievement.set(DistributionProductsAchievedFields.REQUIRED_STANDARD, requiredProducts);

        // 获取实际产品 - 同样可能来自多选或单选字段
        Set<Object> actualProducts = Sets.newHashSet();
        List<Object> reportProductMultiple = getListObjValue(reportData, MustDistributeProductsFields.PRODUCT_MULTIPLE);
        if (reportProductMultiple != null && !reportProductMultiple.isEmpty()) {
            actualProducts.addAll(reportProductMultiple);
        }
        List<Object> reportSingleProduct = getListObjValue(reportData, MustDistributeProductsFields.PRODUCT);
        if (CollectionUtils.isNotEmpty(reportSingleProduct)) {
            actualProducts.addAll(reportSingleProduct);
        }
        achievement.set(DistributionProductsAchievedFields.ACTUAL_STANDARD, Lists.newArrayList(actualProducts));
        //标准值
        achievement.set(DistributionProductsAchievedFields.STANDARD_VALUE,
                getStringValue(standardData, MustDistributeProductsFields.MIN_REPORT_PRODUCT));
        // 计算达成数量
        int achievedQuantity = calculateAchievedQuantity(getListObjValue(achievement, DistributionProductsAchievedFields.REQUIRED_STANDARD), getListObjValue(achievement, DistributionProductsAchievedFields.ACTUAL_STANDARD));
        achievement.set(DistributionProductsAchievedFields.QUANTITY_PRODUCTS_ACHIEVED, achievedQuantity);

        // 添加最低上报产品数量要求（如果有）
        Double minProductCount = getDoubleValue(standardData, MustDistributeProductsFields.MIN_REPORT_PRODUCT);
        if (minProductCount != null) {
            achievement.set("minReportProducts", minProductCount);
            // 检查是否满足最低上报数量要求
            if (actualProducts != null) {
                achievement.set("meetsMinReportRequirement", actualProducts.size() >= minProductCount);
            } else {
                achievement.set("meetsMinReportRequirement", false);
            }
        }
        achievement.set(DataReport2PublicFieldsConstants.DisplayTypeStandardsFields_Virtual_DISPLAY_PHOTO,getDistinctDisplayPhotos(Lists.newArrayList(reportData)));

        return achievement;
    }

    private List<Object> getDistinctDisplayPhotos(List<IObjectData> objectDataList) {
        // 陈列照
        List<Object> photoObjList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(objectDataList)) {
            return photoObjList;
        }
        for (IObjectData objectData : objectDataList) {
            //null
            if (objectData == null) {
                continue;
            }
            //没有陈列照
            if (!objectData.containsField(DataReport2PublicFieldsConstants.DisplayTypeStandardsFields_Virtual_DISPLAY_PHOTO) || objectData.get(DataReport2PublicFieldsConstants.DisplayTypeStandardsFields_Virtual_DISPLAY_PHOTO) == null) {
                continue;
            }
            photoObjList.addAll(getListObjValue(objectData,DataReport2PublicFieldsConstants.DisplayTypeStandardsFields_Virtual_DISPLAY_PHOTO));
        }
        //照片去重
        if (photoObjList != null && !photoObjList.isEmpty()) {
            return getFilenameDistinctList(photoObjList);
        }
        return null;
    }

    @NotNull
    private List<Object> getFilenameDistinctList(List<Object> photoObjList) {
        Map<String, Object> uniquePhotoMap = new LinkedHashMap<>();  // 改为LinkedHashMap以保持插入顺序
        photoObjList = photoObjList.stream().distinct().collect(Collectors.toList());
        for (int i = 0; i < photoObjList.size(); i++) {
            Object photo = photoObjList.get(i);
            Object eval = JSONPath.eval(photo, "$.filename");
            if (eval == null) {
                continue;
            }
            String path = eval.toString();
            uniquePhotoMap.put(path, photo);
        }
        return new ArrayList<>(uniquePhotoMap.values());
    }

    public IObjectData convertToDisplayFromAchievementByActivityItem(User user, IObjectData data, Map<String, Map<String, GroupAchievementDetail>> formIdAndGroupIdDetailsMap) {
        // 获取陈列形式ID
        String displayFormId = Optional.ofNullable(data.get(TPMActivityProofDisplayImgFields.DISPLAY_FORM_ID))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .orElse(null);
        if (StringUtils.isBlank(displayFormId)) {
            return null;
        }

        // 获取当前陈列形式对应的规则组详情
        Map<String, GroupAchievementDetail> formGroupDetails = formIdAndGroupIdDetailsMap.getOrDefault(displayFormId, new HashMap<>());

        // 创建陈列形式达成结果对象
        IObjectData result = dataReportResultDao.createBaseObjectData(user.getTenantId(), null, SummaryDisplayAchievementFields.API_NAME);
        
        // 设置基本信息
        result.set(BaseField.id.getApiName(), new ObjectId().toString());
        result.setDescribeApiName(SummaryDisplayAchievementFields.API_NAME);
        result.set(SummaryDisplayAchievementFields.RELATED_DISPLAY_ACHIEVEMENT, displayFormId);


        // 计算总体达成情况
        int totalGroupCount = formGroupDetails.size();
        int totalAchievedGroupCount = 0;
        int productGroupCount = 0;
        int productAchievedGroupCount = 0;
        int materialGroupCount = 0;
        int materialAchievedGroupCount = 0;
        int projectGroupCount = 0;
        int projectAchievedGroupCount = 0;
        // 未陈列
        int notDisplayGroupCount = 0;

        List<Object> displayPhotos = new ArrayList<>();
        // 处理每个规则组的达成情况
        for (GroupAchievementDetail groupDetail : formGroupDetails.values()) {
            // 未陈列
            if (!groupDetail.isDisplayed()) {
                notDisplayGroupCount++;
            }
            // 产品达成情况
            if (groupDetail.getProductDetail() != null) {
                productGroupCount++;
                if (groupDetail.getProductDetail().isAchieved()) {
                    productAchievedGroupCount++;
                }
            }
            
            // 物料达成情况
            if (groupDetail.getMaterialDetail() != null) {
                materialGroupCount++;
                if (groupDetail.getMaterialDetail().isAchieved()) {
                    materialAchievedGroupCount++;
                }
            }
            
            // 项目达成情况
            if (groupDetail.getProjectDetail() != null) {
                projectGroupCount++;
                if (groupDetail.getProjectDetail().isAchieved()) {
                    projectAchievedGroupCount++;
                }
            }
            
            // 整体达成情况
            if (groupDetail.isAchieved()) {
                totalAchievedGroupCount++;
            }

            // 收集陈列照片
            if (CollectionUtils.isNotEmpty(groupDetail.getDisplayPhotos())) {
                displayPhotos.addAll(groupDetail.getDisplayPhotos());
            }
        }

        // 设置陈列照片
        result.set(SummaryDisplayAchievementFields.DISPLAY_PHOTO, data.get(TPMActivityProofDisplayImgFields.IMAGE));


        // 设置达成状态
        String achievedStatus;
        //取 data 里的达成状态
        achievedStatus = data.get(TPMActivityProofDisplayImgFields.SYSTEM_JUDGMENT_STATUS,String.class);
        if (StringUtils.isBlank(achievedStatus)) {
            achievedStatus = SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_4; // 未陈列
            if (FmcgGray.Checkins.EI.isPartialDisplayedNotAchieved.gray(user.getTenantId())){
                achievedStatus = SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_3;
            }
        }else if(TPMActivityProofDisplayImgFields.SYSTEM_JUDGMENT_STATUS_Options_pass.equals(achievedStatus)){
            achievedStatus = SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_1; // 达标
        }else if(TPMActivityProofDisplayImgFields.SYSTEM_JUDGMENT_STATUS_Options_partial_pass.equals(achievedStatus)){
            achievedStatus = SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_2; // 部分达标
            if (FmcgGray.Checkins.EI.isPartialDisplayedNotAchieved.gray(user.getTenantId())){
                achievedStatus = SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_3;
            }
        }else if(TPMActivityProofDisplayImgFields.SYSTEM_JUDGMENT_STATUS_Options_fail.equals(achievedStatus)){
            achievedStatus = SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_3; // 未达标
        }else if(TPMActivityProofDisplayImgFields.SYSTEM_JUDGMENT_STATUS_Options_pending_approval.equals(achievedStatus)){
            achievedStatus = SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_5;//待审核
        }else{
            achievedStatus = SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_4; // 未陈列
            if (FmcgGray.Checkins.EI.isPartialDisplayedNotAchieved.gray(user.getTenantId())){
                achievedStatus = SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_3;
            }
        }
        result.set(SummaryDisplayAchievementFields.ACHIEVED_RESULTS, achievedStatus);

        // 设置达成项和未达成项总结
        List<String> achievementSummary = new ArrayList<>();
        List<String> summaryIssues = new ArrayList<>();

        // 产品项目达成情况
        if (productGroupCount > 0) {
            if (productAchievedGroupCount >= productGroupCount) {
                achievementSummary.add(SummaryDisplayAchievementFields.ACHIEVEMENT_SUMMARY_Options_4); // 产品项目达标
            } else {
                summaryIssues.add(SummaryDisplayAchievementFields.SUMMARY_ISSUES_Options_1); // 产品项目未达标
            }
        }

        // 物料项目达成情况
        if (materialGroupCount > 0) {
            if (materialAchievedGroupCount >= materialGroupCount) {
                achievementSummary.add(SummaryDisplayAchievementFields.ACHIEVEMENT_SUMMARY_Options_3); // 物料项目达标
            } else {
                summaryIssues.add(SummaryDisplayAchievementFields.SUMMARY_ISSUES_Options_3); // 物料项目未达标
            }
        }

        // 项目达成情况
        if (projectGroupCount > 0) {
            if (projectAchievedGroupCount >= projectGroupCount) {
                achievementSummary.add(SummaryDisplayAchievementFields.ACHIEVEMENT_SUMMARY_Options_1); // 全部项目达标
            } else if (projectAchievedGroupCount > 0) {
                achievementSummary.add(SummaryDisplayAchievementFields.ACHIEVEMENT_SUMMARY_Options_2); // 部分项目达标
            }
        }

        // 设置达成项和未达成项
        if (!achievementSummary.isEmpty()) {
            result.set(SummaryDisplayAchievementFields.ACHIEVEMENT_SUMMARY, achievementSummary);
        }
        if (!summaryIssues.isEmpty()) {
            result.set(SummaryDisplayAchievementFields.SUMMARY_ISSUES, summaryIssues);
        }

        return result;
    }

    /**
     * 项目达成详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    private static class ProjectAchievementDetail implements Serializable {
        private List<IObjectData> achievements;
        private boolean isAchieved;
        private boolean isDisplayed;
        private int totalCount;
        private int achievedCount;
        /**
         * 部分完成数量
         */
        private int partialCount;
//        private double achievementRate;
        private boolean isPendingAudit;
        /**
         * 是否部分完成
         */
        public boolean isPartial() {
            return partialCount > 0 || achievedCount > 0;
        }
    }

    /**
     * 项目组达成详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GroupAchievementDetail implements Serializable{
        private String ruleGroupId;
        private String displayFormId;
        private boolean isAchieved;
        private boolean isDisplayed;
        //待审核
        private boolean isPendingAudit;
        private ProjectAchievementDetail productDetail;
        private ProjectAchievementDetail materialDetail;
        private ProjectAchievementDetail projectDetail;
//        private double achievementRate;
        private int totalCount;
        private int achievedCount;
        private int partialCount;
        //去重后的陈列照
        private List<Object> displayPhotos;
        public boolean isPartial(){
            return partialCount > 0 || achievedCount > 0;
        }
    }


    /**
     * 计算达成详情
     */
    private ProjectAchievementDetail calculateAchievementDetail(List<IObjectData> achievements) {
        if (CollectionUtils.isEmpty(achievements)) {
            return null;
        }

        int totalCount = achievements.size();
        int achievedCount = 0;
        boolean isDisplayed = true;
        boolean isAchieved = true;

        // 获取达成标准方式
        String waysAchieveStandard = null;
        if (achievements.get(0).containsField(DisplayProjectAchievementFields.WAYS_ACHIEVE_STANDARD)) {
            waysAchieveStandard = String.valueOf(
                    achievements.get(0).get(DisplayProjectAchievementFields.WAYS_ACHIEVE_STANDARD));
        }

        // 检查是否未陈列
        isDisplayed = !isGroupAchievedByStandard(achievements, DisplayProjectAchievementFields.WAYS_ACHIEVE_STANDARD_Options_1, DisplayProjectAchievementFields.ACHIEVED_RESULTS, DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_4);

        // 检查是否达成
        isAchieved = isGroupAchievedByStandard(
                achievements,
                waysAchieveStandard,
                DisplayProjectAchievementFields.ACHIEVED_RESULTS,
                DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_1
        );

        // 计算达成数量
        achievedCount = (int) achievements.stream()
                .filter(a -> DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_1.equals(
                        a.get(DisplayProjectAchievementFields.ACHIEVED_RESULTS)))
                .count();
        
        //部分完成数量
        int partialCount = (int) achievements.stream()
                .filter(a -> DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_2.equals(
                        a.get(DisplayProjectAchievementFields.ACHIEVED_RESULTS)))
                .count();
//        double achievementRate = totalCount > 0 ? (double) achievedCount / totalCount : 0;

        try {
            return ProjectAchievementDetail.builder()
                    .achievements(achievements)
                    .isAchieved(isAchieved)
                    .isDisplayed(isDisplayed)
                    .totalCount(totalCount)
                    .achievedCount(achievedCount)
                    .partialCount(partialCount)
//                    .achievementRate(achievementRate)
                    .build();
        } catch (Exception e) {
            log.error("Failed to create achievement detail", e);
            return null;
        }
    }

    /**
     * 按照规则组ID对项目结果进行分组，并计算每个组的达成详情
     *
     * @param projectAchievements 所有项目结果列表
     * @return 按规则组ID分组的达成详情Map
     */
    public Map<String, Map<String, GroupAchievementDetail>> groupProjectAchievements(List<IObjectData> projectAchievements) {
        if (CollectionUtils.isEmpty(projectAchievements)) {
            return new HashMap<>();
        }

        // 按规则组ID分组
        Map<String, List<IObjectData>> groupMap = projectAchievements.stream()
                .collect(Collectors.groupingBy(achievement ->
                        String.valueOf(achievement.get(DisplayProjectAchievementFields.RULE_GROUP_ID))));
        //把整体里有爹的数据列举出来 后续需要合并结果
        List<IObjectData> hasParentOverallAchievements = projectAchievements.stream()
                .filter(achievement -> achievement.containsField(DisplayProjectAchievementFields.CONDITION_PARENT_ID) && StringUtils.isNotBlank(achievement.get(DisplayProjectAchievementFields.CONDITION_PARENT_ID, String.class))
                        && DisplayProjectAchievementFields.SETTING_TYPE_Options_1.equals(achievement.get(DisplayProjectAchievementFields.SETTING_TYPE)))
                .collect(Collectors.toList());
        // projectAchievements 分组 以id
        Map<String, IObjectData> projectAchievementsMap = projectAchievements.stream()
                .collect(Collectors.toMap(k -> k.getId(), v -> v, (v1, v2) -> v1));
        //有爹的 rulegroupId 和其爹的rulegroupId
        Map<String, String> overallHasParentRuleGroupIdAndParentId = new HashMap<>();
        for (IObjectData achievement : hasParentOverallAchievements) {
            String ruleGroupId = String.valueOf(achievement.get(DisplayProjectAchievementFields.RULE_GROUP_ID));
            String parentProjectAchievementId = String.valueOf(achievement.get(DisplayProjectAchievementFields.CONDITION_PARENT_ID));
            IObjectData parentProjectAchievement = projectAchievementsMap.get(parentProjectAchievementId);
            String parentRuleGroupId = String.valueOf(parentProjectAchievement.get(DisplayProjectAchievementFields.RULE_GROUP_ID));
            overallHasParentRuleGroupIdAndParentId.put(ruleGroupId, parentRuleGroupId);
        }


        Map<String, Map<String, GroupAchievementDetail>> resultMap = new HashMap<>();
        Map<String,GroupAchievementDetail> groupAchievementDetailMap = new HashMap<>();
        // 处理每个组
        for (Map.Entry<String, List<IObjectData>> entry : groupMap.entrySet()) {
            String ruleGroupId = entry.getKey();
            List<IObjectData> groupAchievements = entry.getValue();

            // 按设置类型分类
            List<IObjectData> productAchievements = new ArrayList<>();
            List<IObjectData> materialAchievements = new ArrayList<>();
            List<IObjectData> overallAchievements = new ArrayList<>();

            // 获取陈列形式ID
            String displayFormId = "default";
            for (IObjectData achievement : groupAchievements) {
                if (achievement.containsField(DisplayProjectAchievementFields.RELATED_DISPLAY_ACHIEVEMENT) && StringUtils.isNotBlank(getStringValue(achievement, DisplayProjectAchievementFields.RELATED_DISPLAY_ACHIEVEMENT))) {
                    displayFormId = String.valueOf(achievement.get(DisplayProjectAchievementFields.RELATED_DISPLAY_ACHIEVEMENT));
                }

                String settingType = String.valueOf(achievement.get(DisplayProjectAchievementFields.SETTING_TYPE));
                if (DisplayProjectAchievementFields.SETTING_TYPE_Options_2.equals(settingType)) {
                    productAchievements.add(achievement);
                } else if (DisplayProjectAchievementFields.SETTING_TYPE_Options_3.equals(settingType)) {
                    materialAchievements.add(achievement);
                } else if (DisplayProjectAchievementFields.SETTING_TYPE_Options_1.equals(settingType)) {
                    overallAchievements.add(achievement);
                }
            }

            // 计算各类达成详情
            ProjectAchievementDetail productDetail = calculateAchievementDetail(productAchievements);
            ProjectAchievementDetail materialDetail = calculateAchievementDetail(materialAchievements);
            ProjectAchievementDetail projectDetail = calculateAchievementDetail(overallAchievements);

            // 计算整体达成情况
            int totalCount = 0;
            int achievedCount = 0;
            int partialCount = 0;
            boolean isDisplayed = true;
            boolean isAchieved = true;

            // 累加各个detail的计数
            if (productDetail != null) {
                totalCount += productDetail.getTotalCount();
                achievedCount += productDetail.getAchievedCount();
                partialCount += productDetail.getPartialCount();
                isDisplayed &= productDetail.isDisplayed();
                isAchieved &= productDetail.isAchieved();
            }
            if (materialDetail != null) {
                totalCount += materialDetail.getTotalCount();
                achievedCount += materialDetail.getAchievedCount();
                partialCount += materialDetail.getPartialCount();
                isDisplayed &= materialDetail.isDisplayed();
                isAchieved &= materialDetail.isAchieved();
            }
            if (projectDetail != null) {
                totalCount += projectDetail.getTotalCount();
                achievedCount += projectDetail.getAchievedCount();
                partialCount += projectDetail.getPartialCount();
                isDisplayed &= projectDetail.isDisplayed();
                isAchieved &= projectDetail.isAchieved();
            }
            //陈列照

            // 计算整体达成率
            double achievementRate = totalCount > 0 ? (double) achievedCount / totalCount : 0;

            // 创建组详情对象
            GroupAchievementDetail detail = GroupAchievementDetail.builder()
                    .ruleGroupId(ruleGroupId)
                    .displayFormId(displayFormId)
                    .isAchieved(isAchieved)
                    .isDisplayed(isDisplayed)
                    .productDetail(productDetail)
                    .materialDetail(materialDetail)
                    .projectDetail(projectDetail)
//                    .achievementRate(achievementRate)
                    .totalCount(totalCount)
                    .achievedCount(achievedCount)
                    .partialCount(partialCount)
                    .build();
            detail.setDisplayPhotos(getDistinctDisplayPhotos(groupAchievements));
            groupAchievementDetailMap.put(ruleGroupId, detail);

        }
        //if overallHasParentRuleGroupIdAndParentId 不为空
        if (!overallHasParentRuleGroupIdAndParentId.isEmpty()) {
            //遍历overallHasParentRuleGroupIdAndParentId
            for (Map.Entry<String, String> entry : overallHasParentRuleGroupIdAndParentId.entrySet()) {
                String sonRuleGroupId = entry.getKey();
                String parentRuleGroupId = entry.getValue();
                //把子的合并的父的，把父的移除
                GroupAchievementDetail parentDetail = groupAchievementDetailMap.get(parentRuleGroupId);
                GroupAchievementDetail childDetail = groupAchievementDetailMap.get(sonRuleGroupId);
                if (parentDetail != null && childDetail != null) {
                  //把childDetail的陈列照合并到parentDetaillist里 需要有判空
                  if (childDetail.getDisplayPhotos() != null) {
                    if (parentDetail.getDisplayPhotos() == null) {
                        parentDetail.setDisplayPhotos(new ArrayList<>());
                    }
                    parentDetail.getDisplayPhotos().addAll(childDetail.getDisplayPhotos());
                  }
                  //把childDetail的达成详情合并到parentDetail
                  parentDetail.setAchieved(parentDetail.isAchieved() && childDetail.isAchieved());
                  parentDetail.setDisplayed(parentDetail.isDisplayed() && childDetail.isDisplayed());
//                  parentDetail.setAchievementRate(parentDetail.getAchievementRate() + childDetail.getAchievementRate());
                  parentDetail.setTotalCount(parentDetail.getTotalCount() + childDetail.getTotalCount());
                  parentDetail.setAchievedCount(parentDetail.getAchievedCount() + childDetail.getAchievedCount());
                  parentDetail.setPartialCount(parentDetail.getPartialCount() + childDetail.getPartialCount());
                  //产品 物料 整体的合并 两个detail合并
                  if (childDetail.getProductDetail() != null) {
                    if (parentDetail.getProductDetail() == null) {
                        parentDetail.setProductDetail(childDetail.getProductDetail());
                    }else{
                        parentDetail.getProductDetail().setAchieved(parentDetail.getProductDetail().isAchieved() && childDetail.getProductDetail().isAchieved());
                        parentDetail.getProductDetail().setDisplayed(parentDetail.getProductDetail().isDisplayed() || childDetail.getProductDetail().isDisplayed());
                        parentDetail.getProductDetail().setTotalCount(parentDetail.getProductDetail().getTotalCount() + childDetail.getProductDetail().getTotalCount());
                        parentDetail.getProductDetail().setAchievedCount(parentDetail.getProductDetail().getAchievedCount() + childDetail.getProductDetail().getAchievedCount());
                        parentDetail.getProductDetail().setPartialCount(parentDetail.getProductDetail().getPartialCount() + childDetail.getProductDetail().getPartialCount());
                    }
                  }
                  if (childDetail.getMaterialDetail() != null) {
                    if (parentDetail.getMaterialDetail() == null) {
                        parentDetail.setMaterialDetail(childDetail.getMaterialDetail());
                    }
                  }
                  if (childDetail.getProjectDetail() != null) {
                    if (parentDetail.getProjectDetail() == null) {
                        parentDetail.setProjectDetail(childDetail.getProjectDetail());
                    }else{
                        parentDetail.getProjectDetail().setAchieved(parentDetail.getProjectDetail().isAchieved() && childDetail.getProjectDetail().isAchieved());
                        parentDetail.getProjectDetail().setDisplayed(parentDetail.getProjectDetail().isDisplayed() || childDetail.getProjectDetail().isDisplayed());
                        parentDetail.getProjectDetail().setPartialCount(parentDetail.getProjectDetail().getPartialCount() + childDetail.getProjectDetail().getPartialCount());
                        parentDetail.getProjectDetail().setTotalCount(parentDetail.getProjectDetail().getTotalCount() + childDetail.getProjectDetail().getTotalCount());
                        parentDetail.getProjectDetail().setAchievedCount(parentDetail.getProjectDetail().getAchievedCount() + childDetail.getProjectDetail().getAchievedCount());

                    }
                  }
                  if (childDetail.getProjectDetail() != null) {
                    if (parentDetail.getProjectDetail() == null) {
                        parentDetail.setProjectDetail(childDetail.getProjectDetail());
                    }else{
                        parentDetail.getProjectDetail().setAchieved(parentDetail.getProjectDetail().isAchieved() && childDetail.getProjectDetail().isAchieved());
                        parentDetail.getProjectDetail().setDisplayed(parentDetail.getProjectDetail().isDisplayed() || childDetail.getProjectDetail().isDisplayed());
                        parentDetail.getProjectDetail().setPartialCount(parentDetail.getProjectDetail().getPartialCount() + childDetail.getProjectDetail().getPartialCount());
                        parentDetail.getProjectDetail().setTotalCount(parentDetail.getProjectDetail().getTotalCount() + childDetail.getProjectDetail().getTotalCount());
                        parentDetail.getProjectDetail().setAchievedCount(parentDetail.getProjectDetail().getAchievedCount() + childDetail.getProjectDetail().getAchievedCount());
                    }
                  }
                  //移除childDetail
                  groupAchievementDetailMap.remove(sonRuleGroupId);
                    
                }
            }
        }
        groupAchievementDetailMap.forEach((k, v) -> {
            String displayFormId = v.getDisplayFormId();
            resultMap.putIfAbsent(displayFormId, new HashMap<>());
            resultMap.get(displayFormId).put(k, v);
        });
        return resultMap;
    }
}