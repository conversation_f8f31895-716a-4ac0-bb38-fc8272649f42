package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.constants.SuccessfulStoreRangeFields;
import com.facishare.crm.fmcg.wq.constants.SummaryDisplayAchievementFields;
import com.facishare.crm.fmcg.wq.model.RestResult;
import com.facishare.crm.fmcg.wq.rule.model.AchievementResult;
import com.facishare.crm.fmcg.wq.rule.service.DataProcessService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.Data;
import org.apache.commons.collections.ListUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.*;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-18 16:08
 **/

public class DisplayDistrAchSummaryTotalReportController extends FmcgIdempotentPreDefineController<DisplayDistrAchSummaryTotalReportController.Arg, RestResult> {

    private DataProcessService dataProcessService = SpringUtil.getContext().getBean(DataProcessService.class);

    
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected RestResult doServiceIdempotent(@Valid Arg request) {
        AchievementResult achievementResult = dataProcessService.processData(
                request.getTenantId(),
                request.getDataMainId(),
                request.getDataMainApiName(),
                request.getStandardMainApiName(),
                request.getStandardMainId(),
                request.getCheckinsId(),
                request.getBusinessDataApiName(),
                request.getBusinessDataId(),
                request.getAccountId(),
                request.getRecordType()
        );
        RestResult<AchievementResult> result = RestResult.<AchievementResult>builder()
                .code(achievementResult.isSuccess() ? 0 : -1)
                .message(achievementResult.getError())
                .build();
        //暂时不给 data赋值了 fmcg-http 里面定义的data 有问题 会报错
        result.setUuId(Optional.ofNullable(achievementResult).map(o -> o.getDisplayDistrAchSummary()).map(IObjectData::getId).orElse(null));
      // 总结果是达成 extData 赋值为true
      if (SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_1.equals(Optional.ofNullable(achievementResult).map(o -> o.getDisplayDistrAchSummary()).map(o->o.get(SummaryDisplayAchievementFields.ACHIEVED_RESULTS)).orElse(null))) {
        result.setExtData(true);
      }else{
        result.setExtData(false);
      }
        return result;
    }


    @Override
    protected String getIdempotentKey(Arg arg) {
        return controllerContext.getTenantId() + controllerContext.getMethodName() + JSON.toJSONString(arg).hashCode();
    }

    @Data
    public static class Arg implements Serializable {
        @NotBlank(message = "tenantId不能为空: 企业id")
        private String tenantId;
        /**
         * 上报数据id
         */
        @NotBlank(message = "dataMainId不能为空:  上报数据主对象数据id")
        private String dataMainId;
        @NotBlank(message = "dataMainApiName不能为空: 上报数据主对象数据apiName")
        private String dataMainApiName;
        @NotBlank(message = "standardMainId不能为空: 匹配的标准主对象数据id")
        private String standardMainId;
        //标准主对象数据apiName
        private String standardMainApiName = SuccessfulStoreRangeFields.API_NAME;
        /**
         * 外勤id
         */
        private String checkinsId;
        /**
         * 业务单据数据
         */
        @NotBlank(message = "businessDataApiName不能为空: 业务数据主对象数据apiName")
        private String businessDataApiName;
        @NotBlank(message = "businessDataId不能为空: 业务数据主对象数据id 唯一标识,重复会覆盖")
        private String businessDataId;
        @NotBlank(message = "accountId不能为空: 门店id")
        private String accountId;
        @NotBlank(message = "recordType不能为空: 记录类型")
        //仅支持 陈列报告 (default__c)
        //         铺货报告 (distribution_report__c)
        //         活动报告 (activity_evidence_report__c)
        @Pattern(regexp = "default__c|distribution_report__c|activity_evidence_report__c", message = "recordType仅支持default__c(陈列报告)|distribution_report__c(铺货报告)|activity_evidence_report__c(活动报告)")
        private String recordType = "default__c";
    }
}
