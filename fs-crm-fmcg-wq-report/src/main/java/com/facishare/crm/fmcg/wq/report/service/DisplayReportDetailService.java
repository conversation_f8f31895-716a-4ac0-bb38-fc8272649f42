package com.facishare.crm.fmcg.wq.report.service;

import com.facishare.crm.fmcg.wq.constants.DisplayDistrAchSummaryFields;
import com.facishare.crm.fmcg.wq.constants.DisplayProjectAchievementFields;
import com.facishare.crm.fmcg.wq.constants.SummaryDisplayAchievementFields;
import com.facishare.crm.fmcg.wq.dao.DataReportStandardDao;
import com.facishare.crm.fmcg.wq.report.model.ProjectSettingType;
import com.facishare.crm.fmcg.wq.report.model.DisplayReportDetail;
import com.facishare.crm.fmcg.wq.report.service.displayStandard.DisplayStandardService;
import com.facishare.crm.fmcg.wq.report.service.displayStandard.DisplayStandardServiceManager;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DisplayReportDetailService {

    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    protected DataReportStandardDao dataReportStandardDao;
    @Autowired
    private DisplayStandardServiceManager displayStandardServiceManager;

    public List<DisplayReportDetail.DisplayInfo> buildReportData(User systemUser, IObjectData objectData){
        List<DisplayReportDetail.DisplayInfo> displayInfos = Lists.newArrayList();
        // 判断是否为陈列形式
        boolean display = DisplayDistrAchSummaryFields.METHODS_DISPLAY_STANDARDS_Options_2.equals(String.valueOf(objectData.get(DisplayDistrAchSummaryFields.METHODS_DISPLAY_STANDARDS)));

        // 汇总报告Id 查询陈列项目达成对象
        List<IObjectData> displayProjectAllDataList = dataReportStandardDao.getDisplayProjectAchievementByReportId(systemUser, objectData.getId());
        Map<String,String> displayProjectValueAndColorMap = buildDisplayProjectValueAndColorMap(systemUser);
        // 陈列形式+项目标准
        if(display){
            IObjectDescribe describe = serviceFacade.findObject(systemUser.getTenantId(),
                    SummaryDisplayAchievementFields.API_NAME);
            Map<String,String> valueAndColorMap = ObjectUtils.getValueAndColorByDesc(describe.getFieldDescribeMap().get(SummaryDisplayAchievementFields.ACHIEVED_RESULTS));
            // 查询陈列形式达成总结
            List<IObjectData> displayDataList = dataReportStandardDao.getDisShapeSumByDispDistrAchSummaryId(systemUser,objectData.getId(),describe);

            displayDataList.forEach(item -> {
                DisplayReportDetail.DisplayInfo displayInfo = buildDisplayInfo(item, valueAndColorMap);
                displayInfos.add(displayInfo);
                String displayId = String.valueOf(item.get(DisplayProjectAchievementFields.RELATED_DISPLAY_ACHIEVEMENT));
                // 过滤出当前陈列形式的项目达成数据。
                List<IObjectData> displayProjectDataList = displayProjectAllDataList.stream().filter(o->displayId.equals(String.valueOf(o.get(DisplayProjectAchievementFields.RELATED_DISPLAY_ACHIEVEMENT)))).collect(Collectors.toList());
                displayInfo.setProjectInfos(buildDisplayFormInfo(systemUser,displayProjectDataList,displayProjectValueAndColorMap));
            });
        }else{
            // 项目标准
            DisplayReportDetail.DisplayInfo displayInfo = new DisplayReportDetail.DisplayInfo();
            displayInfos.add(displayInfo);
            displayInfo.setProjectInfos(buildDisplayFormInfo(systemUser,displayProjectAllDataList,displayProjectValueAndColorMap));
        }

        return displayInfos;
    }

    private Map<String,String> buildDisplayProjectValueAndColorMap(User systemUser){
        IObjectDescribe displayProjectDescribe = serviceFacade.findObject(systemUser.getTenantId(),
                DisplayProjectAchievementFields.API_NAME);
        return ObjectUtils.getValueAndColorByDesc(
                displayProjectDescribe.getFieldDescribeMap().get(DisplayProjectAchievementFields.ACHIEVED_RESULTS));
    }

    private DisplayReportDetail.DisplayInfo buildDisplayInfo(IObjectData item, Map<String,String> valueAndColorMap) {
        DisplayReportDetail.DisplayInfo displayInfo = new DisplayReportDetail.DisplayInfo();
        // 陈列照片
        displayInfo.setDisplayPhoto((List<Object>) item.get(SummaryDisplayAchievementFields.DISPLAY_PHOTO));
        // 陈列形式
        displayInfo.setName((String)item.get(SummaryDisplayAchievementFields.RELATED_DISPLAY_ACHIEVEMENT_NAME));
        displayInfo.setReachProject(ObjectUtils.buildManySelectValue(item, SummaryDisplayAchievementFields.ACHIEVEMENT_SUMMARY_NAME));
        displayInfo.setUnReachProject(ObjectUtils.buildManySelectValue(item, SummaryDisplayAchievementFields.SUMMARY_ISSUES_NAME));
        displayInfo.setReachStatus((String)item.get(SummaryDisplayAchievementFields.ACHIEVED_RESULTS__R));
        displayInfo.setReachColor(Objects.nonNull(valueAndColorMap) ? valueAndColorMap.get(displayInfo.getReachStatus()) : "");
        return displayInfo;
    }
    private List<DisplayReportDetail.ProjectInfo> buildDisplayFormInfo(User systemUser, List<IObjectData> displayProjectDataList,Map<String,String> displayProjectValueAndColorMap) {
        Map<String, List<IObjectData>> displayProjectDataMap = displayProjectDataList.stream().collect(
                Collectors.groupingBy(
                        item -> String.valueOf(item.get(DisplayProjectAchievementFields.SETTING_TYPE))));

        List<DisplayReportDetail.ProjectInfo> result = Lists.newArrayList();
        for (Map.Entry<String, List<IObjectData>> item : displayProjectDataMap.entrySet()) {
            ProjectSettingType projectSettingType = ProjectSettingType.getByCode(item.getKey());
            if (Objects.isNull(projectSettingType)) {
                log.info("buildProjectInfo enum is null:{}", item.getKey());
                continue;
            }
            DisplayReportDetail.ProjectInfo projectInfo = new DisplayReportDetail.ProjectInfo();
            // TODO 国际化
            projectInfo.setTitle(projectSettingType.i18nKey);
            projectInfo.setCode(projectSettingType.code);
            DisplayStandardService displayStandardService = displayStandardServiceManager.getDisplayStandardService(projectSettingType.serviceKey);
            projectInfo.setProjectDetailInfos(displayStandardService.buildProjectInfo(systemUser,item.getValue(),displayProjectValueAndColorMap));
            result.add(projectInfo);
        }
        // 按照类型排序
        result.sort(Comparator.comparing(DisplayReportDetail.ProjectInfo::getCode));
        return result;
    }
}
