package com.facishare.crm.fmcg.wq.rule.model.mapping.base;

import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.rule.model.mapping.BaseRuleTemplateConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Value Mapping Configuration
 * <p>
 * Defines how source values should be transformed or mapped to target values.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MappingConfig {
    /**
     * Type of mapping to perform
     *
     * @see MappingType
     */
    private MappingType mappingType;

    /**
     * Static value mappings (key=source value, value=target value)
     * Used when mappingType is STATIC
     */
    private Map<String, Object> staticMapping;

    /**
     * Dynamic expression to evaluate to transform the value
     * Used when mappingType is DYNAMIC
     * Example: "#value.toString().toUpperCase()"
     */
    private String dynamicExpression;

    /**
     * String format pattern to apply
     * Example: "%s-%s" for concatenation
     */
    private String stringFormat;

    /**
     * API name to use when looking up prefix field values
     */
    private String prefixObjApiName;
    
    /**
     * Data ID to use when looking up prefix field values
     */
    private String prefixedObjDataId;

    private String prefixedFieldDataName;

    /**
     * format field api name {0}__{1}
     */
    private String prefixedFieldApiName;

    /**
     * standard field api name
     */
    private String standardFieldApiName;

    /**
     * Default value to use when mapping produces null
     */
    private Object defaultValue;

    /**
     * Create a static value mapping configuration
     *
     * @param sourceToTargetMap Map of source values to target values
     * @return The configured mapping
     */
    public static MappingConfig createStaticMapping(Map<String, Object> sourceToTargetMap) {
        return MappingConfig.builder()
                .mappingType(MappingType.STATIC)
                .staticMapping(sourceToTargetMap)
                .build();
    }
    
    /**
     * Create a default value mapping configuration
     */
    public static MappingConfig createDefaultValueMapping(String defaultValue) {
        return MappingConfig.builder()
                .mappingType(MappingType.DEFAULT_VALUE)
                .defaultValue(defaultValue)
                .build();
    }
    /**
     * 使用 FastJSON 实现深拷贝
     */
    public MappingConfig  copy() {
        return JSON.parseObject(JSON.toJSONString(this), MappingConfig.class);
    }

    /**
     * Create a prefixField mapping configuration
     * <p>
     * 、
     **/
    public static MappingConfig createPrefixFieldKeyMapping(String standardFieldApiName, String prefixFieldFromApiName, 
                                                           String prefixedFieldDataId, String prefixedFieldDataName, 
                                                           String prefixedFieldApiName) {
        return MappingConfig.builder()
                .mappingType(MappingType.FIELD_VALUE_PREFIX)
                .prefixObjApiName(prefixFieldFromApiName)
                .prefixedObjDataId(prefixedFieldDataId)
                .prefixedFieldDataName(prefixedFieldDataName)
                .standardFieldApiName(standardFieldApiName)
                .prefixedFieldApiName(prefixedFieldApiName)
                .build();
    }
    
    /**
     * 转换字段值
     */
    public static Object convertStandardValue(Object value, MappingConfig valueMapping) {
        if (valueMapping == null) {
            return value;
        }

        switch (valueMapping.getMappingType()) {
            case DEFAULT_VALUE:
                return value == null ? valueMapping.getDefaultValue() : value;
            case STATIC:
                return valueMapping.getStaticMapping().getOrDefault(
                        value.toString(), valueMapping.getDefaultValue());
            case FUNCTION:
                // 实现自定义函数映射
                return value;

            case CONDITIONAL:
                // 这种情况在外层方法中处理
                return value;

            case FIELD_VALUE_PREFIX:
                return valueMapping.getPrefixedFieldApiName();

            default:
                return value;
        }
    }
} 