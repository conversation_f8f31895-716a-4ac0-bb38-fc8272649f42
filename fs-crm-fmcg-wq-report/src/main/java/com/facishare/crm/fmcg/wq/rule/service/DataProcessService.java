package com.facishare.crm.fmcg.wq.rule.service;

import com.beust.jcommander.internal.Lists;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.crm.fmcg.wq.FmcgGray;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.DataReport2PublicFieldsConstants;
import com.facishare.crm.fmcg.wq.constants.DisplayDistrAchSummaryFields;
import com.facishare.crm.fmcg.wq.constants.SuccessfulStoreRangeFields;
import com.facishare.crm.fmcg.wq.constants.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.wq.constants.TPMActivityFields;
import com.facishare.crm.fmcg.wq.dao.*;
import com.facishare.crm.fmcg.wq.report.model.ReportType;
import com.facishare.crm.fmcg.wq.rule.model.AchievementResult;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.RecordType;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.fxiaoke.release.FsGrayRelease;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DataProcessService {

    @Autowired
    private BaseDaoInterface baseDao;

    @Autowired
    private DataReportStandardDao dataReportStandardDao;

    @Autowired
    private DataReportResultDao dataReportResultDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private RuleService ruleService;
    @Autowired
    private DataReportDataDao dataReportDataDao;

    /**
     * 处理数据映射规则
     *
     * @param tenantId 租户ID
     * @param dataMainId 数据主ID
     * @param dataMainApiName 数据主API名称
     * @param standardMainId 标准主ID
     * @param checkinsId 外勤ID
     * @param businessDataApiName 业务数据API名称
     * @param businessDataId 业务数据ID
     * @param accountId 门店ID
     * @param recordType 记录类型
     * @return 处理结果
     */
    public AchievementResult processData(String tenantId, String dataMainId, String dataMainApiName,
                                String standardMainApiName,
                                String standardMainId, String checkinsId, String businessDataApiName, 
                                String businessDataId, String accountId, String recordType) {
        try {
            log.info("处理字段映射规则，参数: tenantId={}, dataMainId={}, dataMainApiName={}, standardMainId={}, " +
                    "checkinsId={}, businessDataApiName={}, businessDataId={}, accountId={}, recordType={}", 
                    tenantId, dataMainId, dataMainApiName, standardMainId, checkinsId, 
                    businessDataApiName, businessDataId, accountId, recordType);

            if (tenantId == null || StringUtils.isBlank(dataMainId) || dataMainApiName == null) {
                AchievementResult achievementResult = new AchievementResult();
                achievementResult.setSuccess(false);
                achievementResult.setError("参数错误"); //ignoreI18n
                return achievementResult;
            }
            //限制条件 standardMainApiName recordType = default的时候
            if ( ReportType.DISPLAY.getRecord().equals(recordType) || ReportType.DISTRIBUTION.getRecord().equals(recordType)) {
                if (!SuccessfulStoreRangeFields.API_NAME.equals(standardMainApiName)) {
                    AchievementResult achievementResult = new AchievementResult();
                    achievementResult.setSuccess(false);
                    achievementResult.setError("陈列/铺货报告只支持"+ SuccessfulStoreRangeFields.API_NAME + "标准"); //ignoreI18n
                    return achievementResult;
                }
            }else if ( ReportType.ACTIVITY.getRecord().equals(recordType)) {
                if (!TPMActivityAgreementFields.API_NAME.equals(standardMainApiName) && !TPMActivityFields.API_NAME.equals(standardMainApiName)) {
                    AchievementResult achievementResult = new AchievementResult();
                    achievementResult.setSuccess(false);
                    achievementResult.setError("活动报告只支持"+ TPMActivityAgreementFields.API_NAME + "或" + TPMActivityFields.API_NAME + "标准"); //ignoreI18n
                    return achievementResult;
                }
            }else {
                AchievementResult achievementResult = new AchievementResult();
                achievementResult.setSuccess(false);
                achievementResult.setError("业务类型错误"); //ignoreI18n
                return achievementResult;
            }
            // 创建系统用户
            User user = User.systemUser(tenantId);

            // 查询数据主对象
            Map<String, Map<String, IObjectData>> sourceReportDataMap = dataReportDataDao.getMainAndDetailObjectData(user, dataMainApiName, dataMainId);
            if (sourceReportDataMap == null) {
                log.warn("未找到数据主对象: tenantId={}, apiName={}, id={}", tenantId, dataMainApiName, dataMainId);
                AchievementResult achievementResult = new AchievementResult();
                achievementResult.setSuccess(false);
                achievementResult.setError("未找到数据主对象"); //ignoreI18n
                return achievementResult;
            }

            // 查标准数据对象
            Map<String, Map<String, IObjectData>> standardMap = dataReportStandardDao.getByMainObjectId(user,standardMainApiName, standardMainId,recordType);
            
            // 调用规则服务处理数据
            AchievementResult achievementResult = ruleService.processReportDataList(user, sourceReportDataMap, standardMap, recordType);
            // 给total 结果复制
            if (Objects.nonNull(achievementResult.getDisplayDistrAchSummary())) {
                achievementResult.getDisplayDistrAchSummary().set(DisplayDistrAchSummaryFields.RULE_GROUP_API_NAME, standardMainApiName);
                achievementResult.getDisplayDistrAchSummary().set(DisplayDistrAchSummaryFields.RULE_GROUP_ID, standardMainId);
            }
            // 处理结果数据
            processAchievementResult(user, achievementResult, sourceReportDataMap, businessDataApiName, 
                    businessDataId, checkinsId, accountId, recordType);

            return achievementResult;

        } catch (Exception e) {
            log.error("处理字段映射规则失败", e);
            AchievementResult achievementResult = new AchievementResult();
            achievementResult.setSuccess(false);
            achievementResult.setError(e.getMessage());
            return achievementResult;
        }
    }

    /**
     * 处理成就结果数据
     */
    private void processAchievementResult(User user, AchievementResult achievementResult,
                                        Map<String, Map<String, IObjectData>> sourceReportDataMap,
                                        String businessDataApiName, String businessDataId,
                                        String checkinsId, String accountId, String recordType) {
        // 查询原始数据 保存新数据
        IObjectData dataReportResult = null;
        List<IObjectData> resultsByBusinessId = dataReportResultDao.getMainByBusinessId(user, businessDataApiName, businessDataId, recordType);
        if (CollectionUtils.isNotEmpty(resultsByBusinessId)){
            dataReportResult = resultsByBusinessId.get(0);
        }
        if (dataReportResult != null) {
            //删除数据
            dataReportResultDao.deleteByDisplayDistrAchSummaryObj(user, dataReportResult);
        }

        IObjectData displayDistrAchSummary = null;
        if (achievementResult.getDisplayDistrAchSummary() != null) {
           displayDistrAchSummary = processDisplayDistrAchSummary(user, achievementResult, sourceReportDataMap, businessDataApiName,
                    businessDataId, checkinsId, accountId, recordType).get(0);
        }

        if (CollectionUtils.isNotEmpty(achievementResult.getTotalProjectAchievements())) {
            processDisplayProjectAchievements(user, achievementResult);
        }

        if (CollectionUtils.isNotEmpty(achievementResult.getSummaryDisplayAchievements())) {
            processSummaryDisplayAchievements(user, achievementResult);
        }

        if (CollectionUtils.isNotEmpty(achievementResult.getDistributionProductsAchievements())) {
            processDistributionProductsAchievements(user, achievementResult);
        }
        //整体完成 修改主数据字段
        if (FmcgGray.Checkins.EI.syncAchievementResultsAllCompleteTime.fsGray(user.getTenantId()) || "791881".equals(user.getTenantId())) {
            displayDistrAchSummary.set("detail_completed_timestamp__c", String.valueOf(System.currentTimeMillis()));
            dataReportResultDao.update(user,displayDistrAchSummary);
        }
    }

    /**
     * 处理陈列分销达成汇总数据
     */
    private List<IObjectData> processDisplayDistrAchSummary(User user, AchievementResult achievementResult,
                                             Map<String, Map<String, IObjectData>> sourceReportDataMap,
                                             String businessDataApiName, String businessDataId,
                                             String checkinsId, String accountId, String recordType) {
        IObjectData displayDistrAchSummary = achievementResult.getDisplayDistrAchSummary();
        
        //业务数据
        displayDistrAchSummary.set(DisplayDistrAchSummaryFields.CHECK_ID, checkinsId);
        displayDistrAchSummary.set(DisplayDistrAchSummaryFields.BUSINESS_DOCUMENTS_ID, businessDataId);
        displayDistrAchSummary.set(DisplayDistrAchSummaryFields.BUSINESS_DOCUMENTS_APINAME, businessDataApiName);
        displayDistrAchSummary.set(DisplayDistrAchSummaryFields.STORE_NAME, accountId);

        String yyyyMMdd = DateUtils.getStrFromLongZoneId(System.currentTimeMillis(), "yyyyMMdd");
        String accountName = "";
        if (StringUtils.isNotBlank(accountId)) {
            IObjectData accountObj = accountDao.getById(user.getTenantId(), "AccountObj", accountId);
            if (accountObj != null) {
                accountName = accountObj.getName();
            }
        }

        displayDistrAchSummary.set(DisplayDistrAchSummaryFields.REPORT_TITLE, 
                yyyyMMdd + "_" + accountName + "_" + 
                I18N.text(GetI18nKeyUtil.getOptionNameKey(DisplayDistrAchSummaryFields.API_NAME, 
                        BaseField.recordType.getApiName(), recordType)));

        //上报数据 sourceReportDataMap 按照apiName 分组
        List<String> related_api_names = Lists.newArrayList(sourceReportDataMap.keySet());
        Map<String, List<String>> related_object = new HashMap<>();
        List<Map<String, String>> related_object_r = new ArrayList<>();

        sourceReportDataMap.entrySet().forEach(entry -> {
            String apiName = entry.getKey();
            entry.getValue().entrySet().forEach(dataEntry -> {
                String id = dataEntry.getKey();
                Map<String, String> data = new HashMap<>();
                data.put("id", id);
                data.put("describe_api_name", apiName);
                data.put("name", dataEntry.getValue().getName());
                related_object_r.add(data);
                related_object.computeIfAbsent(apiName, k -> new ArrayList<>()).add(id);
            });
        });

        displayDistrAchSummary.set("related_object", related_object);
        displayDistrAchSummary.set("related_api_names", related_api_names);
        displayDistrAchSummary.set("related_object_r", related_object_r);

       return dataReportResultDao.batchSave(user, Lists.newArrayList(achievementResult.getDisplayDistrAchSummary()));
    }

    /**
     * 处理陈列项目达成数据
     */
    private void processDisplayProjectAchievements(User user, AchievementResult achievementResult) {
        List<IObjectData> displayProjectAchievements = achievementResult.getTotalProjectAchievements().stream()
                .filter(o -> StringUtils.isBlank(o.get(DataReport2PublicFieldsConstants.CONDITION_PARENT_ID, String.class)))
                .collect(Collectors.toList());

        List<IObjectData> displayProjectAchievementsWithParent = achievementResult.getTotalProjectAchievements().stream()
                .filter(o -> StringUtils.isNotBlank(o.get(DataReport2PublicFieldsConstants.CONDITION_PARENT_ID, String.class)))
                .collect(Collectors.toList());

        dataReportResultDao.batchSave(user, displayProjectAchievements);
        dataReportResultDao.batchSave(user, displayProjectAchievementsWithParent);
    }

    /**
     * 处理汇总陈列达成数据
     */
    private void processSummaryDisplayAchievements(User user, AchievementResult achievementResult) {
        List<IObjectData> summaryDisplayAchievements = achievementResult.getSummaryDisplayAchievements().stream()
                .filter(o -> StringUtils.isBlank(o.get(DataReport2PublicFieldsConstants.CONDITION_PARENT_ID, String.class)))
                .collect(Collectors.toList());

        List<IObjectData> summaryDisplayAchievementsWithParent = achievementResult.getSummaryDisplayAchievements().stream()
                .filter(o -> StringUtils.isNotBlank(o.get(DataReport2PublicFieldsConstants.CONDITION_PARENT_ID, String.class)))
                .collect(Collectors.toList());

        dataReportResultDao.batchSave(user, summaryDisplayAchievements);
        dataReportResultDao.batchSave(user, summaryDisplayAchievementsWithParent);
    }

    /**
     * 处理分销产品达成数据
     */
    private void processDistributionProductsAchievements(User user, AchievementResult achievementResult) {
        List<IObjectData> distributionProductsAchievements = achievementResult.getDistributionProductsAchievements().stream()
                .filter(o -> StringUtils.isBlank(o.get(DataReport2PublicFieldsConstants.CONDITION_PARENT_ID, String.class)))
                .collect(Collectors.toList());

        List<IObjectData> distributionProductsAchievementsWithParent = achievementResult.getDistributionProductsAchievements().stream()
                .filter(o -> StringUtils.isNotBlank(o.get(DataReport2PublicFieldsConstants.CONDITION_PARENT_ID, String.class)))
                .collect(Collectors.toList());

        dataReportResultDao.batchSave(user, distributionProductsAchievements);
        dataReportResultDao.batchSave(user, distributionProductsAchievementsWithParent);
    }

    /**
     * 处理产品和物料数据映射规则
     * 只处理产品和物料相关的数据，不处理其他类型的数据
     *
     * @param tenantId 租户ID
     * @param dataMainId 数据主ID
     * @param dataMainApiName 数据主API名称
     * @param standardMainId 标准主ID
     * @param checkinsId 外勤ID
     * @param businessDataApiName 业务数据API名称
     * @param businessDataId 业务数据ID
     * @param accountId 门店ID
     * @param recordType 记录类型
     * @return 处理结果
     */
    public AchievementResult processProductAndMaterialDataNoSave(String tenantId, String dataMainId, String dataMainApiName,
                                                                 String standardMainApiName,
                                                                 String standardMainId, String checkinsId, String businessDataApiName,
                                                                 String businessDataId, String accountId, String recordType) {
        try {
            log.info("处理产品和物料数据映射规则，参数: tenantId={}, dataMainId={}, dataMainApiName={}, standardMainId={}, " +
                    "checkinsId={}, businessDataApiName={}, businessDataId={}, accountId={}, recordType={}", 
                    tenantId, dataMainId, dataMainApiName, standardMainId, checkinsId, 
                    businessDataApiName, businessDataId, accountId, recordType);

            if (tenantId == null || StringUtils.isBlank(dataMainId) || dataMainApiName == null) {
                AchievementResult achievementResult = new AchievementResult();
                achievementResult.setSuccess(false);
                achievementResult.setError("参数错误"); //ignoreI18n
                return achievementResult;
            }

            // 创建系统用户
            User user = User.systemUser(tenantId);

            // 查询数据主对象
            Map<String, Map<String, IObjectData>> sourceReportDataMap = dataReportDataDao.getMainAndDetailObjectData(user, dataMainApiName, dataMainId);
            if (sourceReportDataMap == null) {
                log.warn("未找到数据主对象: tenantId={}, apiName={}, id={}", tenantId, dataMainApiName, dataMainId);
                AchievementResult achievementResult = new AchievementResult();
                achievementResult.setSuccess(false);
                achievementResult.setError("未找到数据主对象"); //ignoreI18n
                return achievementResult;
            }

            // 查标准数据对象
            Map<String, Map<String, IObjectData>> standardMap = dataReportStandardDao.getByMainObjectId(user,standardMainApiName, standardMainId, recordType);
            
            // 调用规则服务处理数据 - 使用新的方法
            AchievementResult achievementResult = ruleService.processProductAndMaterialReportDataList(
                user, sourceReportDataMap, standardMap, recordType);

            return achievementResult;

        } catch (Exception e) {
            log.error("处理产品和物料数据映射规则失败", e);
            AchievementResult achievementResult = new AchievementResult();
            achievementResult.setSuccess(false);
            achievementResult.setError(e.getMessage());
            return achievementResult;
        }
    }
} 
