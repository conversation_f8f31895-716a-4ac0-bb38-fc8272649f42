package com.facishare.crm.fmcg.wq.rule.model.mapping.base;

/**
 * Logical Operator
 * <p>
 * Defines logical operators for combining conditions.
 * This is the unified logical operator used across all rule components.
 */
public enum LogicalOperator {
    /**
     * All conditions must be true (AND)
     */
    AND("&&"),

    /**
     * At least one condition must be true (OR)
     */
    OR("||");

    private final String symbol;

    LogicalOperator(String symbol) {
        this.symbol = symbol;
    }

    /**
     * Get the symbol representation of this operator
     * 
     * @return The symbol (e.g., "&&" for AND)
     */
    public String getSymbol() {
        return symbol;
    }
    
    /**
     * Convert a string representation of the operator to the enum
     * For backward compatibility with both string representations
     * 
     * @param operatorStr The string representation ("AND", "OR", "&&", "||")
     * @return The corresponding LogicalOperator enum value
     */
    public static LogicalOperator fromString(String operatorStr) {
        if (operatorStr == null) {
            return AND; // Default to AND
        }
        
        String upperCaseOp = operatorStr.toUpperCase();
        if ("AND".equals(upperCaseOp) || "&&".equals(upperCaseOp)) {
            return AND;
        } else if ("OR".equals(upperCaseOp) || "||".equals(upperCaseOp)) {
            return OR;
        } else {
            return AND; // Default to AND for unknown values
        }
    }
    
    /**
     * Evaluate a condition using this logical operator
     * 
     * @param left The left operand
     * @param right The right operand
     * @return The result of applying this operator to the operands
     */
    public boolean evaluate(boolean left, boolean right) {
        if (this == AND) {
            return left && right;
        } else {
            return left || right;
        }
    }
} 