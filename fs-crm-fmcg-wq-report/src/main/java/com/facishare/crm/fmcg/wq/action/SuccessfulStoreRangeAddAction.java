package com.facishare.crm.fmcg.wq.action;

import com.facishare.appserver.utils.I18nZhCNEnum;
import com.facishare.crm.fmcg.wq.constants.SuccessfulStoreRangeConstants;
import com.facishare.crm.fmcg.wq.dao.CheckinsDao;
import com.facishare.crm.fmcg.wq.rule.util.CheckStandardDetailArgsUtils;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

@Slf4j
public class SuccessfulStoreRangeAddAction extends FmcgSkipPermissionAddAction {

    CheckinsDao checkinsDao = SpringUtil.getContext().getBean(CheckinsDao.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String tenantId = actionContext.getTenantId();
        CheckStandardDetailArgsUtils.checkSuccessStoreRangeDetailArgs(arg);
        //校验不能重复
        if(MapUtils.isNotEmpty(arg.getDetails())) {
            for (Map.Entry<String, List<ObjectDataDocument>> entry : arg.getDetails().entrySet()) {
                switch (entry.getKey()) {
                    case SuccessfulStoreRangeConstants.ProjectStandardsObj:
                        if(CollectionUtils.isNotEmpty(entry.getValue())){
                            long size = entry.getValue().stream().map(o->(o.get(SuccessfulStoreRangeConstants.TPM_PROJECT)==null?"":o.get(SuccessfulStoreRangeConstants.TPM_PROJECT).toString()) + (o.get(SuccessfulStoreRangeConstants.PRODUCT)==null?"":o.get(SuccessfulStoreRangeConstants.PRODUCT).toString())).distinct().count();
                            if(size!= entry.getValue().size()){
//                                throw new ValidateException(I18N.text(I18nZhCNEnum.oaappsrv_waiqin_label_projectrepeat.getI18nKey()));
                            }
                        }
                        break;
                    case SuccessfulStoreRangeConstants.MustDistributeProductsObj:
                        if(CollectionUtils.isNotEmpty(entry.getValue())){
                            //多选校验
                            Set<String> set = Sets.newHashSet();
                            Map<String,List<String>> listMulti = Maps.newHashMap();
                            boolean multiFlag = false;
                            for (ObjectDataDocument objectDataDocument : entry.getValue()) {
                                if(objectDataDocument.get(SuccessfulStoreRangeConstants.PRODUCT)!=null){
                                    String id = objectDataDocument.get(SuccessfulStoreRangeConstants.PRODUCT).toString();
                                    set.add(id);
                                }

                                Object data = objectDataDocument.get(SuccessfulStoreRangeConstants.PRODUCT_MULTIPLE);
                                if(Objects.nonNull(data)){
                                    List<String> list = (List<String>) data;
                                    Object sceneObj = objectDataDocument.get(SuccessfulStoreRangeConstants.DISPLAY_SCENE);
                                    String scene = sceneObj == null?"defaultV":sceneObj.toString();
                                    if(CollectionUtils.containsAny(listMulti.getOrDefault(scene,Lists.newArrayList()),list)){
                                        multiFlag = true;
                                        break;
                                    }else {
                                        listMulti.computeIfAbsent(scene,o->Lists.newArrayList()).addAll(list);
                                    }
                                }
                            }
                            if(set.size()!=0&&set.size()!= entry.getValue().size() || multiFlag){
//                                throw new ValidateException(I18N.text(I18nZhCNEnum.oaappsrv_waiqin_label_mustproductrepeat.getI18nKey()));
                            }
                        }
                        break;
                }

            }
        }

        Map<String,String> field = checkinsDao.getSuccessfulField(tenantId);
        String custom = ConfigFactory.getConfig("checkin-custom-config").get("successfulCustomCheck_"+tenantId);
        SearchQuery.SearchQueryBuilder searchQuery = SearchQuery.builder();
        boolean flag = false;
        if(MapUtils.isNotEmpty(field) && !StringUtils.isNotEmpty(custom)){
            IObjectData argData = arg.getObjectData().toObjectData();
            for (Map.Entry<String, String> entry : field.entrySet()) {
                arg.getObjectData().get(entry.getKey());
                switch (entry.getValue()){
                    case "channel":
                    case "select":
                        String data = argData.get(entry.getKey(),String.class);
                        if(StringUtils.isNotEmpty(data)) {
                            searchQuery.eq(entry.getKey(), data);
                            flag=true;
                        }else{
                            searchQuery.notExist(entry.getKey());
                        }
                        break;
                    case "dept":
                        ArrayList dept = argData.get(entry.getKey(), ArrayList.class, Lists.newArrayList());
                        if(CollectionUtils.isNotEmpty(dept)) {
                            searchQuery.inDepartmentNotSub(entry.getKey(), dept);
                            flag=true;
                        }else{
                            searchQuery.notExist(entry.getKey());
                        }
                }
            }
            if(flag) {
                IObjectDescribe describe = serviceFacade.findObject(actionContext.getTenantId(), SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj);
                List<IObjectData> data = ObjectUtils.queryDataSimple(serviceFacade, actionContext.getUser(), SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj, searchQuery.build().getSearchTemplateQuery(), describe);
                if (CollectionUtils.isNotEmpty(data)) {
                    throw new ValidateException(I18N.text(I18nZhCNEnum.oaappsrv_waiqin_label_standardrepeat.getI18nKey()));
                }
            }
        }else{
//            throw new ValidateException("成功门店配置尚未完成，请到外勤全局设置中配置完成后再添加数据。");
        }
    }
}
