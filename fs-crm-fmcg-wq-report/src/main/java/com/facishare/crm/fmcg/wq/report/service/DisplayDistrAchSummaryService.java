package com.facishare.crm.fmcg.wq.report.service;

import com.facishare.appserver.checkins.api.model.ReportActionInfo;
import com.facishare.appserver.checkins.model.common.IdAndName;
import com.facishare.crm.fmcg.wq.constants.ActivityProofFields;
import com.facishare.crm.fmcg.wq.dao.DataReportResultDao;
import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.crm.fmcg.wq.report.model.ReportType;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/03/28/ 18:23
 **/
@Slf4j
@Service
public class DisplayDistrAchSummaryService {

    @Autowired
    private DataReportResultDao dataReportResultDao;

    @Autowired
    private CheckinsProxy checkinsProxy;

    public List<IObjectData> getDataByReportType(User systemUser, ReportType reportType, IdAndName idAndName) {
        if (Objects.isNull(idAndName) || StringUtils.isBlank(idAndName.getApiName()) || StringUtils.isBlank(idAndName.getId())) {
            log.info("getDataByReportType apiName is blank reportType is:{}", reportType);
            return null;
        }
        // 陈列铺货达成汇总 DisplayDistrAchSummaryObj
        if (ReportType.ACTIVITY.equals(reportType)) {
            return dataReportResultDao.getMainByBusinessId(systemUser, idAndName.getApiName(), idAndName.getId(),
                    Lists.newArrayList(reportType.getRecord()));
        } else {
            return dataReportResultDao.getMainByBusinessId(systemUser, idAndName.getApiName(), idAndName.getId(), Lists.newArrayList(
                    ReportType.DISPLAY.getRecord(), ReportType.DISTRIBUTION.getRecord()));
        }
    }

    public IdAndName buildReportIdAndApiName(User systemUser, ReportType reportType, String dataId, String apiName, String actionId, String checkinId){
        IdAndName idAndName = new IdAndName();
        if (ReportType.ACTIVITY.equals(reportType)) {
            // 活动举证报告
            apiName = StringUtils.isBlank(apiName) ? ActivityProofFields.API_NAME : apiName;
            if (StringUtils.isBlank(dataId)) {
                // 通过外勤 动作查询活动举证id
                dataId = getActivityProofId(systemUser, actionId, checkinId);
            }
        } else {
            // 铺货报告
            if (StringUtils.isBlank(apiName) || StringUtils.isBlank(dataId)) {
                // 查询外勤配置
                ReportActionInfo.Result result = getReportActionInfo(systemUser.getTenantId(), actionId, checkinId);
                apiName = result.getReportApiName();
                dataId = result.getReportDataId();
            }
        }
        if (StringUtils.isBlank(apiName) || StringUtils.isBlank(dataId)) {
            log.info("buildReportIdAndApiName apiName is blank reportType is:{}", reportType);
            return null;
        }
        idAndName.setApiName(apiName);
        idAndName.setId(dataId);
        return idAndName;
    }

    private ReportActionInfo.Result getReportActionInfo(String tenantId, String actionId, String checkId) {
        ReportActionInfo.Arg arg = new ReportActionInfo.Arg();
        arg.setTenantId(tenantId);
        arg.setActionId(actionId);
        arg.setCheckinId(checkId);
        return checkinsProxy.getReportActionInfo(tenantId, arg);
    }

    private String getActivityProofId(User user, String actionId, String checkId) {
        List<IObjectData> resultMainList = dataReportResultDao.getAllIObjectDataListByQuery(user,
                SearchQuery.builder().eq(
                        ActivityProofFields.ACTION_ID, actionId).eq(ActivityProofFields.CHECK_ID, checkId).build(),
                ActivityProofFields.API_NAME);
        if (resultMainList != null && !resultMainList.isEmpty()) {
            return resultMainList.get(0).getId();
        }
        return null;
    }
}
