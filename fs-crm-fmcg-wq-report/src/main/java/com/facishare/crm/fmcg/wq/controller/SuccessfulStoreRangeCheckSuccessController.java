package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.api.success.CheckSuccess;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.constants.SuccessfulStoreRangeConstants;
import com.facishare.crm.fmcg.wq.dao.BaseDao;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

public class SuccessfulStoreRangeCheckSuccessController extends PreDefineController<CheckSuccess.Arg, CheckSuccess.Result> {

    RedisUtils redisUtils = SpringUtil.getContext().getBean(RedisUtils.class);

    BaseDao baseDao = SpringUtil.getContext().getBean(BaseDao.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected CheckSuccess.Result doService(CheckSuccess.Arg arg) {
        CheckSuccess.Result result = new CheckSuccess.Result();
        String custom = ConfigFactory.getConfig("checkin-custom-config").get("successfulCustomCheck_"+controllerContext.getTenantId());
        if(StringUtils.isNotEmpty(custom)){
            return handleCustomService(arg,custom);
        }
        IObjectData storeData = serviceFacade.findObjectData(controllerContext.getUser(),arg.getStoreId(), CommonConstants.ACCOUNT_OBJ);
        try {
            if (arg.getMatchType() == 0) {
                getResultMatchType0(arg, result, storeData);//老的完全匹配
            } else {
                getResultMatchType1(arg, result, storeData); //模糊匹配
            }
        }catch (Exception e){
            log.error("CheckSuccess error arg:{} ",arg,e);
        }


        return result;
    }

    private void getResultMatchType1(CheckSuccess.Arg arg, CheckSuccess.Result result, IObjectData storeData) {
        //模糊匹配第一个规则必须匹配到，否则不进行后续匹配
        if(CollectionUtils.isEmpty(arg.getMatchFields())){
            return;
        }

        //ABCD的数据     能命中的规则 四个   ABCD  ABC_  AB__ A____

        //保底命中第一个字段的数据
        SearchQuery.SearchQueryBuilder searchQuery = SearchQuery.builder();
        searchQuery.eq("state",true);
        if (StringUtils.isNotBlank(arg.getRecordType())){
            searchQuery.eq(BaseField.recordType.getApiName(), arg.getRecordType());
        }
        CheckSuccess.Config config = arg.getMatchFields().get(0);
        switch (config.getType()) {
            case "channel":
            case "select":
                String data = storeData.get(config.getApiName(),String.class);
                if(StringUtils.isNotEmpty(data)) {
                    searchQuery.eq(config.getRuleApiName(), data);
                }else{
                    searchQuery.notExist(config.getRuleApiName());
                }
                break;
            case "dept":
                ArrayList dept = storeData.get(config.getApiName(), ArrayList.class, Lists.newArrayList());
                if(CollectionUtils.isNotEmpty(dept)) {
                    searchQuery.inDepartmentNotSub(config.getRuleApiName(),dept);
                }else{
                    searchQuery.notExist(config.getRuleApiName());
                }
                break;
        }

//        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj);
//        List<IObjectData> data = ObjectUtils.queryDataSimple(serviceFacade,controllerContext.getUser(), SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj,searchQuery.build().getSearchTemplateQuery(),describe);
        List<IObjectData> data = baseDao.getAllIObjectDataListByQuery(User.systemUser(controllerContext.getTenantId()),searchQuery.build(), SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj);

        if (CollectionUtils.isNotEmpty(data)) {
            //只有保底规则
            if(arg.getMatchFields().size()==1){
                result.setId(data.get(0).getId());
                return;
            }
            //多个规则
            for (int i = 1; i < arg.getMatchFields().size(); i++) {
                CheckSuccess.Config subConfig = arg.getMatchFields().get(i);
                subConfig.setData(getDataByConfig(storeData,subConfig,true));
            }
            String[] ruleDataId = new String[arg.getMatchFields().size()];

            for (IObjectData datum : data) {
                boolean isMatch = true;  //是否保底
                int matchSize = 0;  //命中到  第几个字段
                for (int i = 1; i < arg.getMatchFields().size(); i++) {
                    CheckSuccess.Config  subConfig = arg.getMatchFields().get(i);

                    String ruleData = getDataByConfig(datum,subConfig,false);
                    if(isMatch && ruleData.equals(subConfig.getData())){
                        matchSize++;
                        if(i==arg.getMatchFields().size()-1){
                            ruleDataId[matchSize]=datum.getId();
                        }
                    }else if(StringUtils.isEmpty(ruleData)){
                        if(i==arg.getMatchFields().size()-1){
                            ruleDataId[matchSize]=datum.getId();
                        }
                        isMatch = false;
                    }else {
                        break;
                    }
                }
            }
            //找到最适配的规则
            log.info("storeId:{} ,ruleDataId:{}",arg.getStoreId(), Arrays.toString(ruleDataId));
            for (int i = ruleDataId.length - 1; i >= 0; i--) {
                if(StringUtils.isNotEmpty(ruleDataId[i])){
                    result.setId(ruleDataId[i]);
                    break;
                }
            }
        }

    }

    private String getDataByConfig(IObjectData data, CheckSuccess.Config subConfig,boolean isData) {
        String res = "";
        String field = isData?subConfig.getApiName():subConfig.getRuleApiName();
        switch (subConfig.getType()) {
            case "channel":
            case "select":
                res= data.get(field, String.class,"");
                break;
            case "dept":
                ArrayList dept = data.get(field, ArrayList.class, Lists.newArrayList());
                if(CollectionUtils.isNotEmpty(dept)) {
                    res=dept.get(0).toString();
                }
                break;
        }
        return res;
    }

    private void getResultMatchType0(CheckSuccess.Arg arg, CheckSuccess.Result result, IObjectData storeData) {
        SearchQuery.SearchQueryBuilder searchQuery = SearchQuery.builder();
        searchQuery.eq("state",true);
        if (StringUtils.isNotBlank(arg.getRecordType())){
            //业务类型 新版本匹配逻辑
            searchQuery.eq(BaseField.recordType.getApiName(), arg.getRecordType());
        }
        if(Objects.nonNull(storeData)){
            boolean flag = false;
            List<String> deptTemp = Lists.newArrayList();
            String deptApi = "";
            String channelApi ="";
            List<String> channelList = Lists.newArrayList();
            for (Map.Entry<String, CheckSuccess.Config> entry : arg.getSuccessConfig().entrySet()) {
                if(Objects.nonNull(entry.getValue()) && Objects.nonNull(entry.getValue().getApiName())) {
                    switch (entry.getValue().getType()) {
                        case "channel":
                        case "select":
                            String data = storeData.get(entry.getValue().getApiName(),String.class);
                            if(StringUtils.isNotEmpty(data)) {
                                if("ChannelObj".equals(entry.getValue().getObjApiName())){
                                    channelList.addAll(getChannelList(data));
                                    if(CollectionUtils.isNotEmpty(channelList)) {
                                        searchQuery.in(entry.getKey(), channelList);
                                        channelApi = entry.getKey();
                                    }else{
                                        searchQuery.eq(entry.getKey(), data);
                                    }
                                }else {
                                    searchQuery.eq(entry.getKey(), data);
                                }
                            }else{
                                searchQuery.notExist(entry.getKey());
                            }
                            flag = true;
                            break;
                        case "dept":
                            ArrayList dept = storeData.get(entry.getValue().getApiName(), ArrayList.class, Lists.newArrayList());
                            if(CollectionUtils.isNotEmpty(dept)) {
                                Map<String, List<String>> depts = serviceFacade.getAllSuperDeptIdsByDeptIds(controllerContext.getTenantId(), User.systemUser(controllerContext.getTenantId()).getUserId(),dept);
                                for (String depId: depts.get(dept.get(0))) {
                                    deptTemp.add(depId);
                                }
                                deptApi = entry.getKey();
                                searchQuery.inDepartmentNotSub(entry.getKey(), deptTemp);
                                flag = true;
                            }else{
                                searchQuery.notExist(entry.getKey());
                            }
                            break;
                    }
                }
            }
            //无条件不查
            if(flag) {
                List<IObjectData> data = baseDao.getAllIObjectDataListByQuery(User.systemUser(controllerContext.getTenantId()),searchQuery.build(), SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj);
                if (CollectionUtils.isNotEmpty(data)) {
                    if(data.size() == 1){
                        result.setId(data.get(0).getId());
                    }
                    final String deptApiFinal = deptApi;
                    final String channelApiFinal = channelApi;

                    if(data.size() > 1){
                        Collections.sort(data, new Comparator<IObjectData>() {
                            @Override
                            public int compare(IObjectData o1, IObjectData o2) {
                                Integer dept1 = getDeptSize(o1);
                                Integer dept2 = getDeptSize(o2);
                                int res = dept1.compareTo(dept2);
                                if(res!=0){
                                    return res;
                                }
                                if(CollectionUtils.isNotEmpty(channelList)){
                                    return getChannelSize(o1).compareTo(getChannelSize(o2));
                                }
                                return res ;
                            }

                            public Integer getDeptSize(IObjectData o){
                                List<String> dept = o.get(deptApiFinal, ArrayList.class, Lists.newArrayList());
                                if(CollectionUtils.isNotEmpty(deptTemp) && CollectionUtils.isNotEmpty(dept)) {
                                    int size =  deptTemp.indexOf(dept.get(0));
                                    return size==-1?deptTemp.size():size;
                                }
                                return deptTemp.size();
                            }

                            public Integer getChannelSize(IObjectData o){
                                String data = o.get(channelApiFinal,String.class,"");
                                if(StringUtils.isNotEmpty(data)) {
                                    int size = channelList.indexOf(data);
                                    return size==-1?channelList.size():size;
                                }
                                return channelList.size();
                            }
                        });
                        result.setId(data.get(0).getId());
                    }
                }
            }
        }
    }

    private CheckSuccess.Result handleCustomService(CheckSuccess.Arg arg, String custom) {
        CheckSuccess.Result result = new CheckSuccess.Result();
        JSONObject config = JSONObject.parseObject(custom);

        if(MapUtils.isNotEmpty(arg.getCustomData())){
            String objApi= config.getString("objApi");
            if(arg.getCustomData().containsKey(objApi)){
                IObjectData data = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()),arg.getCustomData().get(objApi),objApi);
                String succ = data.get(config.getString("field"),String.class);
                if(StringUtils.isNotEmpty(succ)){
                    result.setId(succ);
                }
            }
        }

        return result;
    }

    private List<String> getChannelList(String id) {
        List<String> res = Lists.newArrayList();
        if(StringUtils.isEmpty(id)){
            return res;
        }
        res.add(id);
        String channelData = redisUtils.getChannelData(controllerContext.getTenantId());
        JSONObject data = new JSONObject();
        if(StringUtils.isEmpty(channelData)) {
            SearchQuery.SearchQueryBuilder searchQuery = SearchQuery.builder();
            IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), "ChannelObj");
            searchQuery.limit(1000);
            List<IObjectData> channelObj = ObjectUtils.queryDataSimple(serviceFacade, User.systemUser(controllerContext.getTenantId()), "ChannelObj",searchQuery.build().getSearchTemplateQuery(),describe);
            if(CollectionUtils.isNotEmpty(channelObj)){
                for (IObjectData datum : channelObj) {
                    data.put(datum.getId(),datum.get("superior_channel",String.class,""));
                }
            }
            redisUtils.setChannelData(controllerContext.getTenantId(), data.toString());
        }else {
            data = JSON.parseObject(channelData);
        }
        getParentChannel(id,res,data);
        return res;
    }

    private void getParentChannel(String id, List<String> res, JSONObject data) {
        String parent = data.getString(id);
        if(StringUtils.isNotEmpty(parent)&& !res.contains(parent)){
            res.add(parent);
            getParentChannel(parent,res,data);
        }else{
            return;
        }
    }
}
