package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.api.success.CheckSuccess;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.constants.SuccessfulStoreRangeConstants;
import com.facishare.crm.fmcg.wq.constants.SuccessfulStoreRangeFields;
import com.facishare.crm.fmcg.wq.dao.BaseDao;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

public class SuccessfulStoreRangeCheckSuccessController extends PreDefineController<CheckSuccess.Arg, CheckSuccess.Result> {

    RedisUtils redisUtils = SpringUtil.getContext().getBean(RedisUtils.class);

    BaseDao baseDao = SpringUtil.getContext().getBean(BaseDao.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected CheckSuccess.Result doService(CheckSuccess.Arg arg) {
        CheckSuccess.Result result = new CheckSuccess.Result();
        String custom = ConfigFactory.getConfig("checkin-custom-config").get("successfulCustomCheck_"+controllerContext.getTenantId());
        if(StringUtils.isNotEmpty(custom)){
            return handleCustomService(arg,custom);
        }
        IObjectData storeData = serviceFacade.findObjectData(controllerContext.getUser(),arg.getStoreId(), CommonConstants.ACCOUNT_OBJ);
        try {
            if (arg.getMatchType() == 0) {
                getResultMatchType0(arg, result, storeData);//老的完全匹配
            } else {
                getResultMatchType1(arg, result, storeData); //模糊匹配
            }
        }catch (Exception e){
            log.error("CheckSuccess error arg:{} ",arg,e);
        }


        return result;
    }

    /**
     * 模糊匹配模式 (MatchType = 1)
     *
     * 匹配策略说明：
     * 1. 采用逐级降级匹配策略，优先匹配更多条件的规则
     * 2. 第一个字段作为保底条件，必须匹配
     * 3. 后续字段按顺序进行匹配，支持部分匹配
     * 4. 匹配优先级：完全匹配 > 部分匹配 > 保底匹配
     *
     * 示例：如果有ABCD四个匹配字段，可能的匹配规则优先级为：
     * ABCD (完全匹配) > ABC_ (三字段匹配) > AB__ (两字段匹配) > A___ (保底匹配)
     *
     * @param arg 检查成功参数，包含匹配字段配置
     * @param result 返回结果对象
     * @param storeData 门店数据对象
     */
    private void getResultMatchType1(CheckSuccess.Arg arg, CheckSuccess.Result result, IObjectData storeData) {
        // 验证匹配字段配置是否为空，如果为空则直接返回
        if(CollectionUtils.isEmpty(arg.getMatchFields())){
            return;
        }

        // 构建基础查询条件
        SearchQuery.SearchQueryBuilder searchQuery = SearchQuery.builder();

        // 添加业务类型条件（如果指定）
        if (StringUtils.isNotBlank(arg.getRecordType())){
            searchQuery.eq(BaseField.recordType.getApiName(), arg.getRecordType());
        }

        // 添加状态过滤条件：只查询启用且显示的规则
        searchQuery.eq(SuccessfulStoreRangeFields.STATE, SuccessfulStoreRangeFields.STATE_Options_true);
        searchQuery.eq(SuccessfulStoreRangeFields.DISPLAY_STATUS, SuccessfulStoreRangeFields.DISPLAY_STATUS_Options_1);

        // 处理第一个字段作为保底匹配条件（必须匹配）
        CheckSuccess.Config config = arg.getMatchFields().get(0);
        switch (config.getType()) {
            case "channel":
            case "select":
                // 处理渠道或选择类型字段
                String data = storeData.get(config.getApiName(),String.class);
                if(StringUtils.isNotEmpty(data)) {
                    searchQuery.eq(config.getRuleApiName(), data);
                }else{
                    // 如果门店数据为空，则匹配规则中该字段也为空的记录
                    searchQuery.notExist(config.getRuleApiName());
                }
                break;
            case "dept":
                // 处理部门类型字段
                ArrayList dept = storeData.get(config.getApiName(), ArrayList.class, Lists.newArrayList());
                if(CollectionUtils.isNotEmpty(dept)) {
                    searchQuery.inDepartmentNotSub(config.getRuleApiName(),dept);
                }else{
                    // 如果门店部门为空，则匹配规则中该字段也为空的记录
                    searchQuery.notExist(config.getRuleApiName());
                }
                break;
        }

        // 记录查询条件日志
        log.info( "searchQuery:{}", JSON.toJSONString(searchQuery.build()));

        // 执行查询获取符合保底条件的所有规则
        List<IObjectData> data = baseDao.getAllIObjectDataListByQuery(User.systemUser(controllerContext.getTenantId()),searchQuery.build(), SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj);

        if (CollectionUtils.isNotEmpty(data)) {
            // 如果只有一个匹配字段（保底规则），直接返回第一个匹配的规则
            if(arg.getMatchFields().size()==1){
                result.setId(data.get(0).getId());
                return;
            }

            // 预处理：提取门店数据中其他字段的值，用于后续匹配
            for (int i = 1; i < arg.getMatchFields().size(); i++) {
                CheckSuccess.Config subConfig = arg.getMatchFields().get(i);
                subConfig.setData(getDataByConfig(storeData,subConfig,true));
            }

            // 创建规则ID数组，索引对应匹配字段数量
            // ruleDataId[i] 存储匹配了前i+1个字段的最佳规则ID
            String[] ruleDataId = new String[arg.getMatchFields().size()];

            // 遍历所有符合保底条件的规则，进行逐级匹配
            for (IObjectData datum : data) {
                boolean isMatch = true;  // 标记当前规则是否持续匹配
                int matchSize = 0;       // 记录当前规则匹配的字段数量

                // 从第二个字段开始进行逐级匹配
                for (int i = 1; i < arg.getMatchFields().size(); i++) {
                    CheckSuccess.Config subConfig = arg.getMatchFields().get(i);

                    // 获取规则中对应字段的值
                    String ruleData = getDataByConfig(datum,subConfig,false);

                    if(isMatch && ruleData.equals(subConfig.getData())){
                        // 字段值完全匹配
                        matchSize++;
                        if(i==arg.getMatchFields().size()-1){
                            // 如果是最后一个字段，记录该规则ID
                            ruleDataId[matchSize]=datum.getId();
                        }
                    }else if(StringUtils.isEmpty(ruleData)){
                        // 规则中该字段为空，视为通配符，可以匹配任意值
                        if(i==arg.getMatchFields().size()-1){
                            // 如果是最后一个字段，记录该规则ID
                            ruleDataId[matchSize]=datum.getId();
                        }
                        isMatch = false; // 标记为非完全匹配
                    }else {
                        // 字段值不匹配且规则字段不为空，停止匹配该规则
                        break;
                    }
                }
            }

            // 查找最适配的规则：从匹配字段最多的开始查找
            log.info("storeId:{} ,ruleDataId:{}",arg.getStoreId(), Arrays.toString(ruleDataId));
            for (int i = ruleDataId.length - 1; i >= 0; i--) {
                if(StringUtils.isNotEmpty(ruleDataId[i])){
                    result.setId(ruleDataId[i]);
                    break;
                }
            }
        }
    }

    /**
     * 根据配置从数据对象中提取字段值
     *
     * @param data 数据对象（门店数据或规则数据）
     * @param subConfig 字段配置信息
     * @param isData true-从门店数据中提取，false-从规则数据中提取
     * @return 提取的字段值，如果为空则返回空字符串
     */
    private String getDataByConfig(IObjectData data, CheckSuccess.Config subConfig,boolean isData) {
        String res = "";
        // 根据isData标志选择对应的字段API名称
        String field = isData?subConfig.getApiName():subConfig.getRuleApiName();

        switch (subConfig.getType()) {
            case "channel":
            case "select":
                // 处理渠道或选择类型字段，直接获取字符串值
                res= data.get(field, String.class,"");
                break;
            case "dept":
                // 处理部门类型字段，部门通常是数组，取第一个元素
                ArrayList dept = data.get(field, ArrayList.class, Lists.newArrayList());
                if(CollectionUtils.isNotEmpty(dept)) {
                    res=dept.get(0).toString();
                }
                break;
        }
        return res;
    }

    /**
     * 完全匹配模式 (MatchType = 0)
     *
     * 匹配策略说明：
     * 1. 严格按照所有配置条件进行精确匹配
     * 2. 支持渠道层级匹配（包含上级渠道）
     * 3. 支持部门层级匹配（包含上级部门）
     * 4. 当匹配到多个规则时，按优先级排序选择最佳匹配
     *
     * 排序优先级：
     * 1. 部门层级越低（越具体）优先级越高
     * 2. 渠道层级越低（越具体）优先级越高
     *
     * @param arg 检查成功参数，包含成功配置
     * @param result 返回结果对象
     * @param storeData 门店数据对象
     */
    private void getResultMatchType0(CheckSuccess.Arg arg, CheckSuccess.Result result, IObjectData storeData) {
        // 构建基础查询条件
        SearchQuery.SearchQueryBuilder searchQuery = SearchQuery.builder();

        // 添加业务类型条件（新版本匹配逻辑）
        if (StringUtils.isNotBlank(arg.getRecordType())){
            searchQuery.eq(BaseField.recordType.getApiName(), arg.getRecordType());
        }

        // 添加状态过滤条件：只查询启用且显示的规则
        searchQuery.eq(SuccessfulStoreRangeFields.STATE, SuccessfulStoreRangeFields.STATE_Options_true);
        searchQuery.eq(SuccessfulStoreRangeFields.DISPLAY_STATUS, SuccessfulStoreRangeFields.DISPLAY_STATUS_Options_1);

        if(Objects.nonNull(storeData)){
            boolean flag = false;                    // 标记是否有有效的匹配条件
            List<String> deptTemp = Lists.newArrayList();  // 存储部门层级列表
            String deptApi = "";                     // 部门字段API名称
            String channelApi ="";                   // 渠道字段API名称
            List<String> channelList = Lists.newArrayList(); // 存储渠道层级列表

            // 遍历所有成功配置，构建查询条件
            for (Map.Entry<String, CheckSuccess.Config> entry : arg.getSuccessConfig().entrySet()) {
                if(Objects.nonNull(entry.getValue()) && Objects.nonNull(entry.getValue().getApiName())) {
                    switch (entry.getValue().getType()) {
                        case "channel":
                        case "select":
                            // 处理渠道或选择类型字段
                            String data = storeData.get(entry.getValue().getApiName(),String.class);
                            if(StringUtils.isNotEmpty(data)) {
                                // 特殊处理渠道对象：需要包含上级渠道
                                if("ChannelObj".equals(entry.getValue().getObjApiName())){
                                    channelList.addAll(getChannelList(data));
                                    if(CollectionUtils.isNotEmpty(channelList)) {
                                        // 使用IN查询匹配当前渠道及其所有上级渠道
                                        searchQuery.in(entry.getKey(), channelList);
                                        channelApi = entry.getKey();
                                    }else{
                                        // 如果获取渠道层级失败，则进行精确匹配
                                        searchQuery.eq(entry.getKey(), data);
                                    }
                                }else {
                                    // 非渠道对象进行精确匹配
                                    searchQuery.eq(entry.getKey(), data);
                                }
                            }else{
                                // 如果门店数据为空，则匹配规则中该字段也为空的记录
                                searchQuery.notExist(entry.getKey());
                            }
                            flag = true;
                            break;
                        case "dept":
                            // 处理部门类型字段
                            ArrayList dept = storeData.get(entry.getValue().getApiName(), ArrayList.class, Lists.newArrayList());
                            if(CollectionUtils.isNotEmpty(dept)) {
                                // 获取部门的所有上级部门
                                Map<String, List<String>> depts = serviceFacade.getAllSuperDeptIdsByDeptIds(controllerContext.getTenantId(), User.systemUser(controllerContext.getTenantId()).getUserId(),dept);
                                for (String depId: depts.get(dept.get(0))) {
                                    deptTemp.add(depId);
                                }
                                deptApi = entry.getKey();
                                // 使用部门层级查询（不包含子部门）
                                searchQuery.inDepartmentNotSub(entry.getKey(), deptTemp);
                                flag = true;
                            }else{
                                // 如果门店部门为空，则匹配规则中该字段也为空的记录
                                searchQuery.notExist(entry.getKey());
                            }
                            break;
                    }
                }
            }

            // 只有在有有效匹配条件时才执行查询
            if(flag) {
                List<IObjectData> data = baseDao.getAllIObjectDataListByQuery(User.systemUser(controllerContext.getTenantId()),searchQuery.build(), SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj);
                if (CollectionUtils.isNotEmpty(data)) {
                    // 如果只匹配到一个规则，直接返回
                    if(data.size() == 1){
                        result.setId(data.get(0).getId());
                    }

                    // 保存API名称供排序使用
                    final String deptApiFinal = deptApi;
                    final String channelApiFinal = channelApi;

                    // 如果匹配到多个规则，需要按优先级排序
                    if(data.size() > 1){
                        Collections.sort(data, new Comparator<IObjectData>() {
                            @Override
                            public int compare(IObjectData o1, IObjectData o2) {
                                // 首先按部门层级排序（层级越低优先级越高）
                                Integer dept1 = getDeptSize(o1);
                                Integer dept2 = getDeptSize(o2);
                                int res = dept1.compareTo(dept2);
                                if(res!=0){
                                    return res;
                                }
                                // 如果部门层级相同，再按渠道层级排序
                                if(CollectionUtils.isNotEmpty(channelList)){
                                    return getChannelSize(o1).compareTo(getChannelSize(o2));
                                }
                                return res ;
                            }

                            /**
                             * 计算部门层级大小
                             * @param o 规则数据对象
                             * @return 部门在层级列表中的索引，索引越小优先级越高
                             */
                            public Integer getDeptSize(IObjectData o){
                                List<String> dept = o.get(deptApiFinal, ArrayList.class, Lists.newArrayList());
                                if(CollectionUtils.isNotEmpty(deptTemp) && CollectionUtils.isNotEmpty(dept)) {
                                    int size =  deptTemp.indexOf(dept.get(0));
                                    return size==-1?deptTemp.size():size;
                                }
                                return deptTemp.size();
                            }

                            /**
                             * 计算渠道层级大小
                             * @param o 规则数据对象
                             * @return 渠道在层级列表中的索引，索引越小优先级越高
                             */
                            public Integer getChannelSize(IObjectData o){
                                String data = o.get(channelApiFinal,String.class,"");
                                if(StringUtils.isNotEmpty(data)) {
                                    int size = channelList.indexOf(data);
                                    return size==-1?channelList.size():size;
                                }
                                return channelList.size();
                            }
                        });
                        // 返回排序后的第一个（优先级最高的）规则
                        result.setId(data.get(0).getId());
                    }
                }
            }
        }
    }

    private CheckSuccess.Result handleCustomService(CheckSuccess.Arg arg, String custom) {
        CheckSuccess.Result result = new CheckSuccess.Result();
        JSONObject config = JSONObject.parseObject(custom);

        if(MapUtils.isNotEmpty(arg.getCustomData())){
            String objApi= config.getString("objApi");
            if(arg.getCustomData().containsKey(objApi)){
                IObjectData data = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()),arg.getCustomData().get(objApi),objApi);
                String succ = data.get(config.getString("field"),String.class);
                if(StringUtils.isNotEmpty(succ)){
                    result.setId(succ);
                }
            }
        }

        return result;
    }

    private List<String> getChannelList(String id) {
        List<String> res = Lists.newArrayList();
        if(StringUtils.isEmpty(id)){
            return res;
        }
        res.add(id);
        String channelData = redisUtils.getChannelData(controllerContext.getTenantId());
        JSONObject data = new JSONObject();
        if(StringUtils.isEmpty(channelData)) {
            SearchQuery.SearchQueryBuilder searchQuery = SearchQuery.builder();
            IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), "ChannelObj");
            searchQuery.limit(1000);
            List<IObjectData> channelObj = ObjectUtils.queryDataSimple(serviceFacade, User.systemUser(controllerContext.getTenantId()), "ChannelObj",searchQuery.build().getSearchTemplateQuery(),describe);
            if(CollectionUtils.isNotEmpty(channelObj)){
                for (IObjectData datum : channelObj) {
                    data.put(datum.getId(),datum.get("superior_channel",String.class,""));
                }
            }
            redisUtils.setChannelData(controllerContext.getTenantId(), data.toString());
        }else {
            data = JSON.parseObject(channelData);
        }
        getParentChannel(id,res,data);
        return res;
    }

    private void getParentChannel(String id, List<String> res, JSONObject data) {
        String parent = data.getString(id);
        if(StringUtils.isNotEmpty(parent)&& !res.contains(parent)){
            res.add(parent);
            getParentChannel(parent,res,data);
        }else{
            return;
        }
    }
}
