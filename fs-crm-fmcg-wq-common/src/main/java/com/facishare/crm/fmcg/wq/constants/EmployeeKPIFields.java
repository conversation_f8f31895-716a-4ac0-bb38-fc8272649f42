package com.facishare.crm.fmcg.wq.constants;

public abstract class EmployeeKPIFields {

	private EmployeeKPIFields(){};

	public static final String DISPLAY_NAME = "员工绩效指标";

	public static final String API_NAME = "EmployeeKPIObj";

	 //聚合对象
	public static final String AGGREGATED_OBJECT = "aggregated_object";

	 //指标计算方式
	public static final String INDICATOR_CALC_METHOD = "indicator_calc_method";

		 //对象
		public static final String INDICATOR_CALC_METHOD_Options_1 = "1";

		 //月度考勤表
		public static final String INDICATOR_CALC_METHOD_Options_2 = "2";

		 //APL函数
		public static final String INDICATOR_CALC_METHOD_Options_3 = "3";

	 //聚合人员字段
	public static final String AGGREGATED_PERSON_FIELD = "aggregated_person_field";

	 //聚合字段
	public static final String AGGREGATED_FIELD = "aggregated_field";

	 //APL信息
	public static final String APL_INFO = "apl_info";

	 //指标描述
	public static final String INDICATOR_DESC = "indicator_desc";

	 //聚合函数
	public static final String AGGREGATION_FUNCTION = "aggregation_function";

	 //聚合日期字段
	public static final String AGGREGATED_DATE_FIELD = "aggregated_date_field";

	public static final String WHERES = "wheres";


	//月度考勤表字段
	public static final String MONTHLY_ATTENDANCE_FIELD = "monthly_attendance_field";

		 //应出勤天数（天）
		public static final String MONTHLY_ATTENDANCE_FIELD_Options_ruleDaysNum = "ruleDaysNum";

		 //正常出勤（天）
		public static final String MONTHLY_ATTENDANCE_FIELD_Options_checkDayNum = "checkDayNum";

		 //旷工（天）
		public static final String MONTHLY_ATTENDANCE_FIELD_Options_absentDays = "absentDays";

		 //外勤（天）
		public static final String MONTHLY_ATTENDANCE_FIELD_Options_waiQinDaysNum = "waiQinDaysNum";

		 //实际工作时长（小时）
		public static final String MONTHLY_ATTENDANCE_FIELD_Options_checkWorkTime = "checkWorkTime";

		 //加班时长（小时）
		public static final String MONTHLY_ATTENDANCE_FIELD_Options_overTime = "overTime";

		 //迟到（次）
		public static final String MONTHLY_ATTENDANCE_FIELD_Options_laterNum = "laterNum";

		 //迟到（分钟）
		public static final String MONTHLY_ATTENDANCE_FIELD_Options_laterTime = "laterTime";

		 //早退（次）
		public static final String MONTHLY_ATTENDANCE_FIELD_Options_earlyNum = "earlyNum";

		 //早退（分钟）
		public static final String MONTHLY_ATTENDANCE_FIELD_Options_earlyTime = "earlyTime";

		 //未打卡（次）
		public static final String MONTHLY_ATTENDANCE_FIELD_Options_missNum = "missNum";

		 //不在考勤范围（次）
		public static final String MONTHLY_ATTENDANCE_FIELD_Options_locationExNum = "locationExNum";

}