package com.facishare.crm.fmcg.wq.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Maps;
import com.facishare.appserver.checkins.model.common.CheckinsFields;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

public class ObjectUtils {

    public final static List<ObjectAction> buttonList = Lists.newArrayList(ObjectAction.AREA_BATCH_ADD,ObjectAction.AREA_BATCH_EDIT,ObjectAction.AREA_MERGE);

    public static List<String> fixFields = Lists.newArrayList("belong_area","name","location","owner","customer_label","account_level","last_visit_closed_time","visit_frequency","route_ids");

    public static final String CUSTOMER_LEVEL = "customer_label";

    public static List<String> getShowFiledApiNameFromListLayout(ILayout layout){
        List<String> res = Lists.newArrayList();

        try {
            IComponent component = layout.getComponents().get(0);
            if (component instanceof TableComponent) {
                List<IFieldSection> sections = ((TableComponent) component).getFieldSections();
                for (IFormField field : sections.get(0).getFields()) {
                    res.add(field.getFieldName());
                }
            }
        } catch (MetadataServiceException e) {
            throw new RuntimeException(e);
        }
        return res;
    }

    /**
     * 返回__r __l数据
     * @param serviceFacade
     * @param user
     * @param context
     * @param describe
     * @param query
     * @param fields
     * @return
     */
    public static List<IObjectData> queryDataWithFieldInfo(ServiceFacade serviceFacade, User user, RequestContext context, IObjectDescribe describe, SearchTemplateQuery query, List<String> fields) {
        int max = query.getLimit() == 0 || query.getLimit() == -1 ? Integer.MAX_VALUE : query.getLimit();

        int limit = Math.min(500, max);
        int offset = 0;

        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        List<IObjectData> data = Lists.newArrayList();
        QueryResult<IObjectData> result;

        SearchTemplateQuery innerQuery = copy(query);
        while (data.size() < max && !(result = serviceFacade.findBySearchTemplateQueryWithFields(ActionContextExt.of(user, context).getContext(), describe.getApiName(), innerQuery, fields)).getData().isEmpty()) {
            serviceFacade.fillUserInfo(describe,result.getData(),user);
            serviceFacade.fillObjectDataWithRefObject(describe,result.getData(),user);
            data.addAll(result.getData());
            offset += result.getData().size();

            innerQuery = copy(query);
            innerQuery.setOffset(offset);
            if (result.getData().size() < limit) {
                break;
            }
        }
        return data;
    }
    public static Optional<String> parseEmployeeValue(IObjectData iObjectData,String apiName) {
        Object owner = iObjectData.get(apiName);
        String ret = null;
        if (Objects.nonNull(owner)) {
            if (owner instanceof List) {
                List ownerList = (List)owner;
                if (!ownerList.isEmpty()) {
                    ret = String.valueOf(ownerList.get(0));
                }
            } else if (owner instanceof String) {
                ret = (String)owner;
            } else {
                if (!(owner instanceof Integer)) {
                    throw new MetaDataException(SystemErrorCode.METADATA_OWNER_ID_ERROR);
                }

                ret = String.valueOf(owner);
            }
        }

        return Optional.ofNullable(ret);
    }
    public static List<IObjectData> queryData(ServiceFacade serviceFacade, User user, RequestContext context, String apiName, SearchTemplateQuery query, List<String> fields) {
        int max = query.getLimit() == 0 || query.getLimit() == -1 ? Integer.MAX_VALUE : query.getLimit();

        int limit = Math.min(250, max);
        int offset = 0;

        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        List<IObjectData> data = Lists.newArrayList();
        QueryResult<IObjectData> result;

        SearchTemplateQuery innerQuery = copy(query);
        while (data.size() < max && !(result = serviceFacade.findBySearchTemplateQueryWithFields(ActionContextExt.of(user, context).getContext(), apiName, innerQuery, fields)).getData().isEmpty()) {

            data.addAll(result.getData());
            offset += result.getData().size();

            innerQuery = copy(query);
            innerQuery.setOffset(offset);
        }
        return data;
    }

    public static List<IObjectData> queryData(ServiceFacade serviceFacade, User user, String apiName, SearchTemplateQuery query) {
        int maxSize = query.getLimit() == 0 || query.getLimit() == -1 ? 5000 : query.getLimit();
        int limit = Math.min(500, maxSize);
        int offset = 0;
        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        List<IObjectData> dataList = Lists.newArrayList();
        QueryResult<IObjectData> result;
        SearchTemplateQuery templateQuery = copy(query);
        while (dataList.size() < maxSize && !(result = serviceFacade.findBySearchQuery(user, apiName, templateQuery)).getData().isEmpty()) {
            dataList.addAll(result.getData());
            offset += result.getData().size();
            templateQuery = copy(query);
            templateQuery.setOffset(offset);
        }
        return dataList;
    }
    public static List<IObjectData> queryDataSimple(ServiceFacade serviceFacade, User user, String apiName, SearchTemplateQuery query, IObjectDescribe describe) {
        int maxSize = query.getLimit() == 0 || query.getLimit() == -1 ? 5000 : query.getLimit();
        int limit = Math.min(500, maxSize);
        int offset = 0;
        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        List<IObjectData> dataList = Lists.newArrayList();
        QueryResult<IObjectData> result;
        SearchTemplateQuery templateQuery = copy(query);
        while (dataList.size() < maxSize && !(result = serviceFacade.findBySearchQuery(user,describe, apiName, templateQuery,true)).getData().isEmpty()) {
            dataList.addAll(result.getData());
            offset += result.getData().size();
            templateQuery = copy(query);
            templateQuery.setOffset(offset);
        }
        return dataList;
    }
    public static int getDataTotal(ServiceFacade serviceFacade, User user, String apiName, SearchTemplateQuery query) {
        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(true);
        QueryResult<IObjectData>  result = serviceFacade.findBySearchQuery(user, apiName, query);
        return result.getTotalNumber();
    }


    public static SearchTemplateQuery copy(SearchTemplateQuery query) {
        SearchTemplateQuery clone = new SearchTemplateQuery();
        clone.setFilters(query.getFilters());
        clone.setOrders(query.getOrders());
        clone.setWheres(query.getWheres());
        clone.setLimit(query.getLimit());
        clone.setOffset(query.getOffset());
        clone.setPattern(query.getPattern());
        clone.setSearchSource(query.getSearchSource());
        return clone;
    }


    public static Map<String,String> getValueAndColorByDesc(IFieldDescribe iFieldDescribe){
        Map<String,String> accountValueAndLevelMap = Maps.newHashMap();
        if(Objects.nonNull(iFieldDescribe)){
            List<Map<String, String>> optionArray = (List<Map<String, String>>) iFieldDescribe.get("options");
            if (!CollectionUtils.isEmpty(optionArray)) {
                optionArray.forEach(v -> accountValueAndLevelMap.put(v.get("label"), v.get("font_color")));
            }
        }
        return accountValueAndLevelMap;
    }

    public static String buildManyRelationValue(IObjectData objectData,String apiName){
        StringBuilder sb = new StringBuilder();
        Object relation = objectData.get(apiName);
        if(Objects.nonNull(relation)){
            if(relation instanceof List){
                List<?> relationList = (List<?>) relation;
                if (!relationList.isEmpty()) {
                    if(relationList.get(0) instanceof HashMap) {
                        List<HashMap> jsonList = (List<HashMap>) relationList;
                        jsonList.forEach(v -> sb.append(v.get("name")).append(","));
                    } else if(relationList.get(0) instanceof JSONObject) {
                        List<JSONObject> jsonList = (List<JSONObject>) relationList;
                        jsonList.forEach(v -> sb.append(v.get("name")).append(","));
                    }
                }
            }
        }
        if(sb.length() > 0){
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    public static String buildManySelectValue(IObjectData objectData,String apiName){
        StringBuilder sb = new StringBuilder();
        Object relation = objectData.get(apiName);
        if(Objects.nonNull(relation)){
            if(relation instanceof List){
                List<?> relationList = (List<?>) relation;
                if (!relationList.isEmpty()) {
                    if(relationList.get(0) instanceof String) {
                        List<String> jsonList = (List<String>) relationList;
                        jsonList.forEach(v -> sb.append(v).append("、"));
                    }
                }
            }
        }
        if(sb.length() > 0){
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }
    public static String transitionField(CheckinsFields checkinsFields, IObjectData jsonObject, IFieldDescribe jsonObjectDesc){
        String fieldApiName = checkinsFields.getFieldApiName();
        String value = "";
        String date_format;
        if (Objects.isNull(jsonObject) || Objects.isNull(jsonObjectDesc) || "check_out_time".equals(fieldApiName) || "checkins_time".equals(fieldApiName) || "duration_formula".equals(fieldApiName)
                || "new_stat_date".equals(fieldApiName) || "status".equals(fieldApiName) || "checkin_status".equals(fieldApiName)) {
            return "";
        }
        Object valueObj = jsonObject.get(fieldApiName);
            switch (checkinsFields.getType()) {
                case "image":
                    try {
                        value = JSONObject.toJSONString (valueObj);
                    }catch (Exception e){}
                    break;
                case "true_or_false":
                    value = BooleanUtils.isNotTrue((boolean)jsonObject.get(fieldApiName)) ? "否" : "是"; //ignoreI18n
                    break;
                case "location":
                    if (Objects.nonNull(valueObj)) {
                        String[] info = valueObj.toString().split("\\#\\%\\$");
                        if (info.length > 2) {
                            value = info[2];
                        }
                    }
                    break;
                case "time":
                case "date_time":
                case "date":
                    date_format = (String)jsonObjectDesc.get("date_format");
                    if (valueObj instanceof Double) {
                        Double doubleValue = (Double) valueObj;
                        value = (0L == doubleValue ? "" : DateUtils.getStringFromTime(doubleValue.longValue(), date_format));
                    } else if (valueObj instanceof Long) {
                        value = (0L == (Long) valueObj ? "" : DateUtils.getStringFromTime((Long) valueObj, date_format));
                    } else if (valueObj instanceof Integer) {
                        value = (0L == (Integer) valueObj ? "" : DateUtils.getStringFromTime(((Integer) valueObj).longValue(), date_format));
                    }
                    break;
                case "percentile":
                    String percentValue = (String)valueObj;
                    if (StringUtils.isNotEmpty(percentValue)) {
                        value = (percentValue + "%");
                    }
                    break;
                case "select_one":
                    if (Objects.nonNull(valueObj)) {
                        List oneOptionsArray = (List) jsonObjectDesc.get("options");
                        for (int i = 0; i < oneOptionsArray.size(); i++) {
                            Map data = (Map) oneOptionsArray.get(i);
                            if (data.get("value").equals(valueObj)) {
                                if (data.get("value").equals("other")) {
                                    Object otherValue = jsonObject.get(fieldApiName + "__o");
                                    if (Objects.nonNull(otherValue)) {
                                        value = data.get("label") + ":" + otherValue.toString();
                                    } else {
                                        value = (String)data.get("label");
                                    }
                                }else {
                                    value = (String)data.get("label");
                                }
                            }
                        }
                    }
                    break;
                case "record_type":
                    List recordTypeArray = (List) jsonObjectDesc.get("options");
                    for (int i = 0; i < recordTypeArray.size(); i++) {
                        Map data = (Map) recordTypeArray.get(i);
                        if (data.get("api_name").equals(valueObj)) {
                            value = (String)data.get("label");
                        }
                    }
                    break;
                case "select_many":
                    if (Objects.nonNull(valueObj)) {
                        List oneOptionsArray = (List) jsonObjectDesc.get("options");
                        boolean isMatched = false;
                        for (int i = 0; i < oneOptionsArray.size(); i++) {
                            Map data = (Map) oneOptionsArray.get(i);
                            List<Object> values = (List) valueObj;
                            if (CollectionUtils.isNotEmpty(values)) {
                                for (Object value1 : values) {
                                    if (data.get("value").equals(value1)) {
                                        if (data.get("value").equals("other")) {
                                            Object otherValue = jsonObject.get(fieldApiName + "__o");
                                            if (Objects.nonNull(otherValue)) {
                                                value += (data.get("label") + ":" + otherValue + (","));
                                            } else {
                                                value += (data.get("label") + (","));
                                            }
                                        }else{
                                            value += (data.get("label") + (","));
                                        }
                                        isMatched = true;
                                    }
                                }
                            }
                        }
                        if (isMatched) {
                            value = value.substring(0,value.length() - 1);
                        }
                    }
                    break;
                case "employee":
                case "employee_many":
                    if (Objects.nonNull(valueObj)) {
                        List jsonArray = (List) jsonObject.get(fieldApiName + "__l");
                        if (Objects.nonNull(jsonArray)) {
                            for (int i = 0; i < jsonArray.size(); i++) {
                                UserInfo map = (UserInfo)jsonArray.get(i);
                                value += (map.getName() + (";"));
                            }
                        }
                        if(value.toString().contains(";")){
                            value = value.substring(0,value.length() - 1);
                        }
                    }
                    break;
                case "department":
                case "department_many":
                    if (Objects.nonNull(valueObj)) {
                        List jsonArray = (List) jsonObject.get(fieldApiName + "__l");
                        if (Objects.nonNull(jsonArray)) {
                            for (int i = 0; i < jsonArray.size(); i++) {
                                QueryDeptInfoByDeptIds.DeptInfo data = (QueryDeptInfoByDeptIds.DeptInfo)jsonArray.get(i);
                                value += (data.getDeptName() + (";"));
                            }
                        }
                        if(value.toString().contains(";")){
                            value = value.substring(0,value.length() - 1);
                        }

                    }
                    break;
                case "object_reference":
                case "country":
                case "province":
                case "city":
                case "district":
                case "town":
                    if (Objects.nonNull(valueObj)) {
                        value = (String) jsonObject.get(fieldApiName + "__r");
                    }
                    break;
                case "object_reference_many":
                    List<String> keys = (List) valueObj;
                    if(CollectionUtils.isNotEmpty(keys)){
                        Object referenceV = jsonObject.get(fieldApiName+"__r");
                        if(Objects.nonNull(referenceV)) {
                            StringBuffer stringBuffer = new StringBuffer();
                            List array = (List)referenceV;
                            for (int i = 0; i < array.size(); i++) {
                                Map data = (Map)array.get(i);
                                stringBuffer.append(data.get("name")).append(",");
                            }
                            value = stringBuffer.substring(0, stringBuffer.length() - 1);
                        }
                    }
                    break;
                default:
                    if("created_by".equals(fieldApiName)){
                        if (Objects.nonNull(valueObj)) {
                            value = ((JSONArray) jsonObject.get(fieldApiName + "__l")).getJSONObject(0).getString("name");
                        }
                    }else {
                        value = (String) jsonObject.get(fieldApiName);
                    }
                    break;
            }
        if(Objects.isNull(value)){
            return "";
        }
        return value;
    }
}
