package com.facishare.crm.fmcg.wq.exception;

import com.facishare.appserver.utils.I18nZhCNEnum;
import com.facishare.paas.appframework.core.exception.ErrorCode;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.logging.log4j.message.Message;

import java.text.MessageFormat;

/**
 * Created by zhangsm on 2018/4/9/0009.
 */
@AllArgsConstructor
public enum CheckinsErrorCode implements ErrorCode {
    UNSUPPORTED(305_002_019,"不支持此操作","不支持此操作"),
    GO_TO_WAIQIN(305_002_018,I18nZhCNEnum.oaappsrv_waiqin_msg_checkinspreobjunsupport.getI18nKey(),I18nZhCNEnum.oaappsrv_waiqin_msg_checkinspreobjunsupport.getName()),
//    REPEAT_INSERT(305_002_019,"data_Id"),
//    不支持该操作
    UNSUPPORTED_OPERATION(305_002_017,I18nZhCNEnum.oaappsrv_waiqin_msg_checkinspreobjunsupport.getI18nKey(),I18nZhCNEnum.oaappsrv_waiqin_msg_checkinspreobjunsupport.getName()),
    GO_TO_630_WAIQIN(305_002_020,"请使用6.3版本以上外勤，执行该操作","请使用6.3版本以上外勤，执行该操作");


    int code;
    String i18nKey;
    String defaultMessage;

    CheckinsErrorCode(int code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    @Override
    public int getCode() {
        return code;
    }

    public String getMessage() {
        return I18NExt.text(i18nKey,defaultMessage);
    }
    public String getMessage(Object[] datas) {
        return MessageFormat.format(getMessage(),datas);
    }
}
