package com.facishare.crm.fmcg.wq.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: fs-crm-fmcg
 * @description: 供货门店
 * @author: zhangsm
 * @create: 2021-04-25 21:52
 **/
public interface AccountObjConstants {
    String API_NAME = "AccountObj";
    String DISPLAY_NAME = "门店"; //ignoreI18n
    @AllArgsConstructor
    @Getter
    enum Field{
        ownDepartment("data_own_department","归属部门"), //ignoreI18n
        upDistributorIds("up_distributors__c","上级配送商"), //ignoreI18n
        upDistributorDesc("up_distributors_name__c","上级配送商描述"), //ignoreI18n
        upDealerIds("up_dealers__c","上级经销商Ids"), //ignoreI18n
        upDealerDesc("up_dealers_name__c","上级经销商描述"), //ignoreI18n
        otherDepartment("cross_regional_supply__c","跨区域供货"), //ignoreI18n
        belongArea("belong_area","所属片区"), //ignoreI18n
        areaOwner("area_owner","片区负责人"), //ignoreI18n
        location("location","定位"), //ignoreI18n
        sellers("sellers__c","销售商"), //ignoreI18n
        coveringSalesAreas("covering_sales_areas","销售区域"), //ignoreI18n
        isOurEnterprise("isOurEnterprise__c","本企业"), //ignoreI18n
        accountNo("account_no__c","客户编码"), //ignoreI18n
        CONTROL_SALES_AREA("control_sales_area__c","是否控制销售区域"); //ignoreI18n

        /**
         * apiName
         */
        private final String apiName;


        /**
         * 标签名
         */
        private final String label;
    }
    @AllArgsConstructor
    @Getter
    enum Value{
        default__c("门店"), //ignoreI18n
        distributor__c("分销邮差"), //ignoreI18n
        dealer__c("经销商"), //ignoreI18n
        ;

        /**
         *
         */
        private String disName;
        public static Value of(String value){
            for (Value fValue : values()) {
                if (fValue.name().equals(value)){
                    return fValue;
                }
            }
            return null;
        }
    }
}
