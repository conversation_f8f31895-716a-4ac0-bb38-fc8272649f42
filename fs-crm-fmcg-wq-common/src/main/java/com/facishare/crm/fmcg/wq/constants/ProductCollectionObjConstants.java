package com.facishare.crm.fmcg.wq.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: fs-crm-fmcg
 * @description: 经营范围 字段
 * @author: zhangsm
 * @create: 2021-04-25 21:52
 **/
public interface ProductCollectionObjConstants {
    String API_NAME = "ProductCollectionObj";
    String DISPLAY_NAME = "经营范围"; //ignoreI18n
    @AllArgsConstructor
    @Getter
    enum Value{
        businessScopeType_1("option1","全部"), //ignoreI18n
        businessScopeType_2("option2","继承服务处经营范围"), //ignoreI18n
        businessScopeType_3("option3","继承上级经销商经营范围"), //ignoreI18n
        businessScopeType_4("option4","指定产品"), //ignoreI18n
        recordType_distributor("default__c","分销邮差商经营品项"), //ignoreI18n
        recordType_dealer("dealer__c","经销商经营品项"), //ignoreI18n
        recordType_server_center("server_center__c","服务处经营品项"); //ignoreI18n

        /**
         *
         */
        private String value;
        /**
         * 标签名
         */
        private final String label;
       public static boolean isInheritUpProduct(String businessScopeType){
            if (Value.businessScopeType_4.value.equals(businessScopeType)){
                return false;
            }else {
                return true;
            }
        }
        public static  boolean isServerCenter(String type){
           return recordType_server_center.getValue().equals(type);
        }
        public static  boolean isDealer(String type){
            return recordType_dealer.getValue().equals(type);
        }
        public static  boolean isDistributor(String type){
            return recordType_distributor.getValue().equals(type);
        }
    }

    @AllArgsConstructor
    @Getter
    enum Field{
        //经营范围
        businessScopeText("field_aTT2p__c","经营范围"), //ignoreI18n
        //服务处
        departmentId("service_center__c","服务处"), //ignoreI18n
        salesDepartmentId("sales_department","营业部"), //ignoreI18n
        //经销商
        dealerId("dealer","经销商"), //ignoreI18n
        //配送商 accountId
        distributorId("distribution","配送商"), //ignoreI18n
        //是否是所有上级产品
        businessScopeType("business_scope_type","经营范围类型"), //ignoreI18n

        //上级范围Id
        upBusinessScopeId("field_31KSo__c","上级范围id"); //ignoreI18n
        /**
         * apiName
         */
        private final String apiName;


        /**
         * 标签名
         */
        private final String label;
    }
}
