package com.facishare.crm.fmcg.wq.constants;

public abstract class SummaryDisplayAchievementFields {

	private SummaryDisplayAchievementFields(){};

	public static final String DISPLAY_NAME = "陈列形式达成总结"; //ignoreI18n

	public static final String API_NAME = "SummaryDisplayAchievementObj";

	 //陈列形式
	public static final String RELATED_DISPLAY_ACHIEVEMENT = "related_display_achievement";
	public static final String RELATED_DISPLAY_ACHIEVEMENT_NAME = "related_display_achievement__r";

	 //达成项总结
	public static final String ACHIEVEMENT_SUMMARY = "achievement_summary";
	public static final String ACHIEVEMENT_SUMMARY_NAME = "achievement_summary__r";

		 //项目全部达标c
		public static final String ACHIEVEMENT_SUMMARY_Options_1 = "1";

		 //项目部分达标
		public static final String ACHIEVEMENT_SUMMARY_Options_2 = "2";

		 //物料项目达标
		public static final String ACHIEVEMENT_SUMMARY_Options_3 = "3";

		 //产品项目达标
		public static final String ACHIEVEMENT_SUMMARY_Options_4 = "4";

		 //产品SKU种类达标
		public static final String ACHIEVEMENT_SUMMARY_Options_5 = "5";

		 //物料SKU种类达标
		public static final String ACHIEVEMENT_SUMMARY_Options_6 = "6";

		 //产品排面数达标
		public static final String ACHIEVEMENT_SUMMARY_Options_7 = "7";

		 //产品层数达标
		public static final String ACHIEVEMENT_SUMMARY_Options_8 = "8";

		 //其他
		public static final String ACHIEVEMENT_SUMMARY_Options_other = "other";

	 //要求位置
	public static final String REQUIRED_LOCATION = "required_location";

		 //主通道
		public static final String REQUIRED_LOCATION_Options_1 = "1";

		 //门店显眼位
		public static final String REQUIRED_LOCATION_Options_2 = "2";

		 //入口处
		public static final String REQUIRED_LOCATION_Options_3 = "3";

		 //端头货架
		public static final String REQUIRED_LOCATION_Options_4 = "4";

		 //堆头陈列区
		public static final String REQUIRED_LOCATION_Options_5 = "5";

		 //收银台附近
		public static final String REQUIRED_LOCATION_Options_6 = "6";

		 //冰柜和冷藏区
		public static final String REQUIRED_LOCATION_Options_7 = "7";

		 //竞品旁边
		public static final String REQUIRED_LOCATION_Options_8 = "8";

		 //其他
		public static final String REQUIRED_LOCATION_Options_other = "other";

	 //陈列照
	public static final String DISPLAY_PHOTO = "display_photo";

	 //实际位置
	public static final String ACTUAL_LOCATION = "actual_location";

		 //主通道
		public static final String ACTUAL_LOCATION_Options_1 = "1";

		 //门店显眼位
		public static final String ACTUAL_LOCATION_Options_2 = "2";

		 //入口处
		public static final String ACTUAL_LOCATION_Options_3 = "3";

		 //端头货架
		public static final String ACTUAL_LOCATION_Options_4 = "4";

		 //堆头陈列区
		public static final String ACTUAL_LOCATION_Options_5 = "5";

		 //收银台附近
		public static final String ACTUAL_LOCATION_Options_6 = "6";

		 //冰柜和冷藏区
		public static final String ACTUAL_LOCATION_Options_7 = "7";

		 //竞品旁边
		public static final String ACTUAL_LOCATION_Options_8 = "8";

		 //其他
		public static final String ACTUAL_LOCATION_Options_other = "other";

	 //未达成项项总结
	public static final String SUMMARY_ISSUES = "summary_issues";
	public static final String SUMMARY_ISSUES_NAME = "summary_issues__r";
		 //产品项目达标
		public static final String SUMMARY_ISSUES_Options_1 = "1";

		 //产品SKU种类不足
		public static final String SUMMARY_ISSUES_Options_2 = "2";

		 //物料SKU种类不足
		public static final String SUMMARY_ISSUES_Options_3 = "3";

		 //产品排面数不足
		public static final String SUMMARY_ISSUES_Options_4 = "4";

		 //产品层数数不足
		public static final String SUMMARY_ISSUES_Options_5 = "5";

		 //产品组数不足
		public static final String SUMMARY_ISSUES_Options_6 = "6";

		 //部分层未达标
		public static final String SUMMARY_ISSUES_Options_7 = "7";

		 //其他
		public static final String SUMMARY_ISSUES_Options_other = "other";

	 //是否按层
	public static final String DEFINE_LAYER = "define_layer";

		 //否
		public static final String DEFINE_LAYER_Options_0 = "0";

		 //是
		public static final String DEFINE_LAYER_Options_1 = "1";

		 //其他
		public static final String DEFINE_LAYER_Options_other = "other";

	 //规则编码
	public static final String RULE_GROUP_ID = "rule_group_id";

	 //达成状态
	public static final String ACHIEVED_RESULTS = "achieved_results";
	public static final String ACHIEVED_RESULTS__R = "achieved_results__r";

		 //达标
		public static final String ACHIEVED_RESULTS_Options_1 = "1";

		 //部分达标
		public static final String ACHIEVED_RESULTS_Options_2 = "2";

		 //未达标
		public static final String ACHIEVED_RESULTS_Options_3 = "3";

		 //未陈列
		public static final String ACHIEVED_RESULTS_Options_4 = "4";

		 //待审
		public static final String ACHIEVED_RESULTS_Options_5 = "5";

		 //其他
		public static final String ACHIEVED_RESULTS_Options_other = "other";

	 //关联报告
	public static final String RELATED_REPORT = "related_report";

}
