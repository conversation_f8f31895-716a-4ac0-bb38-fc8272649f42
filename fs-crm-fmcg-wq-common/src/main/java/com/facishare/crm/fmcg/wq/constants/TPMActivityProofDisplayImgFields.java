package com.facishare.crm.fmcg.wq.constants;

public abstract class TPMActivityProofDisplayImgFields {

	private TPMActivityProofDisplayImgFields(){};

	public static final String DISPLAY_NAME = "活动举证陈列图片"; //ignoreI18n

	public static final String API_NAME = "TPMActivityProofDisplayImgObj";

	 //举证图片
	public static final String IMAGE = "image";

	 //系统判定结果
	public static final String SYSTEM_JUDGMENT_STATUS = "system_judgment_status";

		 //未陈列
		public static final String SYSTEM_JUDGMENT_STATUS_Options_not_display = "not_display";

		 //达标
		public static final String SYSTEM_JUDGMENT_STATUS_Options_pass = "pass";

		 //不达标
		public static final String SYSTEM_JUDGMENT_STATUS_Options_fail = "fail";

		 //部分达标
		public static final String SYSTEM_JUDGMENT_STATUS_Options_partial_pass = "partial_pass";

		//待审核
		public static final String SYSTEM_JUDGMENT_STATUS_Options_pending_approval = "pending_approval";

	 //AI识别层数
	public static final String AI_LAYER_NUMBER = "ai_layer_number";

	 //AI识别组数
	public static final String AI_GROUP_NUMBER = "ai_group_number";

	 //实际陈列位置
	public static final String ACTUAL_DISPLAY_POSITION = "actual_display_position";

	 //标准说明
	public static final String STANDARD_DESCRIPTION = "standard_description";

	 //AI陈列形式
	public static final String AI_DISPLAY_FORM_ID = "ai_display_form_id";

	 //举证项目
	public static final String ACTIVITY_ITEM_ID = "activity_item_id";

	 //检核结果
	public static final String AUDIT_STATUS = "audit_status";

		 //未检核
		public static final String AUDIT_STATUS_Options_not_audit = "not_audit";

		 //合格
		public static final String AUDIT_STATUS_Options_pass = "pass";

		 //不合格
		public static final String AUDIT_STATUS_Options_fail = "fail";

		 //部分合格
		public static final String AUDIT_STATUS_Options_partial_pass = "partial_pass";

	 //标准陈列位置
	public static final String STANDARD_DISPLAY_POSITION = "standard_display_position";

	 //活动举证
	public static final String ACTIVITY_PROOF_ID = "activity_proof_id";

	 //举证陈列形式
	public static final String DISPLAY_FORM_ID = "display_form_id";

}
