package com.facishare.crm.fmcg.wq.constants;

public abstract class TPMActivityDetailFields {

	private TPMActivityDetailFields(){};

	public static final String DISPLAY_NAME = "参与活动项目"; //ignoreI18n

	public static final String API_NAME = "TPMActivityDetailObj";

	 //计费方式
	public static final String CALCULATE_PATTERN = "calculate_pattern";

		 //费用(元)X实际数量
		public static final String CALCULATE_PATTERN_Options_1 = "1";

		 //一口价
		public static final String CALCULATE_PATTERN_Options_2 = "2";

		 //其他
		public static final String CALCULATE_PATTERN_Options_other = "other";

	 //兑付方式
	public static final String PAYMENT_MODE = "payment_mode";

		 //金钱
		public static final String PAYMENT_MODE_Options_money = "money";

		 //产品
		public static final String PAYMENT_MODE_Options_goods = "goods";

		 //其他
		public static final String PAYMENT_MODE_Options_other = "other";

	 //编号
	public static final String CODE = "code";

	 //兑付产品数量
	public static final String PAYMENT_PRODUCT_AMOUNT = "payment_product_amount";

	 //数量标准
	public static final String ACTIVITY_AMOUNT_STANDARD = "activity_amount_standard";

	 //陈列项目衡量标准
	public static final String DISPLAY_PROJECT_JUDGMENT_STANDARD = "display_project_judgment_standard";

	 //物料规则
	public static final String MATERIAL_STANDARD_REQUIREM_ID = "material_standard_requirem_id";

	 //产品要求说明
	public static final String PRODUCT_STANDARD_DESCRIPTION = "product_standard_description";

	 //效果图
	public static final String STANDARD_DISPLAY_IMAGES = "standard_display_images";

	 //费用项目
	public static final String ACTIVITY_ITEM_ID = "activity_item_id";

	 //项目描述
	public static final String REMARK = "remark";

	 //是否上报项目数量
	public static final String IS_REPORT_ITEM_QUANTITY = "is_report_item_quantity";

		 //是
		public static final String IS_REPORT_ITEM_QUANTITY_Options_true = "true";

		 //否
		public static final String IS_REPORT_ITEM_QUANTITY_Options_false = "false";

	 //项目类型
	public static final String TYPE = "type";

	 //陈列形式
	public static final String DISPLAY_FORM_ID = "display_form_id";

	 //产品规则
	public static final String PRODUCT_ITEM_STANDARD_ID = "product_item_standard_id";

	 //兑付产品
	public static final String PAYMENT_PRODUCT = "payment_product";

	 //活动方案
	public static final String ACTIVITY_ID = "activity_id";

	 //费用标准(元)
	public static final String ACTIVITY_COST_STANDARD = "activity_cost_standard";

	 //数量校验
	public static final String AMOUNT_STANDARD_CHECK = "amount_standard_check";

		 //是
		public static final String AMOUNT_STANDARD_CHECK_Options_true = "true";

		 //否
		public static final String AMOUNT_STANDARD_CHECK_Options_false = "false";

	 //物料要求说明
	public static final String MATERIAL_STANDARD_DESCRIPTION = "material_standard_description";

}
