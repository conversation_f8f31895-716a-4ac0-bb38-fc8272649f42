package com.facishare.crm.fmcg.wq.constants;

public abstract class MustDistributeProductsFields {

	private MustDistributeProductsFields(){};

	public static final String DISPLAY_NAME = "必分销品"; //ignoreI18n

	public static final String API_NAME = "MustDistributeProductsObj";

	 //产品名称
	public static final String PRODUCT = "product";

	 //陈列场景
	public static final String DISPLAY_SCENE = "display_scene";

		 //111
		public static final String DISPLAY_SCENE_Options_1 = "1";

		 //222
		public static final String DISPLAY_SCENE_Options_2 = "2";

		 //333
		public static final String DISPLAY_SCENE_Options_3 = "3";

		 //其他
		public static final String DISPLAY_SCENE_Options_other = "other";

	 //产品名称（多选）
	public static final String PRODUCT_MULTIPLE = "product_multiple";

	 //陈列形式
	public static final String DISPLAY_FORMAT = "display_format";

	 //成功门店项目标准
	public static final String SUCCESSFUL_STORE = "successful_store";

	 //产品分类
	public static final String PRODUCT_CATEGORY = "product_category";

	 //最低上报必销品品项数
	public static final String MIN_REPORT_PRODUCT = "min_report_product";

}
