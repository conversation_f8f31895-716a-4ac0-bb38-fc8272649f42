package com.facishare.crm.fmcg.wq.proxy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.facishare.appserver.checkins.api.model.*;
import com.facishare.paas.service.model.ObjectDataDocument;
import com.facishare.rest.core.annotation.*;
import com.facishare.rest.core.codec.IRestCodeC;
import org.apache.poi.ss.formula.functions.T;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;


@RestResource(value = "CHECKINS", desc = "外勤回调方法", contentType = "application/json",codec = "com.facishare.crm.fmcg.wq.proxy.CheckinsProxy$CheckinsCodec")
public interface CheckinsProxy {

    @GET(value = "/crmCallBack/getCheckinsById", desc = "判断是否需要审批，外勤回调接口")
    long getCheckinsById(@HeaderParam("x-tenant-id") String eId, @QueryParam("enterpriseAccount") String enterpriseAccount, @QueryParam("outTenantId") String outTenantId, @QueryParam("checkId") String checkId);

    @GET(value = "/crmCallBack/updateCheckinsLockStatus", desc = "外勤锁定状态变更的回调")
    boolean updateCheckinsLockStatus(@HeaderParam("x-tenant-id") String eId,@QueryParam("enterpriseAccount") String enterpriseAccount, @QueryParam("outTenantId") String outTenantId, @QueryParam("checkId") String checkId, @QueryParam("lockStatus") String lockStatus);

    @GET(value = "/crmCallBack/handleCheckinStatus", desc = "外勤触发了审批流的回调")
    boolean handleCheckinStatus(@HeaderParam("x-tenant-id") String eId,@QueryParam("enterpriseAccount") String enterpriseAccount, @QueryParam("outTenantId") String outTenantId, @QueryParam("checkId") String checkId);

    @GET(value = "/crmCallBack/handleCheckinFinishStatus", desc = "外勤审批状态变更以后的回调")
    boolean handleCheckinFinishStatus(@HeaderParam("x-tenant-id") String eId,@QueryParam("enterpriseAccount") String enterpriseAccount, @QueryParam("outTenantId") String outTenantId, @QueryParam("checkId") String checkId, @QueryParam("triggerType") String triggerType, @QueryParam("isPass") String isPass, @QueryParam("needHandlePlanRepeater") String needHandlePlanRepeater);

    @GET(value = "/crmCallBack/stopPublicEmployee", desc = "停用互联用户")
    boolean stopPublicEmployee(@HeaderParam("x-tenant-id") String eId,@QueryParam("tenantId") String tenantId, @QueryParam("userId") String userId);

    @GET(value = "/crmCallBack/startPublicEmployee", desc = "启用互联用户")
    boolean startPublicEmployee(@HeaderParam("x-tenant-id") String eId,@QueryParam("tenantId") String tenantId, @QueryParam("userId") String userId);

    @POST(value="/crmCallBack/saveJumpInfo",desc = "保存跳店信息")
    BaseResult saveJumpInfo(@HeaderParam("x-fs-ei") String ei,@Body SaveJumpShop jumpInfo);

    @POST(value="/crmCallBack/getJumpShop",desc = "保存跳店信息")
    GetJumpInfoResult getJumpShop(@HeaderParam("x-fs-ei") String ei, @Body SaveJumpShop jumpInfo);
    @POST(value = "/crmCallBack/startPublicEmployeeByPromoter", desc = "通过促销员信息启用互联用户")
    BaseResult startPublicEmployeeByPromoter(@HeaderParam("x-fs-ei") String eId, @Body ObjectDataDocument iObjectData);
    @POST(value = "/crmCallBack/changeRolesWithPromoter", desc = "根据促销员对象选项更新角色")
    BaseResult changeRolesWithPromoter(@HeaderParam("x-fs-ei") String eId, @Body ObjectDataDocument iObjectData);
    @POST(value = "/crmCallBack/batchFilterPointInSalesArea", desc = "过滤坐标点是否位于销售区域内")
    List<String> batchFilterPointInSalesArea(@HeaderParam("x-fs-ei") String eId,@Body BatchFilterPointInSalesAreaArg iObjectData);

    @POST(value = "/crmCallBack/userLimitCheck", desc = "用户许可验证")
    CheckUserlimit.Result userLimitCheck(@HeaderParam("x-tenant-id") String eId,@Body CheckUserlimit.Args args);
    @POST(value = "/crmCallBack/getReportActionInfo", desc = "获取上报动作信息")
    ReportActionInfo.Result getReportActionInfo(@HeaderParam("x-tenant-id") String eId,@Body ReportActionInfo.Arg args);

    class CheckinsCodec implements IRestCodeC {
        @Override
        public <T> byte[] encodeArg(T obj) {
            return JSON.toJSONString(obj, SerializerFeature.WriteMapNullValue, SerializerFeature.DisableCircularReferenceDetect,
                    SerializerFeature.WriteBigDecimalAsPlain).getBytes(StandardCharsets.UTF_8);
        }

        @Override
        public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) {
            if (String.class.equals(clazz)) {
                return (T) new String(bytes, StandardCharsets.UTF_8);
            }

            String json = new String(bytes, StandardCharsets.UTF_8);
            return JSON.parseObject(json, clazz);
        }
    }
}

