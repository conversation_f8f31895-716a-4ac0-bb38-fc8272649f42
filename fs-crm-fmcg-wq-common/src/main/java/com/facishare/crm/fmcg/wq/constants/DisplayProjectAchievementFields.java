package com.facishare.crm.fmcg.wq.constants;

public abstract class DisplayProjectAchievementFields {

	private DisplayProjectAchievementFields(){};

	public static final String DISPLAY_NAME = "陈列项目达成"; //ignoreI18n

	public static final String API_NAME = "DisplayProjectAchievementObj";

	 //缺少产品
	public static final String ABSENCE_PRODUCTS = "absence_products";
	public static final String CONDITION_PARENT_ID = "condition_parent_id";

	 //达成项
	public static final String ACHIEVEMENT_SUMMARY = "achievement_summary";

		 //达标
		public static final String ACHIEVEMENT_SUMMARY_Options_1 = "1";

		 //部分达标
		public static final String ACHIEVEMENT_SUMMARY_Options_2 = "2";

		 //物料达标
		public static final String ACHIEVEMENT_SUMMARY_Options_3 = "3";

		 //产品达标
		public static final String ACHIEVEMENT_SUMMARY_Options_4 = "4";

		 //SKU数达标
		public static final String ACHIEVEMENT_SUMMARY_Options_5 = "5";

		 //排面数达标
		public static final String ACHIEVEMENT_SUMMARY_Options_6 = "6";

		 //层数达标
		public static final String ACHIEVEMENT_SUMMARY_Options_7 = "7";

		 //其他
		public static final String ACHIEVEMENT_SUMMARY_Options_other = "other";

	 //设置类型
	public static final String SETTING_TYPE = "setting_type";
	public static final String SETTING_TYPE_NAME = "setting_type__r";


		 //整体陈列标准
		public static final String SETTING_TYPE_Options_1 = "1";

		 //产品陈列标准
		public static final String SETTING_TYPE_Options_2 = "2";

		 //物料陈列标准
		public static final String SETTING_TYPE_Options_3 = "3";

		 //其他
		public static final String SETTING_TYPE_Options_other = "other";

	 //关联规则组对象名称
	public static final String RULE_GROUP_APINAME = "rule_group_apiname";

	 //差异值
	public static final String DIFFERENCE_QUANTITY = "difference_quantity";

	 //要求物料分类
	public static final String REQUIRED_MATERIALS_CLASSIFICATION = "required_materials_classification";
	public static final String REQUIRED_MATERIALS_CLASSIFICATION__R = "required_materials_classification__r";

	 //项目名称
	public static final String PROJECT_NAME = "project_name";
	public static final String PROJECT_NAME__R = "project_name__r";

	 //缺少物料分类
	public static final String ABSENCE_MATERIALS_CLASSIFICATION = "absence_materials_classification";

	 //达成结果
	public static final String ACHIEVED_RESULTS = "achieved_results";
	public static final String ACHIEVED_RESULTS__R = "achieved_results__r";
		//未陈列
		public static final String ACHIEVED_RESULTS_Options_4 = "4";
		
        // 待审核
        public static final String ACHIEVED_RESULTS_Options_5 = "5";
		// 部分达标
		public static final String ACHIEVED_RESULTS_Options_2 = "2";

		 //达标
		public static final String ACHIEVED_RESULTS_Options_1 = "1";

		 //未达标
		public static final String ACHIEVED_RESULTS_Options_0 = "0";

		 //其他
		public static final String ACHIEVED_RESULTS_Options_other = "other";

	 //关联报告
	public static final String RELATED_REPORT = "related_report";

	 //未达成项
	public static final String SUMMARY_ISSUES = "summary_issues";

		 //SKU数不足
		public static final String SUMMARY_ISSUES_Options_1 = "1";

		 //层数不足
		public static final String SUMMARY_ISSUES_Options_2 = "2";

		 //排面数不足
		public static final String SUMMARY_ISSUES_Options_3 = "3";

		 //物料不足
		public static final String SUMMARY_ISSUES_Options_4 = "4";

		 //物料种类不足
		public static final String SUMMARY_ISSUES_Options_5 = "5";

		 //其他
		public static final String SUMMARY_ISSUES_Options_other = "other";

	 //实际产品
	public static final String ACTUAL_PRODUCTS = "actual_products";

	 //要求产品分类
	public static final String REQUIRED_PRODUCTS_CLASSIFICATION = "required_products_classification";
	public static final String REQUIRED_PRODUCTS_CLASSIFICATION__R = "required_products_classification__r";

	 //实际产品分类
	public static final String ACTUAL_PRODUCTS_CLASSIFICATION = "actual_products_classification";

	 //实际物料
	public static final String ACTUAL_MATERIALS = "actual_materials";

	 //关联物料陈列标准
	public static final String MATERIAL_STANDARDS_ID = "material_standards_id";

	 //陈列形式
	public static final String RELATED_DISPLAY_ACHIEVEMENT = "related_display_achievement";
	public static final String RELATED_DISPLAY_ACHIEVEMENT_NAME = "related_display_achievement__r";

	 //层级
	public static final String LEVEL = "level";
	public static final String LEVEL_NAME = "level__r";
		//每层
		public static final String LEVEL_Options_anyLevel = "-1";
		 //所有层级
		public static final String LEVEL_Options_0 = "0";

		 //第1层
		public static final String LEVEL_Options_1 = "1";

		 //第2层
		public static final String LEVEL_Options_2 = "2";

		 //第3层
		public static final String LEVEL_Options_3 = "3";

		 //第4层
		public static final String LEVEL_Options_4 = "4";

		 //第5层
		public static final String LEVEL_Options_5 = "5";

		 //第6层
		public static final String LEVEL_Options_6 = "6";

		 //第7层
		public static final String LEVEL_Options_7 = "7";

		 //其他
		public static final String LEVEL_Options_other = "other";

	 //缺少产品分类
	public static final String ABSENCE_PRODUCTS_CLASSIFICATION = "absence_products_classification";

	 //关联规则组数据
	public static final String RULE_GROUP_ID = "rule_group_id";

	 //要求项目数量
	public static final String REQUIRED_PROJECT_QUANTITY = "required_project_quantity";

	 //要求物料
	public static final String REQUIRED_MATERIALS = "required_materials";
	public static final String REQUIRED_MATERIALS__R = "required_materials__r";

	 //缺少物料
	public static final String ABSENCE_MATERIALS = "absence_materials";

	 //关联产品陈列标准
	public static final String PRODUCT_STANDARDS_ID = "product_standards_id";

	 //要求产品
	public static final String REQUIRED_PRODUCTS = "required_products";
	public static final String REQUIRED_PRODUCTS__R = "required_products__r";

	 //实际物料分类
	public static final String ACTUAL_MATERIALS_CLASSIFICATION = "actual_materials_classification";

	 //实际项目数量
	public static final String ACTUAL_PROJECT_QUANTITY = "actual_project_quantity";

	 //达标方式
	public static final String WAYS_ACHIEVE_STANDARD = "ways_achieve_standard";

		 //所有规则产品项目均达标
		public static final String WAYS_ACHIEVE_STANDARD_Options_1 = "1";

		 //满足一条规则即达标
		public static final String WAYS_ACHIEVE_STANDARD_Options_2 = "2";

		 //其他
		public static final String WAYS_ACHIEVE_STANDARD_Options_other = "other";

	//不达标项目数量
	public static final String UNMET_PROJECT_COUNT__C = "unmet_project_count__c";

}
