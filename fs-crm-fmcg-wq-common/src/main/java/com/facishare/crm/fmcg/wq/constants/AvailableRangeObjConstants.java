package com.facishare.crm.fmcg.wq.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: fs-crm-fmcg
 * @description: 可售范围
 * @author: zhangsm
 * @create: 2021-04-25 21:52
 **/
public interface AvailableRangeObjConstants {
    String API_NAME = "AvailableRangeObj";
    String DISPLAY_NAME = "可售范围"; //ignoreI18n
    @AllArgsConstructor
    @Getter
    enum Field{
        //查找关联 供应关系
        dealerSupplyId("dealer_supply_id__c","供应关系id"), //ignoreI18n
        accountId("account_id__c","所属配送商"), //ignoreI18n
        storeId("store_id__c","特例门店"), //ignoreI18n
        //未知类型 存的json字段
        orgRange("org_range","适用组织"), //ignoreI18n
        accountRange("account_range","可售客户范围"), //ignoreI18n
        productRange("product_range","可售产品范围"), //ignoreI18n
        //boolean 值
        applyToChannelCustomer("apply_to_channel_customer","适用渠道客户"), //ignoreI18n
        //多行文本
        remark("remark","备注"); //ignoreI18n

        /**
         * apiName
         */
        private final String apiName;


        /**
         * 标签名
         */
        private final String label;
    }
    @AllArgsConstructor
    @Getter
    enum Value{
        All("全部Json","{\"type\":\"ALL\",\"value\":\"ALL\"}"), //ignoreI18n
        FIXED("指定Json","{\"type\":\"FIXED\",\"value\":\"FIXED\"}"); //ignoreI18n



        /**
         *
         */
        private String disName;
        private String value;
       public static Value of(String value){
            for (Value fValue : values()) {
                if (fValue.name().equals(value)){
                    return fValue;
                }
            }
            return null;
        }
    }
}
