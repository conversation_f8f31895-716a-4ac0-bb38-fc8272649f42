package com.facishare.crm.fmcg.wq.constants;

public abstract class TPMActivityProofDetailFields {

	private TPMActivityProofDetailFields(){};

	public static final String DISPLAY_NAME = "活动举证项目"; //ignoreI18n

	public static final String API_NAME = "TPMActivityProofDetailObj";

	 //计费方式
	public static final String CALCULATE_PATTERN = "calculate_pattern";

	 //当前数量
	public static final String AMOUNT = "amount";

	 //系统判定结果
	public static final String SYSTEM_JUDGMENT_STATUS = "system_judgment_status";

		 //未陈列
		public static final String SYSTEM_JUDGMENT_STATUS_Options_not_display = "not_display";

		 //达标
		public static final String SYSTEM_JUDGMENT_STATUS_Options_pass = "pass";

		 //不达标
		public static final String SYSTEM_JUDGMENT_STATUS_Options_fail = "fail";

		 //部分达标
		public static final String SYSTEM_JUDGMENT_STATUS_Options_partial_pass = "partial_pass";

		 //待审核
		public static final String SYSTEM_JUDGMENT_STATUS_Options_pending_approval = "pending_approval";

	 //举证项目
	public static final String PROOF_ITEM = "proof_item";

	 //AI识别总排面数
	public static final String AI_FACE_NUMBER = "ai_face_number";

	 //AI识别产品SKU数
	public static final String AI_SKU_NUMBER = "ai_sku_number";

	 //费用项目
	public static final String ACTIVITY_ITEM_ID = "activity_item_id";

	 //数量标准
	public static final String PROOF_DETAIL_AMOUNT_STANDARD = "proof_detail_amount_standard";

	 //是否上报项目数量
	public static final String IS_REPORT_ITEM_QUANTITY = "is_report_item_quantity";

	 //物料陈列结果
	public static final String MATERIAL_DISPLAY_STATUS = "material_display_status";

		 //达标
		public static final String MATERIAL_DISPLAY_STATUS_Options_pass = "pass";

		 //不达标
		public static final String MATERIAL_DISPLAY_STATUS_Options_fail = "fail";

		 //部分达标
		public static final String MATERIAL_DISPLAY_STATUS_Options_partial_pass = "partial_pass";

	 //活动举证
	public static final String ACTIVITY_PROOF_ID = "activity_proof_id";

	 //陈列形式
	public static final String DISPLAY_FORM_ID = "display_form_id";

	 //AI识别数量
	public static final String AI_NUMBER = "ai_number";

	 //产品陈列结果
	public static final String PRODUCT_DISPLAY_STATUS = "product_display_status";

		 //达标
		public static final String PRODUCT_DISPLAY_STATUS_Options_pass = "pass";

		 //不达标
		public static final String PRODUCT_DISPLAY_STATUS_Options_fail = "fail";

		 //部分达标
		public static final String PRODUCT_DISPLAY_STATUS_Options_partial_pass = "partial_pass";

	 //费用项目高级计费
	public static final String ACTIVITY_ITEM_COST_STANDARD_ID = "activity_item_cost_standard_id";

	 //单项费用(元)
	public static final String SUBTOTAL = "subtotal";

	 //数量校验
	public static final String AMOUNT_STANDARD_CHECK = "amount_standard_check";

	 //活动项目
	public static final String ACTIVITY_DETAIL_ID = "activity_detail_id";

	 //协议项目 高优先级
	public static final String ACTIVITY_AGREEMENT_DETAIL_ID = "activity_agreement_detail_id";

	 //费用标准(元)
	public static final String PROOF_DETAIL_COST_STANDARD = "proof_detail_cost_standard";
	//系统判定方式
	public static final String SYSTEM_JUDGMENT_MODE__C = "system_judgment_mode__c";
		//只标准
		public static final String SYSTEM_JUDGMENT_MODE__C_Options_0 = "0";
		//衡量标准+标准
		public static final String SYSTEM_JUDGMENT_MODE__C_Options_1 = "1";
		//只衡量标准
		public static final String SYSTEM_JUDGMENT_MODE__C_Options_2 = "2";

}
