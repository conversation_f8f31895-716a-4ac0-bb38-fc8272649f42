package com.facishare.crm.fmcg.wq.constants;

public abstract class <PERSON>ary<PERSON><PERSON>Fields {

	private SalaryDataFields(){};

	public static final String DISPLAY_NAME = "工资条";

	public static final String API_NAME = "SalaryDataObj";

	 //结束时间
	public static final String END_DATE = "end_date";

	 //日期范围
	public static final String DATE_RANGE = "date_range";

	 //工资规则
	public static final String SALARY_RULE = "salary_rule";

	 //手机号(外部)
	public static final String MOBILE_NUMBER_EXTERNAL = "mobile_number_external";

	 //包含工资项
	public static final String INCLUDE_PAY_ITEMS = "include_pay_items";

	 //工资发放单
	public static final String SALARY_PAYMENT_SLIP = "salary_payment_slip";

	 //员工固定工资表
	public static final String EMPLOYEE_FIXED_SALARY = "employee_fixed_salary";

	 //员工(内部)
	public static final String EMPLOYEE = "employee";

	 //主属部门(内部)
	public static final String MAIN_DEPARTMENT = "main_department";

	 //应付工资
	public static final String PAYABLE_SALARY = "payable_salary";

	 //员工(外部)
	public static final String EMPLOYEE_EXTERNAL = "employee_external";

	 //手机号(内部)
	public static final String PHONE_NUMBER = "phone_number";

	 //互联角色
	public static final String CONNECTED_ROLE = "connected_role";

	 //开始时间
	public static final String START_DATE = "start_date";

}