package com.facishare.crm.fmcg.wq.constants;

public abstract class SalaryPaymentSlipFields {

	private SalaryPaymentSlipFields(){};

	public static final String DISPLAY_NAME = "工资发放单";

	public static final String API_NAME = "SalaryPaymentSlipObj";

	 //结束时间
	public static final String END_DATE = "end_date";

	 //发放状态
	public static final String PAY_STATUS = "pay_status";

		 //生成异常
		public static final String PAY_STATUS_Options_ERROR = "-1";

		 //正在生成
		public static final String PAY_STATUS_Options_0 = "0";

		 //未发放
		public static final String PAY_STATUS_Options_1 = "1";

		 //发放中
		public static final String PAY_STATUS_Options_2 = "2";

		 //发放异常
		public static final String PAY_STATUS_Options_3 = "3";

		 //已发放
		public static final String PAY_STATUS_Options_4 = "4";

	 //发放说明
	public static final String PAY_DESCRIPTION = "pay_description";

	 //发薪周期
	public static final String PAY_CYCLE = "pay_cycle";

		 //按日发放
		public static final String PAY_CYCLE_Options_1 = "1";

		 //按周发放
		public static final String PAY_CYCLE_Options_2 = "2";

		 //按月发放
		public static final String PAY_CYCLE_Options_3 = "3";

	 //工资规则
	public static final String SALARY_RULE = "salary_rule";

	 //发薪期间
	public static final String PAY_PERIOD = "pay_period";

	 //开始时间
	public static final String START_DATE = "start_date";

}