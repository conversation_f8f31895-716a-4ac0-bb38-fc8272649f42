package com.facishare.crm.fmcg.wq.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 公共的字段
 */
public class DataReport2PublicFieldsConstants {

    /// ----------------------------需要用户后台都处理的映射关系
    // 陈列形式标准 数据 虚拟字段陈列照
    public static String DisplayTypeStandardsFields_Virtual_DISPLAY_PHOTO = DisplayTypeStandardsFields.API_NAME
            + ".display_photo";

    // 产品标准，物料标准，项目标准的 虚拟字段 陈列形式字段key
    public static String ProjectStandardsFields_Virtual_DISPLAY_FORMAT = ProjectStandardsFields.API_NAME + "."
            + ProjectStandardsFields.DISPLAY_FORMAT;
    // 产品标准，物料标准，项目标准的 虚拟字段层级字段key
    public static String ProjectStandardsFields_Virtual_LEVEL = ProjectStandardsFields.API_NAME + "."
            + ProjectStandardsFields.DISPLAY_LEVEL;
    /**
     * 虚拟字段 标准达标值字段key
     * 1. 企业规则 虚拟字段 标准达标值字段key
     * 2. 达成数据 存达成字段值
     */
    public static String VIRTUAL_STANDARD_QUANTITY_KEY = "__quantity__field";
    public static String VIRTUAL_STANDARD_QUANTITY_VALUE = "__quantity__Value";
    /// ---------------------------后台默认处理的映射关系
    // 标准达标方式，虚拟字段
    public static String ACHIEVEMENT_WAY = "achievement__way";

    // 关联的规则组ID
    public static String RULE_GROUP_DATA_DETAIL_ID = ProjectStandardsFields.API_NAME + "." + BaseField.id.getApiName();
    public static String RULE_GROUP_DATA_DETAIL_APINAME = ProjectStandardsFields.API_NAME + "." + BaseField.describeApiName.getApiName();
    public static String PROJECT_STANDARD_SETTING_TYPE = ProjectStandardsFields.API_NAME + "."
            + ProjectStandardsFields.SETTING_TYPE;

    // 达成子条件s
    public static final String SUB_CONDITIONS = "sub_conditions";
    // CONDITION parent id
    public static final String CONDITION_PARENT_ID = "condition_parent_id";
    // 每层
    public static final String LEVEL_Options_anyLevel = "-1";
    public static final String LEVEL_Options_AllLevel = "0";

    // 关联的标准详情id
    public static final String STANDARD_DETAIL_ID = "__related_standard_detail_id";

    /**
     * 完整标准对象的后缀
     */
    public static final String DATA_REPORT_STANDARD_DETAIL_SUFFIX = "__Complate";
    /**
     * 项目标准字段
     */
    public static final String PROJECT_STANDARD_FIELD = "project_standard_field";
    /**
     * 产品或者物料标准字段
     */
    public static final String PRODUCT_OR_MATERIAL_STANDARD_FIELD = "product_or_material_standard_field";

    /**
     * tmp 项目 数据上取的
     */
    public static final String TPM_PROJECT_STANDARD_FIELD = "tpm_project_standard_field";

    
    //报告业务类型枚举
    @AllArgsConstructor
    @Getter
    public  enum ReportRecordType {
        // 陈列达成
        DISPLAY_ACHIEVEMENT(DisplayDistrAchSummaryFields.RECORD_DISPLAY),
        // 铺货分销达成
        DISTRIBUTION_ACHIEVEMENT(DisplayDistrAchSummaryFields.RECORD_DISTRIBUTION),
        // 活动举证达成
        ACTIVITY_ACHIEVEMENT(DisplayDistrAchSummaryFields.RECORD_ACTIVITY),
        ;
        String apiName;

    }
}