package com.facishare.crm.fmcg.wq.dao;

import com.beust.jcommander.internal.Maps;
import com.facishare.appserver.checkins.api.model.GetAreaPersonnelSettingResult;
import com.facishare.appserver.checkins.api.model.MergeAreaDataArg;
import com.facishare.appserver.checkins.api.service.ObjUpdateService;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @program: fs-crm-fmcg
 * @description: 操作外勤的
 * @author: zhangsm
 * @create: 2022-01-19 15:02
 **/
@Slf4j
@Component
public class CheckinsDao {
    @Autowired
    ObjUpdateService objUpdateService;

    public boolean lockRoute(String tenantId,String routeId){
        return objUpdateService.lockRoute(tenantId,routeId);
    }
    public boolean unLockRoute(String tenantId,String routeId){
        return objUpdateService.unLockRoute(tenantId,routeId);
    }
    public int getRouteUpdatePermission(String tenantId,int userId,String routeId){
        try{
            return objUpdateService.getRouteUpdatePermission(tenantId,userId,routeId);
        }catch (Exception e){
            return 0;
        }
    }

    public void mergeArea(String ea,String areaId,List<String> fromAreaIds){
        MergeAreaDataArg arg = new MergeAreaDataArg();
        arg.setEa(ea);
        arg.setFromAreaIds(fromAreaIds);
        arg.setSourceAreaId(areaId);
        objUpdateService.mergeAreaData(arg);
    }

    public GetAreaPersonnelSettingResult getAreaPersonnelSetting(String tenantId){
        return objUpdateService.getAreaPersonnelSetting(tenantId);
    }

    public Map<String ,String> getSuccessfulField(String tenantId){
        Map<String, String> successfulSettingField = objUpdateService.getSuccessfulSettingField(tenantId);
        if (successfulSettingField == null){
            successfulSettingField = Maps.newHashMap();
        }
        successfulSettingField.put(BaseField.recordType.getApiName(),"select");
        //取配置
        String extSuccessfulSettingFields = ConfigFactory.getConfig("checkins-v2-config").get("extSuccessfulSettingFields_"+tenantId);
        if (StringUtils.isNotBlank(extSuccessfulSettingFields)){
            String[] split = extSuccessfulSettingFields.split(",");
            for (String s : split) {
                successfulSettingField.put(s,"select");
            }
        }
        return successfulSettingField;
    }

}
