package com.facishare.crm.fmcg.wq.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.*;
import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg
 * @description: query 拼接
 * @author: zhangsm
 * @create: 2021-04-25 17:37
 **/

public class SearchQuery {
    @Getter
    SearchTemplateQuery searchTemplateQuery;

    private SearchQuery(SearchQueryBuilder builder) {
        this.searchTemplateQuery = builder.searchTemplateQuery;
        if (CollectionUtils.isEmpty(this.searchTemplateQuery.getFilters())){
            this.searchTemplateQuery.resetFilters(builder.filters);
        }
        this.searchTemplateQuery.setWheres(builder.wheres);
        this.searchTemplateQuery.setNeedReturnQuote(false);
        if (CollectionUtils.isEmpty(this.searchTemplateQuery.getOrders())) {
            ArrayList<OrderBy> orders = Lists.newArrayList();
            OrderBy orderBy = new OrderBy();
            orderBy.setFieldName("_id");
            orderBy.setIsAsc(false);
            orders.add(orderBy);
            this.searchTemplateQuery.setOrders(orders);
        }
    }


    public static SearchQueryBuilder builder() {
        return new SearchQueryBuilder();
    }

    public static class SearchQueryBuilder {
        private List<IFilter> filters = Lists.newArrayList();
        List<Wheres> wheres = Lists.newArrayList();
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        public SearchQueryBuilder copyOf(SearchQuery searchQuery) {
            this.searchTemplateQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(searchQuery.getSearchTemplateQuery().toJsonString());
            this.filters = this.searchTemplateQuery.getFilters();
            this.wheres = this.searchTemplateQuery.getWheres();
            return this;
        }
        public SearchQueryBuilder copyOf(SearchTemplateQuery searchTemplateQuery){
            this.searchTemplateQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(searchTemplateQuery.toJsonString());
            this.filters = this.searchTemplateQuery.getFilters();
            this.wheres = this.searchTemplateQuery.getWheres();
            return this;
        }
        public SearchQueryBuilder parse(String json){
            searchTemplateQuery =  JSON.parseObject(json, SearchTemplateQuery.class);
            filters = searchTemplateQuery.getFilters();
            wheres = searchTemplateQuery.getWheres();
            return this;
        }
        public SearchQueryBuilder eq(String name, Object value) {
            filtersAddExcludeEmpty(filter(name, Operator.EQ, value));
            return this;
        }
        public SearchQueryBuilder gt(String name, Object value) {
            filtersAddExcludeEmpty(filter(name, Operator.GT, value));
            return this;
        }
        public SearchQueryBuilder lt(String name, Object value) {
            filtersAddExcludeEmpty(filter(name, Operator.LT, value));
            return this;
        }
        public SearchQueryBuilder notExist(String name) {
            this.filters.add(filter(name, Operator.IS, ""));
            return this;
        }
        public SearchQueryBuilder exist(String name) {
            this.filters.add(filter(name, Operator.ISN, ""));
            return this;
        }

        /**
         * 查不出 空值
         * @param name
         * @param value
         * @return
         */
        public SearchQueryBuilder neq(String name, Object value) {
            filtersAddExcludeEmpty(filter(name, Operator.NEQ, value));
            return this;
        }

        /**
         * 能查出空值
         * @param name
         * @param value
         * @return
         */
        public SearchQueryBuilder neqContainNULL(String name, Object value) {
            operator(name,Operator.N,value);
            return this;
        }
        public SearchQueryBuilder in(String name, Object value) {
            Operator operator = Operator.IN;
            if (!(value instanceof Collection) || ((Collection<?>) value).size() == 1){
                operator = Operator.EQ;
            }
            filtersAddExcludeEmpty(filter(name, operator, value));
            return this;
        }
        public SearchQueryBuilder nin(String name, Object value) {
            Operator operator = Operator.NIN;
            if (!(value instanceof Collection) || ((Collection<?>) value).size() == 1){
                operator = Operator.NEQ;
            }
            filtersAddExcludeEmpty(filter(name, operator, value));
            return this;
        }
        public SearchQueryBuilder nNull(String name) {
            filtersAddExcludeEmpty(filter(name, Operator.ISN, ListUtils.EMPTY_LIST));
            return this;
        }
        public SearchQueryBuilder hasAnyOf(String name, Collection value) {
            Filter filter = filter(name, Operator.HASANYOF/*致辞部门多选的问题 现在 操作符由In 改成这个*/, value);
            filtersAddExcludeEmpty(filter);
            return this;
        }
        public SearchQueryBuilder gte(String name, Object value) {
            filtersAddExcludeEmpty(filter(name, Operator.GTE, value));
            return this;
        }
        public SearchQueryBuilder operator(String name,Operator operator, Object value) {
            filtersAddExcludeEmpty(filter(name, operator, value));
            return this;
        }

        public SearchQueryBuilder lte(String name, Object value) {
            operator(name,Operator.LTE,value);
            return this;
        }
        //between 日期类型
        public SearchQueryBuilder betweenDate(String name, long startTime,long endTime) {
            operator(name,Operator.BETWEEN, Lists.newArrayList(startTime,endTime));
            return this;
        }
        public SearchQueryBuilder nHasAnyOf(String name, Collection value) {
            Filter filter = filter(name, Operator.NHASANYOF/*致辞部门多选的问题 现在 操作符由In 改成这个*/, value);
            filtersAddExcludeEmpty(filter);
            return this;
        }
        public SearchQueryBuilder like(String name, Object value) {
            Filter filter = filter(name, Operator.LIKE, value);
            filtersAddExcludeEmpty(filter);
            return this;
        }
        /**
         * 在 部門 且包含子部門
         * @param name
         * @param value
         * @return
         */
        public SearchQueryBuilder inDepartmentAndSub(String name, Collection value) {
            Filter filter = filter(name, Operator.HASANYOF/*致辞部门多选的问题 现在 操作符由In 改成这个*/, value);
            filter.setIsCascade(true);
            filtersAddExcludeEmpty(filter);
            return this;
        }
        public SearchQueryBuilder ninDepartmentAndSub(String name, Collection value) {
            Filter filter = filter(name, Operator.NHASANYOF/*致辞部门多选的问题 现在 操作符由In 改成这个*/, value);
            filter.setIsCascade(true);
            filtersAddExcludeEmpty(filter);
            return this;
        }
        public SearchQueryBuilder inDepartmentNotSub(String name, Object value) {
            Filter filter = filter(name, Operator.IN, value);
            filter.setIsCascade(false);
            filtersAddExcludeEmpty(filter);
            return this;
        }
        public SearchQueryBuilder limit(int limit) {
            this.searchTemplateQuery.setLimit(limit);
            return this;
        }

        public SearchQueryBuilder offSet(int offset) {
            this.searchTemplateQuery.setOffset(offset);
            return this;
        }

        public SearchQuery build() {
            return new SearchQuery(this);
        }
        private SearchQueryBuilder setFilters(List<IFilter> filters){
            this.filters = filters;
            return this;
        }
        public SearchQueryBuilder addWheres(Wheres wheres){
            this.wheres.add(wheres);
            return this;
        }
        private void filtersAddExcludeEmpty(Filter filter) {
            if (filter.getFieldValues() != null) {
                this.filters.add(filter);
            }
        }
        /**
         * 本身query 会变成where two也会变成where
         * 会清空本身的filter
         * @param two
         * @return
         */
        public SearchQueryBuilder addOrWheres(SearchQueryBuilder two){
//        List<Wheres> iFilters = new ArrayList<>();
            Wheres oneFilter = convertWheres(Where.CONN.AND,this.filters);
            Wheres twoFilter = convertWheres(Where.CONN.AND,two.filters);
//        iFilters.add(oneFilter);
//        iFilters.add(twoFilter);
            return this.setFilters(Lists.newArrayList()).addWheres(oneFilter).addWheres(twoFilter);
        }
        /**
         * 给每个filter 或者 where  增加条件  --where给每个都加
         * @return
         */
        public SearchQueryBuilder addFilterInWhere(Filter filter){
            if(CollectionUtils.isNotEmpty(this.filters)){
                this.filters.add(filter);
            }
            if(CollectionUtils.isNotEmpty(this.wheres)) {
                for (Wheres where : this.wheres) {
                    where.getFilters().add(filter);
                }
            }
            return this;
        }

        public SearchQueryBuilder addFilter(List<Filter> filter){
            if(CollectionUtils.isNotEmpty(this.filters)){
                this.filters.addAll(filter);
            }
            return this;
        }

    }

    public static Filter filter(String name, Operator operator, Object value) {
        return filter(name, operator, value, null);
    }

    private static Wheres convertWheres(Where.CONN conn, List<IFilter> filters){
        Wheres wheres = new Wheres();
        wheres.setConnector(conn.name());
        wheres.setFilters(filters);
        return wheres;
    }
    private static Filter filter(String name, Operator operator, Object value, Integer valueType) {
        Filter filter = new Filter();
        filter.setFieldName(name);
        if (value instanceof Iterable) {
            List<String> list = Lists.newArrayList();
            ((Iterable) value).forEach(o -> list.add(o.toString()));
            filter.setFieldValues(list);
        } else {
            filter.setFieldValues(Arrays.asList(value.toString()));
        }
        filter.setOperator(operator);
        if (valueType != null) {
            filter.setValueType(valueType);
        }
        return filter;
    }

    public static List<Filter> convertFilter(String json){
        JSONObject object = JSONObject.parseObject(json);
        List<Filter> otherFilters = Lists.newArrayList();
        if(com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(object.getJSONArray("filters"))){
            JSONArray array = object.getJSONArray("filters");
            for (int i = 0; i < array.size(); i++) {
                Filter otherProFilter = new Filter();
                otherProFilter.setFieldName(array.getJSONObject(i).getString("field_name"));
                otherProFilter.setOperator(Operator.valueOf(array.getJSONObject(i).getString("operator")));
                otherProFilter.setFieldValues(array.getJSONObject(i).getJSONArray("field_values").stream().map(o->String.valueOf(o)).collect(Collectors.toList()));
                if(Objects.nonNull(array.getJSONObject(i).get("value_type"))) otherProFilter.setValueType(array.getJSONObject(i).getInteger("value_type"));
                if(Objects.nonNull(array.getJSONObject(i).get("is_cascade"))) otherProFilter.setIsCascade(array.getJSONObject(i).getBoolean("is_cascade"));
                otherFilters.add(otherProFilter);
            }
        }
        return otherFilters;
    }
}
