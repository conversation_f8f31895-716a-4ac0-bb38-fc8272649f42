package com.facishare.crm.fmcg.wq;

import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.release.FsGrayRelease;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 灰度配置
 * @author: zhangsm
 * @create: 2022-09-07 11:08
 **/
public interface FmcgGray {

    interface Checkins {
        enum EI implements FmcgGray {
            //移除列表页复选框选中的多条记录显示的对象默认的批量操作按钮，只保留自定义批量操作按钮
            removeCheckinsObjDefaultBatchButtonsForEIs,
            //高级外勤批量操作外露指定按钮
            showCheckinsObjAppointedButtons,
            //高级外勤详情页显示自定义按钮
            showCheckinsWebDetailCustomButtons,
            //达成结果计算全部完成时间字段同步
            syncAchievementResultsAllCompleteTime,
            /**
             * 未陈列，部分达标视为 未达标
             */
            isPartialDisplayedNotAchieved,
            //ios 积分
            iOSIntegral;

            @Override
            public boolean gray(String eId) {
                return GrayRelease.isAllow("checkin-server-v2", this.toString(), eId);
            }

            @Override
            public boolean fsGray(String tenantId) {
                return FsGrayRelease.getInstance("checkin-server-v2").isAllow(this.toString(),tenantId);
            }

        }
        enum EA implements FmcgGray{
            /**
             * 是 银鹭使用的供货关系依赖的 经营范围对象
             */
            isYLProductCollection,

            /**
             * 片区管理
             */
            areaManage,
            //不验证客户id 的lookup字段
            noValidateCoustomerIdLookup;
            @Override
            public boolean gray(String ea) {
                return GrayRelease.isAllow("checkin-server-v2", this.toString(), ea);
            }

            @Override
            public boolean fsGray(String ea) {
                return FsGrayRelease.getInstance("checkin-server-v2").isAllow(this.toString(),ea);
            }
        }
    }

    interface CheckinsOffice {

    }

    interface Supply {
        enum EI implements FmcgGray {
            //关闭同步价目表
            closedSyncAvailablePriceBookObj;

            @Override
            public boolean gray(String eId) {
                return GrayRelease.isAllow("checkin-server-v2", this.toString(), eId);
            }

            @Override
            public boolean fsGray(String tenantId) {
                return FsGrayRelease.getInstance("checkin-server-v2").isAllow(this.toString(),tenantId);
            }

        }
    }


    boolean gray(String eId);

    boolean fsGray(String eId);
}
