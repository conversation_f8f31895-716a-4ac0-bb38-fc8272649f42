package com.facishare.crm.fmcg.wq.constants;

public abstract class EmployeeFixedSalaryFields {

	private EmployeeFixedSalaryFields(){};

	public static final String DISPLAY_NAME = "员工固定工资表";

	public static final String API_NAME = "EmployeeFixedSalaryObj";

	 //互联企业
	public static final String CONNECTED_COMPANY = "connected_company";

	 //工资规则
	public static final String SALARY_RULE = "salary_rule";

	 //手机号(外部)
	public static final String MOBILE_NUMBER_EXTERNAL = "mobile_number_external";

	 //员工(外部)
	public static final String EMPLOYEE_EXTERNAL = "employee_external";

	 //手机号(内部)
	public static final String PHONE_NUMBER = "phone_number";

	 //员工(内部)
	public static final String EMPLOYEE = "employee";

	 //主属部门(内部)
	public static final String MAIN_DEPARTMENT = "main_department";

	 //互联角色
	public static final String CONNECTED_ROLE = "connected_role";

		 //示例选项
		public static final String CONNECTED_ROLE_Options_option1 = "option1";

	 //定薪方式
	public static final String SALARY_METHOD = "salary_method";

		 //日薪
		public static final String SALARY_METHOD_Options_1 = "1";

		 //周薪
		public static final String SALARY_METHOD_Options_2 = "2";

		 //月薪
		public static final String SALARY_METHOD_Options_3 = "3";

}