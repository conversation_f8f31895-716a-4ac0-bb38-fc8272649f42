package com.facishare.crm.fmcg.wq.excel.style;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/03/12/ 17:41
 **/
@Data
public class PullDownCellWriteHandler implements SheetWriteHandler{


    /**
     * 下拉选项值
     */
    private final List<String> dropdownList;

    private final int firstRow;
    private final int lastRow;
    private final int firstCol;
    private final int lastCol;


    public PullDownCellWriteHandler(List<String> dropdownList,int firstRow,int lastRow,int firstCol,int lastCol) {
        this.dropdownList = dropdownList;
        this.firstRow = firstRow;
        this.lastRow = lastRow;
        this.firstCol = firstCol;
        this.lastCol = lastCol;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        if(CollectionUtils.isEmpty(dropdownList)){
            return;
        }
        Sheet sheet = writeSheetHolder.getSheet();
        DataValidationHelper validationHelper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = validationHelper.createExplicitListConstraint(dropdownList.toArray(new String[0]));
        CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
        XSSFDataValidation dataValidation = (XSSFDataValidation) validationHelper.createValidation(constraint,addressList);
        dataValidation.setShowErrorBox(true);
        sheet.addValidationData(dataValidation);

    }

}
