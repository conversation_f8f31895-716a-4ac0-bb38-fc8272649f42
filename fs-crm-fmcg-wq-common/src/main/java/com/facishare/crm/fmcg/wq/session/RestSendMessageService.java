package com.facishare.crm.fmcg.wq.session;

import com.facishare.restful.client.exception.FRestClientException;
import com.fxiaoke.api.MessageServiceV2;
import com.fxiaoke.model.MessageResponse;
import com.fxiaoke.model.message.SendTextCardMessageArg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Slf4j
@Service
public class RestSendMessageService {

    public static final String PMM_APPID = "FMCGPMM";

    @Resource
    public MessageServiceV2 messageServiceV2;


    public void sendTextCardMessage(SendTextCardMessageArg arg) {
        try {
            MessageResponse result = messageServiceV2.sendTextCardMessage(arg);
            if (result.getCode() == MessageResponse.SUCCESS_CODE) {
                log.info("message send success");
            } else {
                log.error("message send failed");
            }
        } catch (FRestClientException ex) {
            log.error("FRestClientException -", ex);
        }
    }


}
