package com.facishare.crm.fmcg.wq.session;

import com.facishare.restful.client.exception.FRestClientException;
import com.fxiaoke.api.MessageServiceV2;
import com.fxiaoke.model.MessageResponse;
import com.fxiaoke.model.message.SendTextCardMessageArg;
import com.fxiaoke.enterpriserelation2.service.UnionMessageService;
import com.fxiaoke.enterpriserelation2.arg.UnionMessageSendArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Slf4j
@Service
public class RestSendMessageService {

    public static final String PMM_APPID = "FMCGPMM";

    @Resource
    public MessageServiceV2 messageServiceV2;

    @Resource
    public UnionMessageService unionMessageService;




    public void sendTextCardMessage(SendTextCardMessageArg arg) {
        try {
            MessageResponse result = messageServiceV2.sendTextCardMessage(arg);
            if (result.getCode() == MessageResponse.SUCCESS_CODE) {
                log.info("message send success");
            } else {
                log.error("message send failed");
            }
        } catch (FRestClientException ex) {
            log.error("FRestClientException -", ex);
        }
    }

    /**
     * 带重试机制的发送文本卡片消息
     *
     * @param arg 消息参数
     * @param employeeId 员工ID
     * @param salaryDataId 工资条ID
     * @return 是否发送成功
     */
    public boolean sendTextCardMessageWithRetry(SendTextCardMessageArg arg, String employeeId) {
        int retryTime = 3; // 默认重试3次
        for (int i = 0; i < retryTime; i++) {
            try {
                MessageResponse result = messageServiceV2.sendTextCardMessage(arg);
                if (result.getCode() == MessageResponse.SUCCESS_CODE) {
                    log.info("sendTextCardMessageWithRetry success，employeeId: {}", employeeId);
                    return true;
                } else {
                    log.warn("sendTextCardMessageWithRetry failed，employeeId: {}", employeeId);
                    if (i == retryTime - 1) {
                        return false;
                    }
                }
            } catch (Exception e) {
                if (i == retryTime - 1) {
                    log.error("sendTextCardMessageWithRetry error，employeeId: {}", employeeId, e);
                    return false;
                } else {
                    log.warn("sendTextCardMessageWithRetry error，第{}次重试，employeeId: {}", i + 1, employeeId, e);
                }
            }
        }
        return false;
    }

    /**
     * 带重试机制的发送Union消息
     *
     * @param headerObj 头部对象
     * @param unionMessageSendArg Union消息参数
     * @param employeeExternalId 外部员工ID
     * @param salaryDataId 工资条ID
     * @return 是否发送成功
     */
    public boolean sendUnionMessageWithRetry(HeaderObj headerObj, UnionMessageSendArg unionMessageSendArg,
                                           String employeeExternalId) {
        int retryTime = 3; // 默认重试3次
        for (int i = 0; i < retryTime; i++) {
            try {
                RestResult<Void> result = unionMessageService.sendMessage(headerObj, unionMessageSendArg);
                if (result != null) {
                    log.info("sendUnionMessageWithRetry success，employeeExternalId: {}",
                        employeeExternalId);
                    return true;
                }
            } catch (Exception e) {
                if (i == retryTime - 1) {
                    log.error("sendUnionMessageWithRetry error，employeeExternalId: {}",
                        employeeExternalId, e);
                    return false;
                } else {
                    log.warn("sendUnionMessageWithRetry error，第{}次重试，employeeExternalId: {}",
                        i + 1, employeeExternalId, e);
                }
            }
        }
        return false;
    }

}
