package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-20 17:00
 **/
@Slf4j
@Component
public class EmployeeDao extends AbstractDao {

    /**
     * 通过用户ids 获取id名字
     */
    public Map<Integer,String> getUserNameByIds(String tenantId, Set<Integer> ids) {
        Map<Integer,String> idNameMap = Maps.newHashMap();
        if (null == ids || ids.isEmpty()) {
            return null;
        }
        if (ids.removeIf(o->o == -10000)){
            idNameMap.put(-10000,"系统"); //ignoreI18n
        }
        Map<Boolean,List<Integer>> idsMap = ids.stream().collect(Collectors.groupingBy(id->id.intValue()>100000000));
        idsMap.forEach((isOuter,v)->{
            if (isOuter){
                List<IObjectData> outerEmployeeObjs = getOuterEmployeeObjs(tenantId, v,Lists.newArrayList("outer_uid","name"));
                //获取外部员工
                idNameMap.putAll(outerEmployeeObjs.stream().collect(Collectors.toMap(o->Integer.valueOf(o.get("outer_uid").toString()),o->o.get("name").toString())));
            }else {
                List<IObjectData> innerEmployeeObjs = getInnerEmployeeObjs(tenantId, v, Lists.newArrayList("user_id","name"));
                //获取内部员工
                idNameMap.putAll(innerEmployeeObjs.stream().collect(Collectors.toMap(o->Integer.valueOf(o.get("user_id").toString()),o->o.get("name").toString())));
            }
        });
        return idNameMap;
    }

    private List<IObjectData> getInnerEmployeeObjs(String tenantId, List<Integer> outerUserIds, List<String> fields) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in("user_id", outerUserIds)
                .build(), "PersonnelObj",fields);
    }

    private List<IObjectData> getOuterEmployeeObjs(String tenantId, List<Integer> userIds, List<String> fields) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in("outer_uid", userIds)
                .build(), "PublicEmployeeObj",fields);
    }

}

