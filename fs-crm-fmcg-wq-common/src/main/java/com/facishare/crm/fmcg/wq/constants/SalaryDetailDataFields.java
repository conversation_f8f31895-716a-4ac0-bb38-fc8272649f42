package com.facishare.crm.fmcg.wq.constants;

public abstract class SalaryDetailDataFields {

	private SalaryDetailDataFields(){};

	public static final String DISPLAY_NAME = "工资条明细";

	public static final String API_NAME = "SalaryDetailDataObj";

	 //结束时间
	public static final String END_DATE = "end_date";

	 //日期范围
	public static final String DATE_RANGE = "date_range";

	 //取值方式
	public static final String VALUE_TYPE = "value_type";

	 //金额
	public static final String AMOUNT = "amount";

	 //工资规则
	public static final String SALARY_RULE = "salary_rule";

	 //公式赋值
	public static final String FORMULA_ASSIGNMENT = "formula_assignment";

	 //手机号(外部)
	public static final String MOBILE_NUMBER_EXTERNAL = "mobile_number_external";

	 //工资项
	public static final String SALARY_ITEM = "salary_item";

	 //计算公式
	public static final String CALCULATION_FORMULA = "calculation_formula";

	 //员工固定工资表
	public static final String EMPLOYEE_FIXED_SALARY = "employee_fixed_salary";

	 //员工(内部)
	public static final String EMPLOYEE = "employee";

	 //主属部门(内部)
	public static final String MAIN_DEPARTMENT = "main_department";

	 //发放状态
	public static final String DISTRIBUTION_STATUS = "distribution_status";

		 //已修正
		public static final String DISTRIBUTION_STATUS_Options_4 = "4";

		 //生成异常
		public static final String PAY_STATUS_Options_ERROR = "-1";


		 //未发放
		public static final String DISTRIBUTION_STATUS_Options_0 = "0";

		 //发放中
		public static final String DISTRIBUTION_STATUS_Options_1 = "1";

		 //发放异常
		public static final String DISTRIBUTION_STATUS_Options_2 = "2";

		 //已发放
		public static final String DISTRIBUTION_STATUS_Options_3 = "3";

	 //工资条
	public static final String SALARY_DATA = "salary_data";

	 //员工(外部)
	public static final String EMPLOYEE_EXTERNAL = "employee_external";

	 //手机号(内部)
	public static final String PHONE_NUMBER = "phone_number";

	 //互联角色
	public static final String CONNECTED_ROLE = "connected_role";

	 //开始时间
	public static final String START_DATE = "start_date";

}