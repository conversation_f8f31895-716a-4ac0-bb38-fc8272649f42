package com.facishare.crm.fmcg.wq.constants;

public abstract class Salary<PERSON>temFields {

	private SalaryItemFields(){};

	public static final String DISPLAY_NAME = "工资项";

	public static final String API_NAME = "SalaryItemObj";

	 //取值方式
	public static final String VALUE_TYPE = "value_type";

		 //计算公式
		public static final String VALUE_TYPE_Options_2 = "2";

		 //固定值
		public static final String VALUE_TYPE_Options_1 = "1";

	 //工资项说明
	public static final String SALARY_ITEM_DESCRIPTION = "salary_item_description";

	 //计算公式
	public static final String CALCULATION_FORMULA = "calculation_formula";

	 //增减属性
	public static final String INCREMENT_DECREMENT_ATTRIB = "increment_decrement_attrib";

		 //扣减项
		public static final String INCREMENT_DECREMENT_ATTRIB_Options_2 = "2";

		 //应发项
		public static final String INCREMENT_DECREMENT_ATTRIB_Options_1 = "1";

	 //启用状态
	public static final String ENABLED_STATUS = "enabled_status";

		 //是
		public static final String ENABLED_STATUS_Options_true = "true";

		 //否
		public static final String ENABLED_STATUS_Options_false = "false";

	 //公式包含指标
	public static final String FORMULA_INCLUDES_KPI = "formula_includes_kpi";

	 //舍位方式
	public static final String ROUNDING_METHOD = "rounding_method";

		 //四舍五入
		public static final String ROUNDING_METHOD_Options_1 = "1";

		 //向上取整
		public static final String ROUNDING_METHOD_Options_2 = "2";

		 //向下取整
		public static final String ROUNDING_METHOD_Options_3 = "3";

	 //定薪方式
	public static final String SALARY_METHOD = "salary_method";

		 //日薪
		public static final String SALARY_METHOD_Options_1 = "1";

		 //周薪
		public static final String SALARY_METHOD_Options_2 = "2";

		 //月薪
		public static final String SALARY_METHOD_Options_3 = "3";

	 //小数位数
	public static final String DECIMAL_PLACES = "decimal_places";

		 //1
		public static final String DECIMAL_PLACES_Options_1 = "1";

		 //2
		public static final String DECIMAL_PLACES_Options_2 = "2";

		 //3
		public static final String DECIMAL_PLACES_Options_3 = "3";

}