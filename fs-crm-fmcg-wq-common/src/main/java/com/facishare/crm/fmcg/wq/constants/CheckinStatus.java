package com.facishare.crm.fmcg.wq.constants;

/**
 * Created by <PERSON><PERSON><PERSON> on 15/6/12.
 */
public enum CheckinStatus {
    //初始状态
    InitSuccess(0),
    // 数据入库成功
    MongoSuccess(1),
    // 正式文件图片入库成功
    PicSuccess(2),
    // feed发送成功
    FeedSuccess(3),
    //完成状态 高级外勤使用
    Finish(4),
    //-1删除状态
    Delete(-1),
    //-2触发流程
    TriggerWorkflow(-2),
    //-3流程驳回
    WorkflowReject(-3);


    CheckinStatus(int i) {
        this.value = i;
    }

    private int value;

    public int intValue() {
        return this.value;
    }
}
