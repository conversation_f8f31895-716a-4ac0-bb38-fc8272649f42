package com.facishare.crm.fmcg.wq.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: fs-crm-fmcg
 * @description: 可售客户
 * @author: zhangsm
 * @create: 2021-04-25 21:52
 **/
public interface AvailableAccountObjConstants {
    String API_NAME = "AvailableAccountObj";
    String DISPLAY_NAME = "可售客户"; //ignoreI18n
    @AllArgsConstructor
    @Getter
    enum Field{
        //id
        availableRangeId("available_range_id","可售范围id"), //ignoreI18n
        accountId("account_id","客户名称"), //ignoreI18n
        //单选
        applyRange("apply_range","适用范围"); //ignoreI18n

        /**
         * apiName
         */
        private final String apiName;


        /**
         * 标签名
         */
        private final String label;
    }
    @AllArgsConstructor
    @Getter
    enum Value{
        ALL("全部"), //ignoreI18n
        FIXED("指定客户"), //ignoreI18n
        CONDITION("符合指定条件"); //ignoreI18n

        /**
         *
         */
        private String disName;
       public static Value of(String value){
            for (Value fValue : values()) {
                if (fValue.name().equals(value)){
                    return fValue;
                }
            }
            return null;
        }
    }
}
