package com.facishare.crm.fmcg.wq;

import com.beust.jcommander.internal.Lists;
import com.facishare.appserver.checkins.CheckinsConstants;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.dto.ImportObjectModule;
import com.facishare.paas.appframework.metadata.importobject.ImportObjectProvider;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.github.autoconf.ConfigFactory;
import org.apache.commons.collections.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg
 * @description:
 * @author: zhangsm
 * @create: 2022-04-07 14:31
 **/
public class WQImportObjectProvider implements ImportObjectProvider {
    @Autowired
    private DescribeLogicService describeLogicService;

    static List<String> supportImportApiNames = ListUtils.EMPTY_LIST;
    static {
         ConfigFactory.getInstance().getConfig("variables_fmcg_gray", iConfig -> {
             supportImportApiNames = Lists.newArrayList(iConfig.get("fmcg_wq_support_obj_vertical","CheckinsObj").split("[|]"));
         }, true);
     }
    @Override
    public String getObjectModule() {
        return "FS-CRM-FMCG-WQ";
    }

    @Override
    public List<ImportObjectModule.ImportModule> getImportObjectModule(User user, ImportModuleContext importModuleContext) {
        Map<String, IObjectDescribe> iObjectDescribeMap = describeLogicService.findObjects(user.getTenantId(), supportImportApiNames);
        return iObjectDescribeMap.values().stream()
                .map(ImportObjectModule.ImportModule::of)
                .collect(Collectors.toList());
    }

}
