package com.facishare.crm.fmcg.wq;

import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.ApplicationObjectSupport;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 实现 PreDefineObject 注册方法
 * @author: zhangsm
 * @create: 2022-09-02 11:58
 **/
@Slf4j
public class WQInitService extends ApplicationObjectSupport {
    @PostConstruct
    public void init() {
        log.info("start init checkins.");
        CheckinsDefaultObject.init();
        log.info("start init mc. supply");
        MCPreDefineObject.init();
        log.info("start init office.");
        CheckinsOfficePreDefineObject.init();
        log.info("starat data report.");
        ReportDefaultObject.init();
    }
}
