package com.facishare.crm.fmcg.wq.notify.impl;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工资条消息服务实现类测试
 */
class SalaryMessageServiceImplTest {

    @Test
    void testSendExternalEmployeeMessage_Success() {
        // 准备测试数据
        BaseResult mockResult = new BaseResult();
        mockResult.setSuccess(true);
        when(unionMessageService.sendTextLinkMessage(any(SendTextLinkMessageArg.class))).thenReturn(mockResult);

        // 执行测试
        boolean result = salaryMessageService.sendExternalEmployeeMessage(TENANT_ID, EMPLOYEE_EXTERNAL_ID, salaryData);

        // 验证结果
        assertTrue(result);
        verify(unionMessageService, times(1)).sendTextLinkMessage(any(SendTextLinkMessageArg.class));
    }

    @Test
    void testSendExternalEmployeeMessage_Failure() {
        // 准备测试数据
        BaseResult mockResult = new BaseResult();
        mockResult.setSuccess(false);
        when(unionMessageService.sendTextLinkMessage(any(SendTextLinkMessageArg.class))).thenReturn(mockResult);

        // 执行测试
        boolean result = salaryMessageService.sendExternalEmployeeMessage(TENANT_ID, EMPLOYEE_EXTERNAL_ID, salaryData);

        // 验证结果
        assertFalse(result);
        verify(unionMessageService, times(1)).sendTextLinkMessage(any(SendTextLinkMessageArg.class));
    }

    @Test
    void testSendExternalEmployeeMessage_NullResult() {
        // 准备测试数据
        when(unionMessageService.sendTextLinkMessage(any(SendTextLinkMessageArg.class))).thenReturn(null);

        // 执行测试
        boolean result = salaryMessageService.sendExternalEmployeeMessage(TENANT_ID, EMPLOYEE_EXTERNAL_ID, salaryData);

        // 验证结果
        assertFalse(result);
        verify(unionMessageService, times(1)).sendTextLinkMessage(any(SendTextLinkMessageArg.class));
    }

    @Test
    void testSendExternalEmployeeMessage_InvalidTenantId() {
        // 执行测试
        boolean result = salaryMessageService.sendExternalEmployeeMessage("", EMPLOYEE_EXTERNAL_ID, salaryData);

        // 验证结果
        assertFalse(result);
        verify(unionMessageService, never()).sendTextLinkMessage(any(SendTextLinkMessageArg.class));
    }

    @Test
    void testSendExternalEmployeeMessage_InvalidEmployeeId() {
        // 执行测试
        boolean result = salaryMessageService.sendExternalEmployeeMessage(TENANT_ID, "", salaryData);

        // 验证结果
        assertFalse(result);
        verify(unionMessageService, never()).sendTextLinkMessage(any(SendTextLinkMessageArg.class));
    }

    @Test
    void testSendExternalEmployeeMessage_NullSalaryData() {
        // 执行测试
        boolean result = salaryMessageService.sendExternalEmployeeMessage(TENANT_ID, EMPLOYEE_EXTERNAL_ID, null);

        // 验证结果
        assertFalse(result);
        verify(unionMessageService, never()).sendTextLinkMessage(any(SendTextLinkMessageArg.class));
    }

    @Test
    void testSendExternalEmployeeMessage_InvalidEmployeeIdFormat() {
        // 执行测试
        boolean result = salaryMessageService.sendExternalEmployeeMessage(TENANT_ID, "invalid_id", salaryData);

        // 验证结果
        assertFalse(result);
        verify(unionMessageService, never()).sendTextLinkMessage(any(SendTextLinkMessageArg.class));
    }

    @Test
    void testSendExternalEmployeeMessage_Exception() {
        // 准备测试数据
        when(unionMessageService.sendTextLinkMessage(any(SendTextLinkMessageArg.class)))
            .thenThrow(new RuntimeException("Network error"));

        // 执行测试
        boolean result = salaryMessageService.sendExternalEmployeeMessage(TENANT_ID, EMPLOYEE_EXTERNAL_ID, salaryData);

        // 验证结果
        assertFalse(result);
        verify(unionMessageService, times(1)).sendTextLinkMessage(any(SendTextLinkMessageArg.class));
    }
}
