package com.facishare.crm.fmcg.wq.command;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 命令调用器
 * @author: dev
 * @create: 2024-07-10
 */
@Slf4j
@Component
public class CommandInvoker {
    
    /**
     * 执行命令
     *
     * @param command 要执行的命令
     * @param <T> 命令返回类型
     * @return 命令执行结果
     */
    public <T> T execute(SalaryCommand<T> command) {
        try {
            log.debug("开始执行命令: {}", command.getClass().getSimpleName());
            T result = command.execute();
            log.debug("命令执行完成: {}", command.getClass().getSimpleName());
            return result;
        } catch (Exception e) {
            log.error("命令执行失败: {}", command.getClass().getSimpleName(), e);
            throw e;
        }
    }
}
