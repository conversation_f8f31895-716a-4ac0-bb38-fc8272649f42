package com.facishare.crm.fmcg.wq.model.kpi;

import com.facishare.crm.fmcg.wq.constants.EmployeeKPIFields;
import com.facishare.crm.fmcg.wq.model.AggregateFunction;
import com.facishare.crm.fmcg.wq.model.KPICalculateType;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class AggregateSalaryKPI extends SalaryKPI {

    private String objectApiName;

    private String aggregateFieldApiName;

    private String aggregateTimeFieldApiName;

    private String aggregateUserFieldApiName;

    private AggregateFunction aggregateFunction;

    private String wheres;

    @SuppressWarnings("Duplicates")
    public static AggregateSalaryKPI of(IObjectData data) {
        AggregateSalaryKPI metric = new AggregateSalaryKPI();

        metric.setTenantId(data.getTenantId());
        metric.setId(data.getId());
        metric.setName(data.getName());
        metric.setDescription(data.get(EmployeeKPIFields.INDICATOR_DESC, String.class));
        metric.setKpiCalculateType(KPICalculateType.of(data.get(EmployeeKPIFields.INDICATOR_CALC_METHOD, String.class)));

        metric.setObjectApiName(data.get(EmployeeKPIFields.AGGREGATED_OBJECT, String.class));
        metric.setAggregateFieldApiName(data.get(EmployeeKPIFields.AGGREGATED_FIELD, String.class));
        metric.setAggregateTimeFieldApiName(data.get(EmployeeKPIFields.AGGREGATED_DATE_FIELD, String.class));
        metric.setAggregateUserFieldApiName(data.get(EmployeeKPIFields.AGGREGATED_PERSON_FIELD, String.class));
        metric.setAggregateFunction(AggregateFunction.valueOf(data.get(EmployeeKPIFields.AGGREGATION_FUNCTION, String.class)));
        /**
         * where条件
         */
        metric.setWheres(data.get(EmployeeKPIFields.WHERES, String.class));

        return metric;
    }
}