package com.facishare.crm.fmcg.wq.service;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 薪资服务接口
 * @author: dev
 * @create: 2024-06-23
 */
public interface SalaryService {
    /**
     * 计算工资条明细数据，根据员工固定薪资对象和时间范围生成薪资明细列表。
     * <p>
     * 该方法会执行以下操作：
     * <ol>
     *   <li>从employeeFixedSalaryObj获取员工信息和相关的薪资规则</li>
     *   <li>获取薪资规则关联的工资项列表</li>
     *   <li>对每个工资项，根据其取值方式进行处理：</li>
     *     <ul>
     *       <li>如果是固定值类型，从员工固定薪资明细中获取金额</li>
     *       <li>如果是计算公式类型，解析公式并计算结果</li>
     *     </ul>
     *   <li>生成对应的薪资明细对象，设置各项字段</li>
     * </ol>
     * <p>
     * 对于计算公式类型的工资项，会根据公式中包含的KPI指标，使用相应的KPI计算器进行计算。
     *
     * @param employeeFixedSalaryObj 员工固定薪资对象，包含员工信息和固定薪资数据
     * @param startTime 计算周期的开始时间，毫秒时间戳
     * @param endTime 计算周期的结束时间，毫秒时间戳
     * @return 生成的薪资明细对象列表(SalaryDetailDataObj)
     * @throws RuntimeException 如果计算薪资明细过程中发生错误
     *
     * @see com.facishare.crm.fmcg.wq.constants.SalaryDetailDataFields
     * @see com.facishare.crm.fmcg.wq.constants.SalaryItemFields
     * @see com.facishare.crm.fmcg.wq.constants.EmployeeKPIFields
     *
     * <pre>
     * // 示例用法
     * IObjectData employeeFixedSalary = employeeFixedSalaryDao.getById(tenantId, employeeFixedSalaryId);
     * long startTime = DateUtils.parseDate("2023-01-01").getTime();
     * long endTime = DateUtils.parseDate("2023-01-31").getTime();
     * List<IObjectData> salaryDetails = salaryService.calculateSalaryDetailDatas(employeeFixedSalary, startTime, endTime);
     * </pre>
     */
    List<IObjectData> calculateSalaryDetailDatas(IObjectData employeeFixedSalaryObj, long startTime, long endTime);

    /**
     * 生成工资条对象，根据员工固定薪资对象、时间范围和薪资明细数据生成工资条。
     * <p>
     * 该方法会执行以下操作：
     * <ol>
     *   <li>从employeeFixedSalaryObj获取员工信息</li>
     *   <li>查询并删除已存在的工资条数据，避免重复数据</li>
     *   <li>创建新的工资条对象，设置员工信息、时间范围等字段</li>
     *   <li>计算薪资总额（应发工资）并设置到工资条对象中</li>
     *   <li>保存工资条对象</li>
     *   <li>更新薪资明细对象，关联到新生成的工资条</li>
     * </ol>
     * <p>
     * 如果salaryDetailDatas为空，仍然会创建一个空的工资条对象，但不会关联任何薪资明细。
     *
     * @param employeeFixedSalaryObj 员工固定薪资对象，包含员工信息和固定薪资数据
     * @param startTime 工资条的开始时间，毫秒时间戳
     * @param endTime 工资条的结束时间，毫秒时间戳
     * @param salaryDetailDatas 薪资明细数据列表，可以为空
     * @return 生成的工资条对象(SalaryDataObj)
     * @throws RuntimeException 如果生成工资条过程中发生错误
     *
     * @see com.facishare.crm.fmcg.wq.constants.SalaryDataFields
     * @see com.facishare.crm.fmcg.wq.constants.SalaryDetailDataFields
     *
     * <pre>
     * // 示例用法
     * IObjectData employeeFixedSalary = employeeFixedSalaryDao.getById(tenantId, employeeFixedSalaryId);
     * long startTime = DateUtils.parseDate("2023-01-01").getTime();
     * long endTime = DateUtils.parseDate("2023-01-31").getTime();
     * List<IObjectData> salaryDetails = salaryService.calculateSalaryDetailDatas(employeeFixedSalary, startTime, endTime);
     * IObjectData salaryData = salaryService.generateSalaryData(employeeFixedSalary, startTime, endTime, salaryDetails);
     * </pre>
     */
    IObjectData generateSalaryData(IObjectData employeeFixedSalaryObj, long startTime, long endTime, List<IObjectData> salaryDetailDatas);

    /**
     * 生成工资条对象，根据员工固定薪资对象、时间范围和薪资明细数据生成工资条。
     * <p>
     * 该方法与上面的generateSalaryData方法类似，但增加了allowModify参数来控制是否允许修改已发放或已修正的薪资明细。
     * <p>
     * 当allowModify为false时，如果存在已发放的薪资明细，将抛出异常。
     * <p>
     * 对于已修正的薪资明细，将保留其数据内容，而不使用新计算的数据。
     *
     * @param employeeFixedSalaryObj 员工固定薪资对象，包含员工信息和固定薪资数据
     * @param startTime 工资条的开始时间，毫秒时间戳
     * @param endTime 工资条的结束时间，毫秒时间戳
     * @param salaryDetailDatas 薪资明细数据列表，可以为空
     * @param allowModify 是否允许修改已发放或已修正的薪资明细
     * @return 生成的工资条对象(SalaryDataObj)
     * @throws ValidateException 如果存在已发放的薪资明细且allowModify为false
     * @throws RuntimeException 如果生成工资条过程中发生其他错误
     */
    IObjectData generateSalaryData(IObjectData employeeFixedSalaryObj, long startTime, long endTime, List<IObjectData> salaryDetailDatas, boolean allowModify);

    /**
     * 根据工资发放单据聚合 工资条数据
     */
    IObjectData handleSalaryPaymentSlipObj(IObjectData salaryPaymentSlipObj);


    /**
     * 更具工资发放单下发薪资
     * 1.给员工发送消息，导航到工资条信息
     * 2.更新工资条状态为已发放
     * 3.更新工资发放单状态为已发放
     *
     * @param salaryPaymentSlipObj 工资发放单据
     * @return 处理结果
     */
    IObjectData paySalaryBySalaryPaymentSlip(IObjectData salaryPaymentSlipObj);

    /**
     * 获取薪资规则
     *
     * @param ruleId 规则ID
     * @return 薪资规则
     */
    ObjectDataDocument getSalaryRule(String ruleId);

    /**
     * 根据条件查询薪资规则
     *
     * @param conditions 查询条件
     * @return 薪资规则列表
     */
    List<ObjectDataDocument> querySalaryRules(Map<String, Object> conditions);

    /**
     * 保存薪资规则
     *
     * @param ruleData 规则数据
     * @return 保存后的规则
     */
    ObjectDataDocument saveSalaryRule(ObjectDataDocument ruleData);

    /**
     * 根据员工和周期获取薪资明细
     *
     * @param employeeId 员工ID
     * @param cycle      薪资周期
     * @return 薪资明细
     */
    ObjectDataDocument getSalaryDetail(String employeeId, String cycle);

    /**
     * 根据条件查询薪资明细
     *
     * @param conditions 查询条件
     * @return 薪资明细列表
     */
    List<ObjectDataDocument> querySalaryDetails(Map<String, Object> conditions);

    /**
     * 获取薪资汇总数据
     *
     * @param departmentId 部门ID
     * @param cycle        薪资周期
     * @return 薪资汇总数据
     */
    ObjectDataDocument getSalarySummary(String departmentId, String cycle);

    /**
     * 发放薪资
     *
     * @param detailId 薪资明细ID
     * @return 操作结果
     */
    boolean paySalary(String detailId);

    /**
     * 批量发放薪资
     *
     * @param detailIds 薪资明细ID列表
     * @return 操作结果
     */
    boolean batchPaySalary(List<String> detailIds);

    /**
     * 导出薪资明细
     *
     * @param conditions 查询条件
     * @return 导出文件路径
     */
    String exportSalaryDetails(Map<String, Object> conditions);

    /**
     * 根据员工ID查询工资条
     * <p>
     * 该方法查询指定员工的所有工资条数据。
     *
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @return 工资条对象列表
     */
    List<IObjectData> getSalaryDataByEmployeeId(String tenantId, String employeeId);

    /**
     * 根据外部员工ID查询工资条
     * <p>
     * 该方法查询指定外部员工的所有工资条数据。
     *
     * @param tenantId 租户ID
     * @param employeeExternalId 外部员工ID
     * @return 工资条对象列表
     */
    List<IObjectData> getSalaryDataByEmployeeExternalId(String tenantId, String employeeExternalId);

    /**
     * 批量发送工资条消息给员工
     * <p>
     * 该方法将指定的工资条信息批量发送给员工。
     * 根据用户类型区分不同的通知方式：
     * <ul>
     *   <li>对于内部用户（employeeId有效），使用CRM通知方式发送消息</li>
     *   <li>对于外部用户（employeeExternalId有效），使用通知公告方式发送消息</li>
     * </ul>
     *
     * @param tenantId 租户ID
     * @param salaryDataList 工资条对象列表
     * @return 成功发送的工资条数量
     */
    int sendSalaryDataMessages(String tenantId, List<IObjectData> salaryDataList);

    /**
     * 发送工资条消息给员工（单个工资条）
     * <p>
     * 该方法将指定的工资条信息发送给员工。
     *
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @param salaryDataId 工资条ID
     * @return 是否发送成功
     * @deprecated 请使用 {@link #sendSalaryDataMessages(String, List)} 方法代替
     */
    @Deprecated
    boolean sendSalaryDataMessage(String tenantId, String employeeId, String salaryDataId);
}