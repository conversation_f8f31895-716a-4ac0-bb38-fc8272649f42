package com.facishare.crm.fmcg.wq.notify;

import com.facishare.qixin.converter.QXEIEAConverter;
import com.fxiaoke.constant.GenerateUrlType;
import com.fxiaoke.enterpriserelation2.arg.UnionMessageSendArg;
import com.fxiaoke.enterpriserelation2.arg.UnionTextMessage;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.service.UnionMessageService;
import com.fxiaoke.model.message.SendTextCardMessageArg;
import com.fxiaoke.model.message.SendTextLinkMessageArg;
import com.fxiaoke.model.message.SendTextMessageArg;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-05-30 16:05
 **/
public class OutUnionMessageServiceImpl  {
    @Autowired
    private UnionMessageService unionMessageService;
    @Autowired
    private QXEIEAConverter qxEIEAConverter;

    public BaseResult sendTextMessage(SendTextMessageArg arg, String appId) {
        HeaderObj headerObj =  HeaderObj.newInstance(arg.getEi());
        UnionMessageSendArg unionMessageSendArg = new UnionMessageSendArg();
        UnionTextMessage unionTextMessage = new UnionTextMessage();
        unionTextMessage.setContent(arg.getMessageContent());
        unionMessageSendArg.setFsAppId(appId);
        unionMessageSendArg.setMessage(unionTextMessage);
        unionMessageSendArg.setLinkType(getLinType(arg.getReceiverChannelData()));
        unionMessageSendArg.setSenderEi(arg.getEi());
        unionMessageSendArg.setMessageType(UnionMessageSendArg.UnionMessageType.TEXT.getType());
        addChannelInfoForArg(unionMessageSendArg, arg.getReceiverChannelData());
        Set<Long> receiverIds = new HashSet<>();
        if (arg.getOutEmployees() == null || arg.getOutEmployees().isEmpty()) {
            return new BaseResult(arg.getUuid(), HttpResponseCode.FAIL, "outEmployees is null");
        }
        arg.getOutEmployees().stream().forEach(x -> receiverIds.add((long) x));
        unionMessageSendArg.setReceiverIds(receiverIds);
        int retryTime = messageServerConfig.getRetry();
        for (int i = 0; i < retryTime; i++) {
            try {
                RestResult<Void> result = unionMessageService.sendMessage(headerObj, unionMessageSendArg);
                if (result != null) {
                    logger.info("sendMessage result {} by unionMessageSendArg {},uuid {}", result, unionMessageSendArg, arg.getUuid());
                    return new BaseResult(arg.getUuid(), HttpResponseCode.SUCCESS, result.getErrMsg());
                }
            } catch (Exception e) {
                if (i == retryTime - 1) {
                    logger.error("sendTextMessage error by ei {},uuid {}", arg.getEi(), arg.getUuid(), e);
                    return new BaseResult(arg.getUuid(), HttpResponseCode.FAIL, e.getMessage());
                }
            }
        }
        return new BaseResult(arg.getUuid(), HttpResponseCode.SUCCESS, "success");
    }

    private void addChannelInfoForArg(UnionMessageSendArg unionMessageSendArg, String receiverChannelData){
        if (StringUtils.isBlank(receiverChannelData)){
            unionMessageSendArg.setChannels(Sets.newHashSet(UnionAuthTypeEnum.FS.getType()));
            return;
        }
        try {
            JSONObject jsonObject = JSON.parseObject(receiverChannelData);
            Integer outChannelType = jsonObject.getInteger("outChannelType");
            if (outChannelType != null){
                unionMessageSendArg.setChannels(Sets.newHashSet(outChannelType));
            }
            String outChannelData = jsonObject.getString("outChannelData");
            if (outChannelData != null){
                unionMessageSendArg.setWxServiceAppIds(Sets.newHashSet(outChannelData));
            }
        } catch (Exception e){
            logger.warn("can not add ChannelInfo by:{}", receiverChannelData, e);
        }

    }

    private int getLinType(String receiverChannelData) {
        JSONObject appIdJson = JSON.parseObject(receiverChannelData);
        if (appIdJson.get("linkType") != null) {
            return appIdJson.getInteger("linkType");
        } else {
            return LinkTypeEnum.ER.getType();
        }
    }

    @Override
    public BaseResult sendTextLinkMessage(SendTextLinkMessageArg arg, String appId) {
        HeaderObj headerObj = HeaderObj.newInstance(arg.getEi());
        UnionMessageSendArg unionMessageSendArg = new UnionMessageSendArg();
        UnionCardMessage unionCardMessage = new UnionCardMessage();
        String url = arg.getOutPlatformUrl();
        if (arg.getGenerateUrlType() == GenerateUrlType.crmUrl) {
            url = URLBuildUtils.generatorEventLinkAddress(arg.getObjectApiName(), arg.getObjectId(),
                    qxEIEAConverter.enterpriseIdToEa(arg.getEi()));
        }
        unionCardMessage.setForwardUrl(url);
        unionCardMessage.setSummary(arg.getMessageContent());
        unionCardMessage.setTile(arg.getTitle());
        addTemplateIdForOutCardMessage(unionCardMessage, arg.getReceiverChannelData());

        Map<String, LinkedHashMap<String, String>> templateIdKeyMap = new LinkedHashMap<>();
        templateIdKeyMap.put(unionCardMessage.getTemplateIdForWechatServiceMsg(), new LinkedHashMap<>());
        unionCardMessage.setTemplateIdKeyMap(templateIdKeyMap);
        unionCardMessage.setExtraDataMap(arg.getExtraDataMap());
        unionMessageSendArg.setFsAppId(appId);
        unionMessageSendArg.setMessage(unionCardMessage);
        unionMessageSendArg.setLinkType(getLinType(arg.getReceiverChannelData()));
        unionMessageSendArg.setSenderEi(arg.getEi());
        unionMessageSendArg.setMessageType(UnionMessageSendArg.UnionMessageType.CARD.getType());
        addChannelInfoForArg(unionMessageSendArg, arg.getReceiverChannelData());
        Set<Long> receiverIds = new HashSet<>();
        if (arg.getOutEmployees() == null || arg.getOutEmployees().isEmpty()) {
            return new BaseResult(arg.getUuid(), HttpResponseCode.FAIL, "outEmployees is null");
        }
        arg.getOutEmployees().stream().forEach(x -> receiverIds.add((long) x));
        unionMessageSendArg.setReceiverIds(receiverIds);
        int retryTime = messageServerConfig.getRetry();
        for (int i = 0; i < retryTime; i++) {
            try {
                RestResult<Void> result = unionMessageService.sendMessage(headerObj, unionMessageSendArg);
                if (result != null) {
                    logger.info("sendTextLinkMessage result {} by unionMessageSendArg {},,uuid {}", result, unionMessageSendArg, arg.getUuid());
                    return new BaseResult(arg.getUuid(), HttpResponseCode.SUCCESS, result.getErrMsg());
                }
            } catch (Exception e) {
                if (i == retryTime - 1) {
                    logger.error("sendTextLinkMessage error by ei {},uuid {}", arg.getEi(), arg.getUuid(), e);
                    return new BaseResult(arg.getUuid(), HttpResponseCode.FAIL, e.getMessage());
                }
            }
        }
        return new BaseResult(arg.getUuid(), HttpResponseCode.SUCCESS, "success");
    }

    private void addTemplateIdForOutCardMessage(UnionCardMessage unionCardMessage, String receiverChannelData){
        if (StringUtils.isBlank(receiverChannelData)){
            return;
        }
        try {
            JSONObject jsonObject = JSON.parseObject(receiverChannelData);
            String templateIdForOut = jsonObject.getString("templateIdForOut");
            if (StringUtils.isNotBlank(templateIdForOut)){
                unionCardMessage.setTemplateIdForWechatServiceMsg(templateIdForOut);
            }
        } catch (Exception e){
            logger.warn("can not getTemplateIdForOut by:{}", receiverChannelData, e);
        }
    }

    @Override
    public BaseResult sendTextCardMessage(SendTextCardMessageArg arg, String appId) {
        HeaderObj headerObj = HeaderObj.newInstance(arg.getEi());
        UnionMessageSendArg unionMessageSendArg = new UnionMessageSendArg();
        UnionCardMessage unionCardMessage = new UnionCardMessage();
        String url = arg.getTextCardMessage().getOutPlatformUrl();
        if (arg.getGenerateUrlType() == GenerateUrlType.crmUrl) {
            url = URLBuildUtils.generatorEventLinkAddress(arg.getObjectApiName(), arg.getObjectId(), qxEIEAConverter.enterpriseIdToEa(arg.getEi()));
        }
        unionCardMessage.setForwardUrl(url);
        unionCardMessage.setSummary(arg.getTextCardMessage().getBody().getContentElement().getText());
        Map<String, String> contents = new LinkedHashMap<>();
        if (arg.getTextCardMessage().getBody().getForm() != null && !arg.getTextCardMessage().getBody().getForm().isEmpty()) {
            arg.getTextCardMessage().getBody().getForm().stream().forEach(x -> contents.put(x.getKeyElement().getText(), x.getValueElement().getText()));
        }
        unionCardMessage.setContents(contents);
        if (null != arg.getTextCardMessage().getBody().getRemarkElement()){
            unionCardMessage.setRemark(arg.getTextCardMessage().getBody().getRemarkElement().getText());
        }
        unionCardMessage.setTile(arg.getTextCardMessage().getHead().getTitleElement().getText());
        addTemplateIdForOutCardMessage(unionCardMessage, arg.getReceiverChannelData());
        Map<String, LinkedHashMap<String, String>> templateIdKeyMap = arg.getTemplateIdKeyMap();
        if (templateIdKeyMap == null){
            templateIdKeyMap = new LinkedHashMap<>();
        }
        if (!templateIdKeyMap.containsKey(unionCardMessage.getTemplateIdForWechatServiceMsg())){
            LinkedHashMap<String, String> keyMap = new LinkedHashMap<>();
            for (Map.Entry<String, String> entry: contents.entrySet()){
                keyMap.put(entry.getKey(), entry.getKey());
            }
            templateIdKeyMap.put(unionCardMessage.getTemplateIdForWechatServiceMsg(), keyMap);
        }
        unionCardMessage.setTemplateIdKeyMap(templateIdKeyMap);
        unionCardMessage.setExtraDataMap(arg.getExtraDataMap());
        unionMessageSendArg.setFsAppId(appId);
        unionMessageSendArg.setMessage(unionCardMessage);
        unionMessageSendArg.setLinkType(getLinType(arg.getReceiverChannelData()));
        unionMessageSendArg.setSenderEi(arg.getEi());
        unionMessageSendArg.setMessageType(UnionMessageSendArg.UnionMessageType.CARD.getType());
        addChannelInfoForArg(unionMessageSendArg, arg.getReceiverChannelData());
        Set<Long> receiverIds = new HashSet<>();
        if (arg.getOutEmployees() == null || arg.getOutEmployees().isEmpty()) {
            return new BaseResult(arg.getUuid(), HttpResponseCode.FAIL, "outEmployees is null");
        }
        arg.getOutEmployees().stream().forEach(x -> receiverIds.add((long) x));
        unionMessageSendArg.setReceiverIds(receiverIds);
        int retryTime = messageServerConfig.getRetry();
        for (int i = 0; i < retryTime; i++) {
            try {
                RestResult<Void> result = unionMessageService.sendMessage(headerObj, unionMessageSendArg);
                if (result != null) {
                    logger.info("sendMessage result {} by unionMessageSendArg {},uuid {}", result, unionMessageSendArg, arg.getUuid());
                    return new BaseResult(arg.getUuid(), HttpResponseCode.SUCCESS, result.getErrMsg());
                }
            } catch (Exception e) {
                if (i == retryTime - 1) {
                    logger.error("sendTextCardMessage by ei {},uuid {}", arg.getEi(), arg.getUuid(), e);
                    return new BaseResult(arg.getUuid(), HttpResponseCode.FAIL, e.getMessage());
                }
            }
        }
        return new BaseResult(arg.getUuid(), HttpResponseCode.SUCCESS, "success");
    }
}