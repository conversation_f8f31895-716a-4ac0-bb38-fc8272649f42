
package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryDetailFields;
import com.facishare.crm.fmcg.wq.constants.SalaryDetailDataFields;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-05-07 16:51
 **/
@Slf4j
@Component
public class EmployeeFixedSalaryDetailDao extends AbstractDao{
    final static List<String> employeeFixedSalaryDetailFields = ConfigUtils.getFields(EmployeeFixedSalaryDetailFields.class);



    public List<IObjectData> getByIds(String tenantId, List<String> kpiIds) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(BaseField.id.getApiName(), kpiIds)
                .build(), EmployeeFixedSalaryDetailFields.API_NAME, employeeFixedSalaryDetailFields);
    }
    /**
     * 通过员工固定工资id 查询员工固定工资明细
     */
    public List<IObjectData> getByEmployeeFixedSalaryMainId(String tenantId, String employeeFixedSalaryId) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY, employeeFixedSalaryId)
                .build(), EmployeeFixedSalaryDetailFields.API_NAME, employeeFixedSalaryDetailFields);
    }
}
