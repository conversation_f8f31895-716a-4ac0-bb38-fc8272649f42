package com.facishare.crm.fmcg.wq.command;

import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.crm.fmcg.wq.service.impl.SalaryServiceImpl;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 导出薪资明细命令
 * @author: dev
 * @create: 2024-07-10
 */
@Slf4j
public class ExportSalaryDetailsCommand implements SalaryCommand<String> {

    private final Map<String, Object> conditions;
    private final SalaryServiceImpl salaryService;

    /**
     * 构造函数
     *
     * @param conditions 查询条件
     * @param salaryService 薪资服务
     */
    public ExportSalaryDetailsCommand(Map<String, Object> conditions, SalaryService salaryService) {
        this.conditions = conditions;
        this.salaryService = (SalaryServiceImpl) salaryService;
    }

    @Override
    public String execute() {
        log.info("执行薪资明细导出命令, 条件: {}", conditions);
        return salaryService.doExportSalaryDetails(conditions);
    }
}
