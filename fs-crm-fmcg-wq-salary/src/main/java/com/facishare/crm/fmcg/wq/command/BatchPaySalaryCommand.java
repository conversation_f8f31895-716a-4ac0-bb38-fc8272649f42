package com.facishare.crm.fmcg.wq.command;

import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.crm.fmcg.wq.service.impl.SalaryServiceImpl;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 批量发放薪资命令
 * @author: dev
 * @create: 2024-07-10
 */
@Slf4j
public class BatchPaySalaryCommand implements SalaryCommand<Boolean> {

    private final List<String> detailIds;
    private final SalaryServiceImpl salaryService;

    /**
     * 构造函数
     *
     * @param detailIds 薪资明细ID列表
     * @param salaryService 薪资服务
     */
    public BatchPaySalaryCommand(List<String> detailIds, SalaryService salaryService) {
        this.detailIds = detailIds;
        this.salaryService = (SalaryServiceImpl) salaryService;
    }

    @Override
    public Boolean execute() {
        log.info("执行批量薪资发放命令, 明细ID数量: {}", detailIds.size());
        return salaryService.doBatchPaySalary(detailIds);
    }
}
