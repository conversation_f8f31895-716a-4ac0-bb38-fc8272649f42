package com.facishare.crm.fmcg.wq.service.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.command.*;
import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.dao.*;
import com.facishare.crm.fmcg.wq.factory.SalaryKPIFactory;
import com.facishare.crm.fmcg.wq.model.MetricCalculateResult;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.notify.SalaryMessageService;
import com.facishare.crm.fmcg.wq.service.SalaryExpressionCalcService;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.crm.fmcg.wq.session.FmcgPushSession;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.expression.ExpressionDTO;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.facishare.paas.appframework.core.model.User;

import static com.facishare.paas.appframework.common.service.model.NewCrmNotification.CUSTOM_REMIND_RECORD_TYPE;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 薪资服务实现类
 * @author: dev
 * @create: 2024-06-23
 */
@Service
@Slf4j
public class SalaryServiceImpl implements SalaryService {

    @Autowired
    private SalaryDao salaryDao;
    @Autowired
    private SalaryRuleDao salaryRuleDao;
    @Autowired
    private SalaryItemDao salaryItemDao;
    @Autowired
    private SalaryDataDao salaryDataDao;
    @Autowired
    private EmployeeKPIDao employeeKPIDao;
    @Autowired
    private SalaryDetailDataDao salaryDetailDataDao;
    @Autowired
    private EmployeeFixedSalaryDetailDao employeeFixedSalaryDetailDao;

    @Autowired
    private SalaryPaymentSlipDao salaryPaymentSlipDao;

    @Autowired
    private SalaryExpressionCalcService salaryExpressionCalcService;

    @Autowired
    private CommandInvoker commandInvoker;

    @Autowired
    private SalaryKPIFactory salaryKPIFactory;

    @Autowired
    private FmcgPushSession fmcgPushSession;

    @Autowired
    public CRMNotificationService crmNotificationService;

    @Autowired
    private SalaryMessageService salaryMessageService;

    @Override
    public List<IObjectData> calculateSalaryDetailDatas(IObjectData employeeFixedSalaryObj, long startTime, long endTime) {
        // 使用命令模式执行薪资计算
        CalculateSalaryCommand command = new CalculateSalaryCommand(employeeFixedSalaryObj, startTime, endTime, this);
        return commandInvoker.execute(command);
    }

    @Override
    public IObjectData generateSalaryData(IObjectData employeeFixedSalaryObj, long startTime, long endTime, List<IObjectData> salaryDetailDatas) {
        return generateSalaryData(employeeFixedSalaryObj, startTime, endTime, salaryDetailDatas, true);
    }

    /**
     * 生成工资条对象，根据员工固定薪资对象、时间范围和薪资明细数据生成工资条。
     * 增加allowModify参数控制是否允许修改已发放或已修正的薪资明细。
     *
     * @param employeeFixedSalaryObj 员工固定薪资对象，包含员工信息和固定薪资数据
     * @param startTime 工资条的开始时间，毫秒时间戳
     * @param endTime 工资条的结束时间，毫秒时间戳
     * @param salaryDetailDatas 薪资明细数据列表，可以为空
     * @param allowModify 是否允许修改已发放或已修正的薪资明细
     * @return 生成的工资条对象(SalaryDataObj)
     * @throws RuntimeException 如果生成工资条过程中发生错误
     */
    public IObjectData generateSalaryData(IObjectData employeeFixedSalaryObj, long startTime, long endTime, List<IObjectData> salaryDetailDatas, boolean allowModify) {
        log.info("生成工资条，employeeId: {}, startTime: {}, endTime: {}, 明细数量: {}",
                employeeFixedSalaryObj.getId(), startTime, endTime,
                salaryDetailDatas != null ? salaryDetailDatas.size() : 0);

        try {
            // 1. 从employeeFixedSalaryObj获取员工信息
            ObjectDataExt employeeFixedSalaryObjExt = ObjectDataExt.of(employeeFixedSalaryObj);
            String tenantId = employeeFixedSalaryObjExt.getTenantId();
            String employeeId = employeeFixedSalaryObjExt.getEmployeeFieldValue(EmployeeFixedSalaryFields.EMPLOYEE);
            String employeeExternalId = employeeFixedSalaryObjExt.getEmployeeFieldValue(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL);

            // 2. 查询已存在的工资条数据
            User systemUser = User.systemUser(tenantId);
            List<IObjectData> existingSalaryDatas = null;

            if (employeeId != null && !employeeId.isEmpty()) {
                existingSalaryDatas = salaryDataDao.getByEmployeeIdAndTimeRange(tenantId, employeeId, startTime, endTime);
            } else if (employeeExternalId != null && !employeeExternalId.isEmpty()) {
                existingSalaryDatas = salaryDataDao.getByEmployeeExternalIdAndTimeRange(tenantId, employeeExternalId, startTime, endTime);
            }

            // 存储已修正的薪资明细数据，用于后续处理
            Map<String, IObjectData> correctedDetailDataMap = new HashMap<>();
            // 存储已有的薪资明细数据，用于后续更新
            Map<String, IObjectData> existingDetailDataMap = new HashMap<>();
            // 用于存储工资条对象
            IObjectData salaryData = null;

            if (existingSalaryDatas != null && !existingSalaryDatas.isEmpty()) {
                log.info("发现已存在的工资条数据，数量: {}", existingSalaryDatas.size());
                // 使用第一个已存在的工资条进行更新
                salaryData = existingSalaryDatas.get(0);
                log.info("将使用已存在的工资条进行更新，ID: {}", salaryData.getId());

                // 获取关联的薪资明细
                List<IObjectData> existingDetailDatas = salaryDetailDataDao.getBySalaryDataId(tenantId, salaryData.getId());

                // 检查是否有已发放的薪资明细
                boolean hasDistributedDetails = false;

                if (existingDetailDatas != null && !existingDetailDatas.isEmpty()) {
                    log.info("发现已存在的薪资明细数据，数量: {}", existingDetailDatas.size());

                    for (IObjectData detailData : existingDetailDatas) {
                        String distributionStatus = detailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS, String.class);
                        String salaryItemId = detailData.get(SalaryDetailDataFields.SALARY_ITEM, String.class);

                        // 检查是否为已发放状态
                        if (SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_3.equals(distributionStatus)) {
                            if (!allowModify) {
                                log.error("存在已发放的薪资明细，不允许修改，明细ID: {}", detailData.getId());
                                throw new ValidateException("存在已发放的薪资明细，不允许修改"); //ignoreI18n
                            }
                            hasDistributedDetails = true;
                        }

                        // 如果是已修正状态，保存到映射中
                        if (SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_4.equals(distributionStatus)) {
                            if (salaryItemId != null && !salaryItemId.isEmpty()) {
                                log.info("发现已修正的薪资明细，将保留其数据，明细ID: {}, 工资项ID: {}",
                                        detailData.getId(), salaryItemId);
                                correctedDetailDataMap.put(salaryItemId, detailData);
                            }
                        }

                        // 将所有已有明细保存到映射中，用于后续更新
                        if (salaryItemId != null && !salaryItemId.isEmpty()) {
                            existingDetailDataMap.put(salaryItemId, detailData);
                        }
                    }
                }

                // 如果有多个工资条，记录日志但不处理
                if (existingSalaryDatas.size() > 1) {
                    log.warn("发现多个工资条数据，将只更新第一个，其他工资条将被忽略");
                }
            } else {
                // 3. 如果不存在工资条，则创建新的工资条对象
                log.info("未发现已存在的工资条数据，将创建新的工资条");
                salaryData = salaryDataDao.createSalaryData(tenantId);
            }

            // 4. 设置员工信息
            String mainDepartment = employeeFixedSalaryObjExt.getStringValue(EmployeeFixedSalaryFields.MAIN_DEPARTMENT);
            String phoneNumber = employeeFixedSalaryObjExt.getStringValue(EmployeeFixedSalaryFields.PHONE_NUMBER);
            String mobileNumberExternal = employeeFixedSalaryObjExt.getStringValue(EmployeeFixedSalaryFields.MOBILE_NUMBER_EXTERNAL);
            String connectedRole = employeeFixedSalaryObjExt.getStringValue(EmployeeFixedSalaryFields.CONNECTED_ROLE);

            // 设置员工相关字段
            salaryData.set(SalaryDataFields.EMPLOYEE, employeeId);
            salaryData.set(SalaryDataFields.EMPLOYEE_EXTERNAL, employeeExternalId);
            salaryData.set(SalaryDataFields.MAIN_DEPARTMENT, mainDepartment);
            salaryData.set(SalaryDataFields.PHONE_NUMBER, phoneNumber);
            salaryData.set(SalaryDataFields.MOBILE_NUMBER_EXTERNAL, mobileNumberExternal);
            salaryData.set(SalaryDataFields.CONNECTED_ROLE, connectedRole);

            // 5. 设置工资条的开始和结束时间
            salaryData.set(SalaryDataFields.START_DATE, startTime);
            salaryData.set(SalaryDataFields.END_DATE, endTime);

            // 6. 关联员工固定薪资对象
            salaryData.set(SalaryDataFields.EMPLOYEE_FIXED_SALARY, employeeFixedSalaryObj.getId());

            // 7. 计算薪资总额（应发工资）和收集工资项ID
            double totalSalary = 0.0;
            List<String> salaryItemIds = new ArrayList<>();

            // 收集所有需要考虑的薪资明细，包括新的和已存在的
            Map<String, IObjectData> allDetailDataMap = new HashMap<>();

            // 先添加新的薪资明细
            if (salaryDetailDatas != null && !salaryDetailDatas.isEmpty()) {
                for (IObjectData detailData : salaryDetailDatas) {
                    String salaryItemId = detailData.get(SalaryDetailDataFields.SALARY_ITEM, String.class);
                    if (salaryItemId != null && !salaryItemId.isEmpty()) {
                        allDetailDataMap.put(salaryItemId, detailData);
                    }
                }
            }

            // 添加已存在的薪资明细（如果不在新的明细中）
            for (Map.Entry<String, IObjectData> entry : existingDetailDataMap.entrySet()) {
                String salaryItemId = entry.getKey();
                if (!allDetailDataMap.containsKey(salaryItemId)) {
                    IObjectData detailData = entry.getValue();
                    String distributionStatus = detailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS, String.class);

                    // 如果是已发放或已修正状态，则包含在计算中
                    if (SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_3.equals(distributionStatus) ||
                        SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_4.equals(distributionStatus)) {
                        allDetailDataMap.put(salaryItemId, detailData);
                    }
                }
            }

            // 计算总金额并收集工资项ID
            for (Map.Entry<String, IObjectData> entry : allDetailDataMap.entrySet()) {
                String salaryItemId = entry.getKey();
                IObjectData detailData = entry.getValue();
                String amountStr = null;

                // 优先使用原始数据的金额
                // 对于已修正的数据，使用已修正的金额
                if (correctedDetailDataMap.containsKey(salaryItemId)) {
                    IObjectData correctedDetailData = correctedDetailDataMap.get(salaryItemId);
                    amountStr = correctedDetailData.get(SalaryDetailDataFields.AMOUNT, String.class);
                    log.info("使用已修正的薪资明细金额，工资项ID: {}, 金额: {}", salaryItemId, amountStr);
                } else if (existingDetailDataMap.containsKey(salaryItemId)) {
                    // 对于已存在的数据，使用原有的金额
                    IObjectData existingDetailData = existingDetailDataMap.get(salaryItemId);
                    amountStr = existingDetailData.get(SalaryDetailDataFields.AMOUNT, String.class);
                    log.info("使用已存在的薪资明细金额，工资项ID: {}, 金额: {}", salaryItemId, amountStr);
                } else {
                    // 使用新数据的金额
                    amountStr = detailData.get(SalaryDetailDataFields.AMOUNT, String.class);
                    log.info("使用新的薪资明细金额，工资项ID: {}, 金额: {}", salaryItemId, amountStr);
                }

                if (amountStr != null && !amountStr.isEmpty()) {
                    try {
                        double amount = Double.parseDouble(amountStr);
                        totalSalary += amount;
                    } catch (NumberFormatException e) {
                        log.warn("薪资明细金额格式错误: {}", amountStr, e);
                    }
                }

                // 收集工资项ID
                salaryItemIds.add(salaryItemId);
            }

            if (allDetailDataMap.isEmpty()) {
                log.info("薪资明细数据为空，将创建一个空的工资条");
            }

            // 设置应付工资
            salaryData.set(SalaryDataFields.PAYABLE_SALARY, String.valueOf(totalSalary));

            // 设置包含的工资项
            if (!salaryItemIds.isEmpty()) {
                salaryData.set(SalaryDataFields.INCLUDE_PAY_ITEMS, salaryItemIds);
            }
            // 工资规则
            salaryData.set(SalaryDataFields.SALARY_RULE, employeeFixedSalaryObjExt.getStringValue(EmployeeFixedSalaryFields.SALARY_RULE));
            // 8. 保存工资条对象
            IObjectData savedSalaryData = salaryDataDao.saveSalaryData(systemUser, salaryData);

            // 9. 更新薪资明细，关联到工资条
            if (salaryDetailDatas != null && !salaryDetailDatas.isEmpty()) {
                for (IObjectData detailData : salaryDetailDatas) {
                    // 获取工资项ID
                    String salaryItemId = detailData.get(SalaryDetailDataFields.SALARY_ITEM, String.class);
                    if (salaryItemId == null || salaryItemId.isEmpty()) {
                        log.warn("薪资明细数据缺少工资项ID，将创建新的薪资明细");
                        // 关联到工资条
                        detailData.set(SalaryDetailDataFields.SALARY_DATA, savedSalaryData.getId());

                        // 如果没有设置发放状态，设置为未发放
                        if (detailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS) == null) {
                            detailData.set(SalaryDetailDataFields.DISTRIBUTION_STATUS, SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_0);
                        }

                        // 创建新的薪资明细
                        salaryDetailDataDao.saveSalaryDetailData(systemUser, detailData);
                        continue;
                    }

                    IObjectData detailDataToUpdate = null;
                    boolean isNewDetail = false;

                    // 检查是否有已存在的明细数据
                    if (existingDetailDataMap.containsKey(salaryItemId)) {
                        // 使用已存在的薪资明细进行更新
                        detailDataToUpdate = existingDetailDataMap.get(salaryItemId);
                        log.info("将更新已存在的薪资明细，明细ID: {}, 工资项ID: {}",
                                detailDataToUpdate.getId(), salaryItemId);
                    } else {
                        // 创建新的薪资明细
                        detailDataToUpdate = detailData;
                        isNewDetail = true;
                        log.info("创建新的薪资明细，工资项ID: {}", salaryItemId);
                    }

                    // 对于已修正的数据，保持其状态和数据不变
                    if (correctedDetailDataMap.containsKey(salaryItemId)) {
                        log.info("保持已修正的薪资明细数据不变，工资项ID: {}", salaryItemId);
                        // 不做任何修改，保持原有数据
                    } else if (!isNewDetail) {
                        // 对于已存在的明细，不更新其字段值，只更新关联
                        log.info("保持已存在的薪资明细数据不变，只更新关联，工资项ID: {}", salaryItemId);

                        // 如果没有状态，设置为未发放（这是唯一可能需要设置的字段）
                        String status = detailDataToUpdate.get(SalaryDetailDataFields.DISTRIBUTION_STATUS, String.class);
                        if (status == null || status.isEmpty()) {
                            detailDataToUpdate.set(SalaryDetailDataFields.DISTRIBUTION_STATUS, SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_0);
                        }
                    }

                    // 关联到工资条
                    detailDataToUpdate.set(SalaryDetailDataFields.SALARY_DATA, savedSalaryData.getId());

                    // 保存或更新薪资明细
                    if (isNewDetail) {
                        salaryDetailDataDao.saveSalaryDetailData(systemUser, detailDataToUpdate);
                    } else {
                        salaryDetailDataDao.updateSalaryDetailData(systemUser, detailDataToUpdate);
                    }
                }
            }

            // 处理已存在但不在新数据中的薪资明细
            if (!existingDetailDataMap.isEmpty() && salaryDetailDatas != null && !salaryDetailDatas.isEmpty()) {
                // 收集新数据中的工资项ID
                Set<String> newSalaryItemIds = new HashSet<>();
                for (IObjectData detailData : salaryDetailDatas) {
                    String salaryItemId = detailData.get(SalaryDetailDataFields.SALARY_ITEM, String.class);
                    if (salaryItemId != null && !salaryItemId.isEmpty()) {
                        newSalaryItemIds.add(salaryItemId);
                    }
                }

                // 检查已存在但不在新数据中的薪资明细
                for (Map.Entry<String, IObjectData> entry : existingDetailDataMap.entrySet()) {
                    String salaryItemId = entry.getKey();
                    if (!newSalaryItemIds.contains(salaryItemId)) {
                        IObjectData detailData = entry.getValue();
                        String distributionStatus = detailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS, String.class);

                        // 对于所有已存在的明细，只更新关联ID，保持其他数据不变
                        log.info("更新已存在的薪资明细关联，明细ID: {}, 工资项ID: {}",
                                detailData.getId(), salaryItemId);
                        // 只更新关联到新的工资条
                        detailData.set(SalaryDetailDataFields.SALARY_DATA, savedSalaryData.getId());
                        salaryDetailDataDao.updateSalaryDetailData(systemUser, detailData);
                    }
                }
            }

            log.info("工资条生成成功，ID: {}, 员工ID: {}, 应付工资: {}",
                    savedSalaryData.getId(), employeeId != null ? employeeId : employeeExternalId, totalSalary);

            return savedSalaryData;
        } catch (Exception e) {
            log.error("生成工资条失败", e);
            throw new RuntimeException("生成工资条失败: " + e.getMessage(), e);
        }
    }

    @Override
    public IObjectData handleSalaryPaymentSlipObj(IObjectData salaryPaymentSlipObj) {
        log.info("处理工资发放单，ID: {}", salaryPaymentSlipObj.getId());

        // 1. 从salaryPaymentSlipObj获取信息
        ObjectDataExt salaryPaymentSlipObjExt = ObjectDataExt.of(salaryPaymentSlipObj);
        String tenantId = salaryPaymentSlipObjExt.getTenantId();
        User systemUser = User.systemUser(tenantId);
        try {

            // 2. 获取日期范围
            long startTime = salaryPaymentSlipObjExt.get(SalaryPaymentSlipFields.START_DATE, Long.class);
            long endTime = salaryPaymentSlipObjExt.get(SalaryPaymentSlipFields.END_DATE, Long.class);

            if (startTime <= 0 || endTime <= 0) {
                throw new ValidateException("工资发放单的日期范围无效"); //ignoreI18n
            }

            // 3. 查询符合日期范围的工资条记录
            List<IObjectData> salaryDataList = salaryDataDao.getAllIObjectDataListByQueryWithFields(
                    systemUser,
                    SearchQuery.builder()
                            //完全包含
                            .gte(SalaryDataFields.START_DATE, startTime)
                            .lte(SalaryDataFields.END_DATE, endTime)
                            //规则
                            .eq(SalaryDataFields.SALARY_RULE, salaryPaymentSlipObjExt.getStringValue(SalaryPaymentSlipFields.SALARY_RULE))
                            .build(),
                    SalaryDataFields.API_NAME,
                    null
            );

            log.info("查询到符合日期范围的工资条数量: {}", salaryDataList.size());

            if (salaryDataList.isEmpty()) {
                log.warn("未找到符合日期范围的工资条记录，startTime: {}, endTime: {}", startTime, endTime);
                return salaryPaymentSlipObj;
            }

            // 4. 更新工资条，关联到工资发放单
            int updatedCount = 0;
            for (IObjectData salaryData : salaryDataList) {
                // 检查工资条是否已关联到其他工资发放单
                String existingPaymentSlipId = salaryData.get(SalaryDataFields.SALARY_PAYMENT_SLIP, String.class);
                if (existingPaymentSlipId != null && !existingPaymentSlipId.isEmpty() && !existingPaymentSlipId.equals(salaryPaymentSlipObj.getId())) {
                    log.warn("工资条已关联到其他工资发放单，跳过处理，工资条ID: {}, 已关联工资发放单ID: {}",
                            salaryData.getId(), existingPaymentSlipId);
                    continue;
                }

                // 关联工资条到工资发放单
                salaryData.set(SalaryDataFields.SALARY_PAYMENT_SLIP, salaryPaymentSlipObj.getId());
                salaryDataDao.updateSalaryData(systemUser, salaryData);
                updatedCount++;
            }

            log.info("成功关联工资条到工资发放单，工资发放单ID: {}, 关联工资条数量: {}",
                    salaryPaymentSlipObj.getId(), updatedCount);

            // 5. 更新工资发放单状态（如果需要）
            // 更新为已经生成
            salaryPaymentSlipObj.set(SalaryPaymentSlipFields.PAY_STATUS, SalaryPaymentSlipFields.PAY_STATUS_Options_1);
            salaryPaymentSlipObj = salaryPaymentSlipDao.updateSalaryPaymentSlip(systemUser, salaryPaymentSlipObj);

            return salaryPaymentSlipObj;
        } catch (Exception e) {
            //更新状态为发放异常
            salaryPaymentSlipObj.set(SalaryPaymentSlipFields.PAY_STATUS, SalaryPaymentSlipFields.PAY_STATUS_Options_3);
            salaryPaymentSlipObj = salaryPaymentSlipDao.updateSalaryPaymentSlip(systemUser, salaryPaymentSlipObj);
            log.error("处理工资发放单失败", e);
            throw new RuntimeException("处理工资发放单失败: " + e.getMessage(), e);
        }
    }

    /**
     * 实际执行薪资计算的逻辑
     * 此方法由CalculateSalaryCommand调用
     *
     * @param employeeFixedSalaryObj 员工固定薪资对象
     * @param startTime              开始时间
     * @param endTime                结束时间
     * @return 薪资明细列表
     */
    public List<IObjectData> doCalculateSalaryDetailDatas(IObjectData employeeFixedSalaryObj, long startTime, long endTime) {
        List<IObjectData> result = Lists.newArrayList();
        ObjectDataExt employeeFixedSalaryObjExt = ObjectDataExt.of(employeeFixedSalaryObj);
        String tenantId = employeeFixedSalaryObjExt.getTenantId();
        String employeeId = employeeFixedSalaryObjExt.getEmployeeFieldValue(EmployeeFixedSalaryFields.EMPLOYEE);
        if (StringUtils.isBlank(employeeId)) {
            employeeId = employeeFixedSalaryObjExt.getEmployeeFieldValue(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL);
        }
        if (StringUtils.isBlank(employeeId)) {
            throw new ValidateException("员工id为空"); //ignoreI18n
        }
        log.info("计算员工薪资，employeeId: {}, cycleStart: {}, cycleEnd: {}", employeeId, startTime, endTime);
        //1.查薪资规则
        String salaryRuleId = employeeFixedSalaryObjExt.getStringValue(EmployeeFixedSalaryFields.SALARY_RULE);
        if (StringUtils.isBlank(salaryRuleId)) {
            throw new ValidateException("薪资规则id为空"); //ignoreI18n
        }
        IObjectData salaryRule = salaryRuleDao.getById(tenantId, salaryRuleId);
        if (salaryRule == null) {
            throw new ValidateException("薪资规则不存在"); //ignoreI18n
        }
        ObjectDataExt salaryRuleExt = ObjectDataExt.of(salaryRule);
        //2.查工资项
        List<String> salaryItemIds = salaryRuleExt.getDimensionValues(SalaryRuleFields.SALARY_ITEM);
        if (salaryItemIds == null || salaryItemIds.isEmpty()) {
            throw new ValidateException("薪资规则中未配置工资项");//ignoreI18n
        }
        List<IObjectData> salaryItems = salaryItemDao.getbyIds(tenantId, salaryItemIds);
        if (salaryItems == null || salaryItems.isEmpty()) {
            throw new ValidateException("薪资规则中配置的工资项不存在");//ignoreI18n
        }
        SalaryContext salaryContext = SalaryContext.builder()
                .endTime(endTime)
                .startTime(startTime)
                .owner(employeeId)
                .build();
        //3.计算每个薪资项目
        for (IObjectData salaryItem : salaryItems) {
            IObjectData salaryDetailData = salaryDetailDataDao.createSalaryDetailDataByEmployeeFixedSalaryObjExt(employeeFixedSalaryObjExt, startTime, endTime);
            //工资项
            salaryDetailData.set(SalaryDetailDataFields.SALARY_ITEM, salaryItem.getId());
            ObjectDataExt salaryItemExt = ObjectDataExt.of(salaryItem);
            //判断类型
            if (SalaryItemFields.VALUE_TYPE_Options_1.equals(salaryItemExt.getStringValue(SalaryItemFields.VALUE_TYPE))) {
                //查 固定工资明细  EmployeeFixedSalaryDetailObj
                List<IObjectData> employeeFixedSalaryDetailObjs = employeeFixedSalaryDetailDao.getByEmployeeFixedSalaryMainId(tenantId, employeeFixedSalaryObjExt.getId());
                //固定值
                salaryDetailData.set(SalaryDetailDataFields.AMOUNT, Optional.ofNullable(employeeFixedSalaryDetailObjs)
                        .map(o -> o.stream().filter(e -> e.get(EmployeeFixedSalaryDetailFields.SALARY_ITEM).equals(salaryItem.getId())).findFirst().orElse(null))
                        .map(o -> o.get(EmployeeFixedSalaryDetailFields.AMOUNT, String.class)).orElse(null));
                result.add(salaryDetailData);
            } else if (SalaryItemFields.VALUE_TYPE_Options_2.equals(salaryItemExt.getStringValue(SalaryItemFields.VALUE_TYPE))) {

                //计算公式
                ExpressionDTO expressionDTO = JSON.parseObject(salaryItemExt.getStringValue(SalaryItemFields.CALCULATION_FORMULA), ExpressionDTO.class);
                expressionDTO.setExtFields(Lists.newArrayList());
                //3.1 查指标
                List<String> kpiIds = salaryItemExt.getDimensionValues(SalaryItemFields.FORMULA_INCLUDES_KPI);
                if (kpiIds == null || kpiIds.isEmpty()) {
                    throw new ValidateException("工资项中未配置指标");//ignoreI18n
                }
                //3.2 查指标对象
                List<IObjectData> kpiObjs = employeeKPIDao.getbyKpiIds(tenantId, kpiIds);
                for (IObjectData kpiObj : kpiObjs) {
                    //3.3 计算指标值
                    MetricCalculateResult calculate = salaryKPIFactory.calculate(salaryContext, kpiObj);
                    String key = SalaryExpressionCalcServiceImpl.KPI_EXT_START + kpiObj.getId();
                    salaryContext.getExtDataMap().put(key, calculate.getValue());
                    salaryContext.getExtDataNameMap().put(key, kpiObj.getName());
                    expressionDTO.getExtFields().add(ExpressionDTO.FormVariableDTO.builder().fieldName(key).value(calculate.getValue()).build());
                }
                //计算公式
                salaryDetailData.set(SalaryDetailDataFields.CALCULATION_FORMULA, formatFormula(salaryRuleExt.getStringValue(SalaryRuleFields.CALCULATION_FORMULA), salaryContext.getExtDataNameMap()));
                //计算公式赋值
//                salaryDetailData.set(SalaryDetailDataFields.FORMULA_ASSIGNMENT,formatFormula(salaryRuleExt.getStringValue(SalaryRuleFields.CALCULATION_FORMULA), salaryContext.getExtDataMap()));

                //3.4 工资项如果是固定值的话
                //调用计算每个工资项的值
                Pair<String, Object> stringObjectPair = salaryExpressionCalcService.calculateWithExpression(expressionDTO, salaryContext);
                salaryDetailData.set(SalaryDetailDataFields.FORMULA_ASSIGNMENT, stringObjectPair.getKey());
                salaryDetailData.set(SalaryDetailDataFields.AMOUNT, stringObjectPair.getValue());
                result.add(salaryDetailData);
            }
        }
        salaryKPIFactory.clearAllCaches();

        return result;
    }

    private String formatFormula(String formulaStr, Map<String, Object> extDataNameMap) {
        if (formulaStr == null || extDataNameMap == null) {
            return formulaStr;
        }

        for (Map.Entry<String, Object> entry : extDataNameMap.entrySet()) {
            // 使用正则表达式中的特殊字符需要转义
            String key = Pattern.quote("$" + entry.getKey() + "$");
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            formulaStr = formulaStr.replaceAll(key, value);
        }
        return formulaStr;
    }



    @Override
    public ObjectDataDocument getSalaryRule(String ruleId) {
        log.info("获取薪资规则，ruleId: {}", ruleId);
        // TODO: 实现获取薪资规则逻辑
        return null;
    }

    @Override
    public List<ObjectDataDocument> querySalaryRules(Map<String, Object> conditions) {
        log.info("查询薪资规则，conditions: {}", conditions);
        // TODO: 实现查询薪资规则逻辑
        return null;
    }

    @Override
    public ObjectDataDocument saveSalaryRule(ObjectDataDocument ruleData) {
        log.info("保存薪资规则，ruleData: {}", ruleData);
        // TODO: 实现保存薪资规则逻辑
        return null;
    }

    @Override
    public ObjectDataDocument getSalaryDetail(String employeeId, String cycle) {
        log.info("获取薪资明细，employeeId: {}, cycle: {}", employeeId, cycle);
        // TODO: 实现获取薪资明细逻辑
        return null;
    }

    @Override
    public List<ObjectDataDocument> querySalaryDetails(Map<String, Object> conditions) {
        log.info("查询薪资明细，conditions: {}", conditions);
        // TODO: 实现查询薪资明细逻辑
        return null;
    }

    @Override
    public ObjectDataDocument getSalarySummary(String departmentId, String cycle) {
        log.info("获取薪资汇总，departmentId: {}, cycle: {}", departmentId, cycle);
        // TODO: 实现获取薪资汇总逻辑
        return null;
    }

    @Override
    public boolean paySalary(String detailId) {
        log.info("发放薪资，detailId: {}", detailId);
        // 使用命令模式执行薪资发放
        PaySalaryCommand command = new PaySalaryCommand(detailId, this);
        return commandInvoker.execute(command);
    }

    /**
     * 实际执行薪资发放的逻辑
     * 此方法由PaySalaryCommand调用
     *
     * @param detailId 薪资明细ID
     * @return 是否发放成功
     */
    public boolean doPaySalary(String detailId) {
        // TODO: 实现发放薪资逻辑
        // 1. 验证薪资明细状态
        // 2. 更新薪资明细状态为已发放
        // 3. 记录发放日志
        return false;
    }

    @Override
    public boolean batchPaySalary(List<String> detailIds) {
        log.info("批量发放薪资，detailIds: {}", detailIds);
        // 使用命令模式执行批量薪资发放
        BatchPaySalaryCommand command = new BatchPaySalaryCommand(detailIds, this);
        return commandInvoker.execute(command);
    }

    /**
     * 实际执行批量薪资发放的逻辑
     * 此方法由BatchPaySalaryCommand调用
     *
     * @param detailIds 薪资明细ID列表
     * @return 是否全部发放成功
     */
    public boolean doBatchPaySalary(List<String> detailIds) {
        // TODO: 实现批量发放薪资逻辑
        // 1. 验证所有薪资明细状态
        // 2. 批量更新薪资明细状态为已发放
        // 3. 记录发放日志
        return false;
    }

    @Override
    public String exportSalaryDetails(Map<String, Object> conditions) {
        log.info("导出薪资明细，conditions: {}", conditions);
        // 使用命令模式执行薪资明细导出
        ExportSalaryDetailsCommand command = new ExportSalaryDetailsCommand(conditions, this);
        return commandInvoker.execute(command);
    }

    /**
     * 根据工资发放单发放工资
     * <p>
     * 该方法将执行以下操作：
     * <ol>
     *   <li>查询与工资发放单关联的所有工资条</li>
     *   <li>查询每个工资条关联的薪资明细</li>
     *   <li>更新薪资明细的发放状态为“已发放”</li>
     *   <li>更新工资发放单的状态为“已发放”</li>
     * </ol>
     *
     * @param salaryPaymentSlipObj 工资发放单对象
     * @return 更新后的工资发放单对象
     * @throws RuntimeException 如果发放工资过程中发生错误
     */
    @Override
    public IObjectData paySalaryBySalaryPaymentSlip(IObjectData salaryPaymentSlipObj) {
        log.info("根据工资发放单发放工资，ID: {}", salaryPaymentSlipObj.getId());

        // 1. 从工资发放单获取信息
        ObjectDataExt salaryPaymentSlipObjExt = ObjectDataExt.of(salaryPaymentSlipObj);
        String tenantId = salaryPaymentSlipObjExt.getTenantId();
        User systemUser = User.systemUser(tenantId);

        try {
            // 2. 检查工资发放单状态
            String payStatus = salaryPaymentSlipObjExt.getStringValue(SalaryPaymentSlipFields.PAY_STATUS);
            if (SalaryPaymentSlipFields.PAY_STATUS_Options_4.equals(payStatus)) {
                log.warn("工资发放单已经是已发放状态，不需要重复发放，ID: {}", salaryPaymentSlipObj.getId());
                return salaryPaymentSlipObj;
            }

            if (!SalaryPaymentSlipFields.PAY_STATUS_Options_1.equals(payStatus)) {
                log.error("工资发放单状态不正确，无法发放工资，当前状态: {}, ID: {}", payStatus, salaryPaymentSlipObj.getId());
                throw new ValidateException("工资发放单状态不正确，无法发放工资"); //ignoreI18n
            }

            // 3. 查询与工资发放单关联的工资条
            List<IObjectData> salaryDataList = salaryDataDao.getAllIObjectDataListByQueryWithFields(
                    systemUser,
                    com.facishare.crm.fmcg.wq.util.SearchQuery.builder()
                            .eq(SalaryDataFields.SALARY_PAYMENT_SLIP, salaryPaymentSlipObj.getId())
                            .build(),
                    SalaryDataFields.API_NAME,
                    null
            );

            log.info("查询到与工资发放单关联的工资条数量: {}", salaryDataList.size());

            if (salaryDataList.isEmpty()) {
                log.warn("未找到与工资发放单关联的工资条，ID: {}", salaryPaymentSlipObj.getId());
                throw new ValidateException("未找到与工资发放单关联的工资条"); //ignoreI18n
            }

            // 4. 处理每个工资条及其关联的薪资明细
            int totalDetailCount = 0;
            int updatedDetailCount = 0;

            for (IObjectData salaryData : salaryDataList) {
                // 查询工资条关联的薪资明细
                List<IObjectData> detailDataList = salaryDetailDataDao.getBySalaryDataId(tenantId, salaryData.getId());
                totalDetailCount += detailDataList.size();

                log.info("工资条ID: {}, 关联的薪资明细数量: {}", salaryData.getId(), detailDataList.size());

                // 更新每个薪资明细的发放状态
                for (IObjectData detailData : detailDataList) {
                    String distributionStatus = detailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS, String.class);

                    // 如果已经是已发放状态，则跳过
                    if (SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_3.equals(distributionStatus)) {
                        log.info("薪资明细已经是已发放状态，跳过更新，明细ID: {}", detailData.getId());
                        updatedDetailCount++;
                        continue;
                    }

                    // 更新为已发放状态
                    detailData.set(SalaryDetailDataFields.DISTRIBUTION_STATUS, SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_3);
                    salaryDetailDataDao.updateSalaryDetailData(systemUser, detailData);
                    updatedDetailCount++;

                    log.info("更新薪资明细为已发放状态，明细ID: {}", detailData.getId());
                }
            }

            log.info("工资发放单关联的薪资明细总数: {}, 已更新数量: {}", totalDetailCount, updatedDetailCount);

            // 5. 更新工资发放单状态为已发放
            salaryPaymentSlipObj.set(SalaryPaymentSlipFields.PAY_STATUS, SalaryPaymentSlipFields.PAY_STATUS_Options_4);
            salaryPaymentSlipObj = salaryPaymentSlipDao.updateSalaryPaymentSlip(systemUser, salaryPaymentSlipObj);

            log.info("工资发放单发放成功，ID: {}, 关联工资条数量: {}, 薪资明细总数: {}",
                    salaryPaymentSlipObj.getId(), salaryDataList.size(), totalDetailCount);

            return salaryPaymentSlipObj;
        } catch (Exception e) {
            // 发生异常时，更新工资发放单状态为发放异常
            salaryPaymentSlipObj.set(SalaryPaymentSlipFields.PAY_STATUS, SalaryPaymentSlipFields.PAY_STATUS_Options_3);
            salaryPaymentSlipObj = salaryPaymentSlipDao.updateSalaryPaymentSlip(systemUser, salaryPaymentSlipObj);

            log.error("发放工资失败，工资发放单ID: {}", salaryPaymentSlipObj.getId(), e);
            throw new RuntimeException("发放工资失败: " + e.getMessage(), e);
        }
    }

    /**
     * 实际执行薪资明细导出的逻辑
     * 此方法由ExportSalaryDetailsCommand调用
     *
     * @param conditions 查询条件
     * @return 导出文件路径
     */
    public String doExportSalaryDetails(Map<String, Object> conditions) {
        // TODO: 实现导出薪资明细逻辑
        // 1. 根据条件查询薪资明细
        // 2. 生成Excel文件
        // 3. 保存文件并返回路径
        return null;
    }

    @Override
    public List<IObjectData> getSalaryDataByEmployeeId(String tenantId, String employeeId) {
        log.info("根据员工ID查询工资条，tenantId: {}, employeeId: {}", tenantId, employeeId);

        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(employeeId)) {
            log.warn("查询工资条参数无效，tenantId: {}, employeeId: {}", tenantId, employeeId);
            return Lists.newArrayList();
        }

        try {
            // 使用SalaryDataDao查询员工的工资条
            List<IObjectData> salaryDataList = salaryDataDao.getByEmployeeId(tenantId, employeeId);
            log.info("查询到员工工资条数量: {}, employeeId: {}", salaryDataList.size(), employeeId);
            return salaryDataList;
        } catch (Exception e) {
            log.error("查询员工工资条失败，employeeId: {}", employeeId, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<IObjectData> getSalaryDataByEmployeeExternalId(String tenantId, String employeeExternalId) {
        log.info("根据外部员工ID查询工资条，tenantId: {}, employeeExternalId: {}", tenantId, employeeExternalId);

        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(employeeExternalId)) {
            log.warn("查询工资条参数无效，tenantId: {}, employeeExternalId: {}", tenantId, employeeExternalId);
            return Lists.newArrayList();
        }

        try {
            // 使用SalaryDataDao查询外部员工的工资条
            List<IObjectData> salaryDataList = salaryDataDao.getByEmployeeExternalId(tenantId, employeeExternalId);
            log.info("查询到外部员工工资条数量: {}, employeeExternalId: {}", salaryDataList.size(), employeeExternalId);
            return salaryDataList;
        } catch (Exception e) {
            log.error("查询外部员工工资条失败，employeeExternalId: {}", employeeExternalId, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public int sendSalaryDataMessages(String tenantId, List<IObjectData> salaryDataList) {
        log.info("批量发送工资条消息，tenantId: {}, 工资条数量: {}", tenantId, salaryDataList.size());
        
        if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(salaryDataList)) {
            log.warn("批量发送工资条消息参数无效，tenantId: {}, salaryDataList为空", tenantId);
            return 0;
        }
        
        int successCount = 0;
        
        // 分组处理内部员工和外部员工的工资条
        Map<Boolean, List<IObjectData>> groupedSalaryData = salaryDataList.stream()
            .collect(Collectors.partitioningBy(data -> {
                String employeeId = data.get(SalaryDataFields.EMPLOYEE, String.class);
                return StringUtils.isNotBlank(employeeId);
            }));
        
        // 处理内部员工的工资条（使用文本卡片消息）
        List<IObjectData> internalEmployeeSalaryData = groupedSalaryData.get(true);
        if (!CollectionUtils.isEmpty(internalEmployeeSalaryData)) {
            successCount += salaryMessageService.batchSendInternalEmployeeMessages(tenantId, internalEmployeeSalaryData);
        }
        
        // 处理外部员工的工资条（使用通知公告方式）
        List<IObjectData> externalEmployeeSalaryData = groupedSalaryData.get(false);
        if (!CollectionUtils.isEmpty(externalEmployeeSalaryData)) {
            successCount += salaryMessageService.batchSendExternalEmployeeMessages(tenantId, externalEmployeeSalaryData);
        }
        
        log.info("批量发送工资条消息完成，总数: {}, 成功数: {}", salaryDataList.size(), successCount);
        return successCount;
    }

    @Override
    public boolean sendSalaryDataMessage(String tenantId, String employeeId, String salaryDataId) {
        log.info("发送工资条消息给员工，tenantId: {}, employeeId: {}, salaryDataId: {}", tenantId, employeeId, salaryDataId);

        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(employeeId) || StringUtils.isBlank(salaryDataId)) {
            log.warn("发送工资条消息参数无效，tenantId: {}, employeeId: {}, salaryDataId: {}", tenantId, employeeId, salaryDataId);
            return false;
        }

        try {
            // 1. 获取工资条数据
            IObjectData salaryData = salaryDataDao.getById(tenantId, salaryDataId);
            if (salaryData == null) {
                log.warn("未找到工资条数据，salaryDataId: {}", salaryDataId);
                return false;
            }

            // 2. 验证工资条是否属于该员工
            String dataEmployeeId = salaryData.get(SalaryDataFields.EMPLOYEE, String.class);
            String dataEmployeeExternalId = salaryData.get(SalaryDataFields.EMPLOYEE_EXTERNAL, String.class);

            if (!employeeId.equals(dataEmployeeId) && !employeeId.equals(dataEmployeeExternalId)) {
                log.warn("工资条不属于该员工，employeeId: {}, dataEmployeeId: {}, dataEmployeeExternalId: {}",
                        employeeId, dataEmployeeId, dataEmployeeExternalId);
                return false;
            }

            // 3. 根据员工类型选择不同的发送方式
            boolean isInternalEmployee = StringUtils.isNotBlank(dataEmployeeId);
            
            if (isInternalEmployee) {
                // 内部用户：使用文本卡片消息
                return salaryMessageService.sendInternalEmployeeMessage(tenantId, dataEmployeeId, salaryData);
            } else {
                // 外部用户：使用通知公告方式
                return salaryMessageService.sendExternalEmployeeMessage(tenantId, dataEmployeeExternalId, salaryData);
            }
        } catch (Exception e) {
            log.error("发送工资条消息失败，employeeId: {}, salaryDataId: {}", employeeId, salaryDataId, e);
            return false;
        }
    }
}
