package com.facishare.crm.fmcg.wq.service.decorator;

import com.facishare.crm.fmcg.wq.model.MetricCalculateResult;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.model.kpi.SalaryKPI;
import com.facishare.crm.fmcg.wq.service.SalaryKPICalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 带缓存的薪资KPI计算器装饰器
 * @author: dev
 * @create: 2024-07-10
 */
@Slf4j
public class CachingSalaryKPICalculator<T extends SalaryKPI> extends SalaryKPICalculatorDecorator<T> {
    
    private final Map<String, MetricCalculateResult> cache = new ConcurrentHashMap<>();
    
    public CachingSalaryKPICalculator(SalaryKPICalculator<T> decorated) {
        super(decorated);
    }
    
    @Override
    public MetricCalculateResult doCalculate(SalaryContext context, T metric) {
        String cacheKey = generateCacheKey(context, metric);
        
        // 检查缓存中是否存在结果
        if (cache.containsKey(cacheKey)) {
            log.debug("KPI计算缓存命中: {}", cacheKey);
            return cache.get(cacheKey);
        }
        
        // 调用被装饰对象的计算方法
        MetricCalculateResult result = decorated.doCalculate(context, metric);
        
        // 将结果存入缓存
        cache.put(cacheKey, result);
        log.debug("KPI计算结果已缓存: {}", cacheKey);
        
        return result;
    }
    
    /**
     * 生成缓存键
     *
     * @param context 薪资上下文
     * @param metric KPI指标
     * @return 缓存键
     */
    private String generateCacheKey(SalaryContext context, T metric) {
        return String.format("%s_%s_%d_%d",
                metric.getId(),
                context.getOwner(),
                context.getStartTime(),
                context.getEndTime());
    }
    
    /**
     * 清除缓存
     */
    public void clearCache() {
        cache.clear();
        log.info("KPI计算缓存已清除");
    }
}
