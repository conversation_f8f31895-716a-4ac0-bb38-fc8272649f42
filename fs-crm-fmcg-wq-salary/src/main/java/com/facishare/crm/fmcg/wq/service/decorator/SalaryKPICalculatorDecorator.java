package com.facishare.crm.fmcg.wq.service.decorator;

import com.facishare.crm.fmcg.wq.model.MetricCalculateResult;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.model.kpi.SalaryKPI;
import com.facishare.crm.fmcg.wq.service.SalaryKPICalculator;
import com.facishare.paas.metadata.api.IObjectData;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 薪资KPI计算器装饰器基类
 * @author: dev
 * @create: 2024-07-10
 */
public abstract class SalaryKPICalculatorDecorator<T extends SalaryKPI> extends SalaryKPICalculator<T> {

    protected final SalaryKPICalculator<T> decorated;

    public SalaryKPICalculatorDecorator(SalaryKPICalculator<T> decorated) {
        this.decorated = decorated;
    }

    /**
     * 获取被装饰的计算器
     *
     * @return 被装饰的计算器
     */
    public SalaryKPICalculator<T> getDecorated() {
        return decorated;
    }

    @Override
    public void validate(T metric) {
        decorated.validate(metric);
    }


    @Override
    public MetricCalculateResult doCalculate(SalaryContext context, T metric) {
        return decorated.doCalculate(context, metric);
    }
}
