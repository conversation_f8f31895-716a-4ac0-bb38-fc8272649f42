package com.facishare.crm.fmcg.wq.command;

import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.crm.fmcg.wq.service.impl.SalaryServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 计算薪资命令
 * @author: dev
 * @create: 2024-07-10
 */
@Slf4j
public class CalculateSalaryCommand implements SalaryCommand<List<IObjectData>> {

    private final IObjectData employeeFixedSalaryObj;
    private final long startTime;
    private final long endTime;
    private final SalaryServiceImpl salaryService;

    /**
     * 构造函数
     *
     * @param employeeFixedSalaryObj 员工固定薪资对象
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param salaryService 薪资服务
     */
    public CalculateSalaryCommand(IObjectData employeeFixedSalaryObj, long startTime, long endTime, SalaryService salaryService) {
        this.employeeFixedSalaryObj = employeeFixedSalaryObj;
        this.startTime = startTime;
        this.endTime = endTime;
        this.salaryService = (SalaryServiceImpl) salaryService;
    }

    @Override
    public List<IObjectData> execute() {
        log.info("执行薪资计算命令, 员工ID: {}, 开始时间: {}, 结束时间: {}",
                employeeFixedSalaryObj.getId(), startTime, endTime);
        return salaryService.doCalculateSalaryDetailDatas(employeeFixedSalaryObj, startTime, endTime);
    }
}
