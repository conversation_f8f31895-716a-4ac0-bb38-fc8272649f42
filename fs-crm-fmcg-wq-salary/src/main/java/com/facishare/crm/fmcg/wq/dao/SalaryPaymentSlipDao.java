package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.SalaryPaymentSlipFields;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 工资发放单数据访问类
 * @author: dev
 * @create: 2025-05-07 16:51
 **/
@Slf4j
@Component
public class SalaryPaymentSlipDao extends AbstractDao {
    final static List<String> salaryPaymentSlipFields = ConfigUtils.getFields(SalaryPaymentSlipFields.class);

    /**
     * 根据ID获取工资发放单
     *
     * @param tenantId 租户ID
     * @param salaryPaymentSlipId 工资发放单ID
     * @return 工资发放单对象
     */
    public IObjectData getById(String tenantId, String salaryPaymentSlipId) {
        return getById(tenantId, SalaryPaymentSlipFields.API_NAME, salaryPaymentSlipId);
    }

    /**
     * 根据ID列表批量获取工资发放单
     *
     * @param tenantId 租户ID
     * @param ids 工资发放单ID列表
     * @return 工资发放单对象列表
     */
    public List<IObjectData> getByIds(String tenantId, List<String> ids) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(BaseField.id.getApiName(), ids)
                .build(), SalaryPaymentSlipFields.API_NAME, salaryPaymentSlipFields);
    }

    /**
     * 根据发放周期获取工资发放单
     *
     * @param tenantId 租户ID
     * @param payCycle 发放周期
     * @return 工资发放单对象列表
     */
    public List<IObjectData> getByPayCycle(String tenantId, String payCycle) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(SalaryPaymentSlipFields.PAY_CYCLE, payCycle)
                .build(), SalaryPaymentSlipFields.API_NAME, salaryPaymentSlipFields);
    }

    /**
     * 根据发放状态获取工资发放单
     *
     * @param tenantId 租户ID
     * @param payStatus 发放状态
     * @return 工资发放单对象列表
     */
    public List<IObjectData> getByPayStatus(String tenantId, String payStatus) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(SalaryPaymentSlipFields.PAY_STATUS, payStatus)
                .build(), SalaryPaymentSlipFields.API_NAME, salaryPaymentSlipFields);
    }

    /**
     * 根据时间范围获取工资发放单
     *
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 工资发放单对象列表
     */
    public List<IObjectData> getByTimeRange(String tenantId, long startTime, long endTime) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(SalaryPaymentSlipFields.START_DATE, startTime)
                .eq(SalaryPaymentSlipFields.END_DATE, endTime)
                .build(), SalaryPaymentSlipFields.API_NAME, salaryPaymentSlipFields);
    }

    /**
     * 创建工资发放单对象
     *
     * @param tenantId 租户ID
     * @return 新创建的工资发放单对象
     */
    public IObjectData createSalaryPaymentSlip(String tenantId) {
        IObjectData salaryPaymentSlip = createBaseObjectData(tenantId, null, SalaryPaymentSlipFields.API_NAME);
        salaryPaymentSlip.setId(new ObjectId().toHexString());
        return salaryPaymentSlip;
    }

    /**
     * 保存工资发放单对象
     *
     * @param user 用户
     * @param salaryPaymentSlip 工资发放单对象
     * @return 保存后的工资发放单对象
     */
    public IObjectData saveSalaryPaymentSlip(User user, IObjectData salaryPaymentSlip) {
        return save(user, salaryPaymentSlip);
    }

    /**
     * 更新工资发放单对象
     *
     * @param user 用户
     * @param salaryPaymentSlip 工资发放单对象
     * @return 更新后的工资发放单对象
     */
    public IObjectData updateSalaryPaymentSlip(User user, IObjectData salaryPaymentSlip) {
        return update(user, salaryPaymentSlip);
    }
}
