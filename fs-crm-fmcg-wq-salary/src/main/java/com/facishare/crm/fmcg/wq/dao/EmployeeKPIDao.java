package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.EmployeeKPIFields;
import com.facishare.crm.fmcg.wq.constants.UserScheduleDetailFields;
import com.facishare.crm.fmcg.wq.constants.UserScheduleFields;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-05-07 16:51
 **/
@Slf4j
@Component
public class EmployeeKPIDao extends AbstractDao{
    final static List<String> employeeKPIFields = ConfigUtils.getFields(EmployeeKPIFields.class);

    public List<IObjectData> getbyKpiIds(String tenantId, List<String> kpiIds) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(BaseField.id.getApiName(), kpiIds)
                .build(), EmployeeKPIFields.API_NAME, employeeKPIFields);
    }
}
