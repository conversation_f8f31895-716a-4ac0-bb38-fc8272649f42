package com.facishare.crm.fmcg.wq.model;

import com.facishare.crm.fmcg.wq.constants.EmployeeKPIFields;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum KPICalculateType {

    OBJECT("object"),
    KAOQIN_STAT("kaoqin_stat"),
    APL("apl");

    private final String value;


    public String value() {
        return this.value;
    }
    public static KPICalculateType of(String value) {
        switch (value) {
            case EmployeeKPIFields.INDICATOR_CALC_METHOD_Options_1: {
                return KPICalculateType.OBJECT;

            }
            case EmployeeKPIFields.INDICATOR_CALC_METHOD_Options_2: {
                return KPICalculateType.KAOQIN_STAT;
            }
            case EmployeeKPIFields.INDICATOR_CALC_METHOD_Options_3:{
                return KPICalculateType.APL;
            }
            default:
                throw new ValidateException("not support calc type");
        }
    }
}