package com.facishare.crm.fmcg.wq.command;

import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.crm.fmcg.wq.service.impl.SalaryServiceImpl;
import lombok.extern.slf4j.Slf4j;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 发放薪资命令
 * @author: dev
 * @create: 2024-07-10
 */
@Slf4j
public class PaySalaryCommand implements SalaryCommand<Boolean> {

    private final String detailId;
    private final SalaryServiceImpl salaryService;

    /**
     * 构造函数
     *
     * @param detailId 薪资明细ID
     * @param salaryService 薪资服务
     */
    public PaySalaryCommand(String detailId, SalaryService salaryService) {
        this.detailId = detailId;
        this.salaryService = (SalaryServiceImpl) salaryService;
    }

    @Override
    public Boolean execute() {
        log.info("执行薪资发放命令, 明细ID: {}", detailId);
        return salaryService.doPaySalary(detailId);
    }
}
