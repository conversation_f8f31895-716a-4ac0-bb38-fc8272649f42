package com.facishare.crm.fmcg.wq.dao;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;

import java.util.List;
import java.util.Map;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 薪资数据访问接口
 * @author: dev
 * @create: 2024-06-23
 */
public interface SalaryDao {

    /**
     * 保存薪资规则
     *
     * @param ruleData 规则数据
     * @return 保存后的规则数据
     */
    ObjectDataDocument saveSalaryRule(ObjectDataDocument ruleData);

    /**
     * 根据ID获取薪资规则
     *
     * @param ruleId 规则ID
     * @return 薪资规则
     */
    ObjectDataDocument getSalaryRuleById(String ruleId);

    /**
     * 根据条件查询薪资规则
     *
     * @param conditions 查询条件
     * @return 薪资规则列表
     */
    List<ObjectDataDocument> querySalaryRules(Map<String, Object> conditions);

    /**
     * 保存薪资明细
     *
     * @param detailData 明细数据
     * @return 保存后的明细数据
     */
    ObjectDataDocument saveSalaryDetail(ObjectDataDocument detailData);

    /**
     * 根据ID获取薪资明细
     *
     * @param detailId 明细ID
     * @return 薪资明细
     */
    ObjectDataDocument getSalaryDetailById(String detailId);

    /**
     * 根据员工ID和周期获取薪资明细
     *
     * @param employeeId 员工ID
     * @param cycle      薪资周期
     * @return 薪资明细
     */
    ObjectDataDocument getSalaryDetailByEmployeeAndCycle(String employeeId, String cycle);

    /**
     * 根据条件查询薪资明细
     *
     * @param conditions 查询条件
     * @return 薪资明细列表
     */
    List<ObjectDataDocument> querySalaryDetails(Map<String, Object> conditions);

    /**
     * 更新薪资明细状态
     *
     * @param detailId 明细ID
     * @param status   状态
     * @return 是否成功
     */
    boolean updateSalaryDetailStatus(String detailId, String status);

    /**
     * 批量更新薪资明细状态
     *
     * @param detailIds 明细ID列表
     * @param status    状态
     * @return 是否成功
     */
    boolean batchUpdateSalaryDetailStatus(List<String> detailIds, String status);

    /**
     * 保存薪资汇总数据
     *
     * @param summaryData 汇总数据
     * @return 保存后的汇总数据
     */
    ObjectDataDocument saveSalarySummary(ObjectDataDocument summaryData);

    /**
     * 根据部门和周期获取薪资汇总数据
     *
     * @param departmentId 部门ID
     * @param cycle        薪资周期
     * @return 薪资汇总数据
     */
    ObjectDataDocument getSalarySummaryByDeptAndCycle(String departmentId, String cycle);
} 