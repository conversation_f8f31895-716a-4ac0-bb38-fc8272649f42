package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.EmployeeKPIFields;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-05-07 16:51
 **/
@Slf4j
@Component
public class SalaryRuleDao extends AbstractDao{
    final static List<String> salaryRuleFields = ConfigUtils.getFields(SalaryRuleFields.class);

    public List<IObjectData> getbyIds(String tenantId, List<String> salaryRuleIds) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(BaseField.id.getApiName(), salaryRuleIds)
                .build(), EmployeeKPIFields.API_NAME, salaryRuleFields);
    }
    public IObjectData getById(String tenantId, String salaryRuleId) {
        return getById(tenantId, EmployeeKPIFields.API_NAME, salaryRuleId);
    }

}
