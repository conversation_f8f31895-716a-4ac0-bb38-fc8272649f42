package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-05-07 16:51
 **/
@Slf4j
@Component
public class SalaryItemDao extends AbstractDao{
    final static List<String> salaryItemFields = ConfigUtils.getFields(SalaryItemFields.class);

    public List<IObjectData> getbyIds(String tenantId, List<String> salaryItemIds) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(BaseField.id.getApiName(), salaryItemIds)
                .build(), SalaryItemFields.API_NAME, salaryItemFields);
    }
    public IObjectData getById(String tenantId, String salaryItemId) {
        return getById(tenantId, SalaryRuleFields.API_NAME, salaryItemId);
    }
}
