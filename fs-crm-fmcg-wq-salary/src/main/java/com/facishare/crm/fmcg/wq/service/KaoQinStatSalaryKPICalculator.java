package com.facishare.crm.fmcg.wq.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.constants.SystemConstants;
import com.facishare.crm.fmcg.wq.model.AggregateFunction;
import com.facishare.crm.fmcg.wq.model.KaoQinFieldType;
import com.facishare.crm.fmcg.wq.model.MetricCalculateResult;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.model.exception.AbandonActionException;
import com.facishare.crm.fmcg.wq.model.kpi.AggregateSalaryKPI;
import com.facishare.crm.fmcg.wq.model.kpi.KaoQinStatSalaryKPI;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 聚合指标计算器
 */
@SuppressWarnings("Duplicates")
@Component("kaoQinStatSalaryKPICalculator")
public class KaoQinStatSalaryKPICalculator extends SalaryKPICalculator<KaoQinStatSalaryKPI> {

    @Resource
    private ServiceFacade serviceFacade;



    /**
     * 进行聚合查询，计算聚合值
     *
     * @param context 激励上下文
     * @param metric  指标
     * @return 聚合查询结果
     */
    @Override
    public MetricCalculateResult doCalculate(SalaryContext context, KaoQinStatSalaryKPI metric) {
        MetricCalculateResult metricCalculateResult = new MetricCalculateResult();
        if (context.getExtDataMap() != null && context.getExtDataMap().containsKey(metric.getKaoQinFieldType().toString())){
            metricCalculateResult.setValue(context.getExtDataMap().getOrDefault(metric.getKaoQinFieldType().toString(),"0").toString());
        }else{
            setKaoQinStatMap(context);
            metricCalculateResult.setValue(context.getExtDataMap().getOrDefault(metric.getKaoQinFieldType().toString(),"0").toString());
        }
        return metricCalculateResult;
    }

    private void setKaoQinStatMap(SalaryContext context) {
        if (context.getExtDataMap() == null){
            context.setExtDataMap(Maps.newHashMap());
        }
        //todo 考勤接口
        for (KaoQinFieldType value : KaoQinFieldType.values()) {
            context.getExtDataMap().put(value.toString(),"1");
        }
        context.watch().lap("KaoQinStatSalaryKPICalculator#setKaoQinStatMap." + context.getOwner());
    }

}
