
package com.facishare.crm.fmcg.wq.dao;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.SalaryDetailDataFields;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-05-07 16:51
 **/
@Slf4j
@Component
public class SalaryDetailDataDao extends AbstractDao{
    final static List<String> salaryDetailDataFields = ConfigUtils.getFields(SalaryDetailDataFields.class);



    public List<IObjectData> getByIds(String tenantId, List<String> kpiIds) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(BaseField.id.getApiName(), kpiIds)
                .build(), SalaryDetailDataFields.API_NAME, salaryDetailDataFields);
    }

    /**
     * 根据工资条ID获取薪资明细
     *
     * @param tenantId 租户ID
     * @param salaryDataId 工资条ID
     * @return 薪资明细列表
     */
    public List<IObjectData> getBySalaryDataId(String tenantId, String salaryDataId) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(SalaryDetailDataFields.SALARY_DATA, salaryDataId)
                .build(), SalaryDetailDataFields.API_NAME, salaryDetailDataFields);
    }

    /**
     * 保存薪资明细对象
     *
     * @param user 用户
     * @param salaryDetailData 薪资明细对象
     * @return 保存后的薪资明细对象
     */
    public IObjectData saveSalaryDetailData(User user, IObjectData salaryDetailData) {
        return save(user, salaryDetailData);
    }

    /**
     * 更新薪资明细对象
     *
     * @param user 用户
     * @param salaryDetailData 薪资明细对象
     * @return 更新后的薪资明细对象
     */
    public IObjectData updateSalaryDetailData(User user, IObjectData salaryDetailData) {
        return update(user, salaryDetailData);
    }

    /**
     * create salary detail data
     */
    public IObjectData createSalaryDetailDataByEmployeeFixedSalaryObjExt(ObjectDataExt employeeFixedSalaryObjExt,long startTime,long endTime) {
        IObjectData salaryDetailDataObj = employeeFixedSalaryObjExt.copy();
        salaryDetailDataObj.setDescribeApiName(SalaryDetailDataFields.API_NAME);
        salaryDetailDataObj.setId(new ObjectId().toHexString());
        //取SalaryDetailDataFields 上字段 赋值为 employeeFixedSalaryObjExt 上 EmployeeFixedSalaryFields 字段值
        salaryDetailDataObj.set(SalaryDetailDataFields.EMPLOYEE_FIXED_SALARY, employeeFixedSalaryObjExt.getId());
        salaryDetailDataObj.set(SalaryDetailDataFields.START_DATE,startTime);
        salaryDetailDataObj.set(SalaryDetailDataFields.END_DATE,endTime);
        return salaryDetailDataObj;
    }
}
