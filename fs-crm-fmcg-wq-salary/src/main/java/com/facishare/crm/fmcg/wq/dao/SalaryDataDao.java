package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.SalaryDataFields;
import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 工资条数据访问类
 * @author: zhangsm
 * @create: 2025-05-07 16:51
 **/
@Slf4j
@Component
public class SalaryDataDao extends AbstractDao{
    final static List<String> salaryDataFields = ConfigUtils.getFields(SalaryDataFields.class);

    /**
     * 根据ID列表批量获取工资条
     *
     * @param tenantId 租户ID
     * @param ids 工资条ID列表
     * @return 工资条对象列表
     */
    public List<IObjectData> getbyIds(String tenantId, List<String> ids) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(BaseField.id.getApiName(), ids)
                .build(), SalaryDataFields.API_NAME, salaryDataFields);
    }

    /**
     * 根据ID获取工资条
     *
     * @param tenantId 租户ID
     * @param salaryDataId 工资条ID
     * @return 工资条对象
     */
    public IObjectData getById(String tenantId, String salaryDataId) {
        return getById(tenantId, SalaryDataFields.API_NAME, salaryDataId);
    }

    /**
     * 根据员工ID获取工资条
     *
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @return 工资条对象列表
     */
    public List<IObjectData> getByEmployeeId(String tenantId, String employeeId) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(SalaryDataFields.EMPLOYEE, employeeId)
                .build(), SalaryDataFields.API_NAME, salaryDataFields);
    }

    /**
     * 根据外部员工ID获取工资条
     *
     * @param tenantId 租户ID
     * @param employeeExternalId 外部员工ID
     * @return 工资条对象列表
     */
    public List<IObjectData> getByEmployeeExternalId(String tenantId, String employeeExternalId) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(SalaryDataFields.EMPLOYEE_EXTERNAL, employeeExternalId)
                .build(), SalaryDataFields.API_NAME, salaryDataFields);
    }

    /**
     * 根据员工ID和时间范围查询工资条
     *
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 工资条对象列表
     */
    public List<IObjectData> getByEmployeeIdAndTimeRange(String tenantId, String employeeId, long startTime, long endTime) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(SalaryDataFields.EMPLOYEE, employeeId)
                .eq(SalaryDataFields.START_DATE, startTime)
                .eq(SalaryDataFields.END_DATE, endTime)
                .build(), SalaryDataFields.API_NAME, salaryDataFields);
    }

    /**
     * 根据外部员工ID和时间范围查询工资条
     *
     * @param tenantId 租户ID
     * @param employeeExternalId 外部员工ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 工资条对象列表
     */
    public List<IObjectData> getByEmployeeExternalIdAndTimeRange(String tenantId, String employeeExternalId, long startTime, long endTime) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(SalaryDataFields.EMPLOYEE_EXTERNAL, employeeExternalId)
                .eq(SalaryDataFields.START_DATE, startTime)
                .eq(SalaryDataFields.END_DATE, endTime)
                .build(), SalaryDataFields.API_NAME, salaryDataFields);
    }

    /**
     * 创建工资条对象
     *
     * @param tenantId 租户ID
     * @return 新创建的工资条对象
     */
    public IObjectData createSalaryData(String tenantId) {
        IObjectData salaryData = createBaseObjectData(tenantId, null, SalaryDataFields.API_NAME);
        salaryData.setId(new ObjectId().toHexString());
        return salaryData;
    }

    /**
     * 保存工资条对象
     *
     * @param user 用户
     * @param salaryData 工资条对象
     * @return 保存后的工资条对象
     */
    public IObjectData saveSalaryData(User user, IObjectData salaryData) {
        return save(user, salaryData);
    }

    /**
     * 更新工资条对象
     *
     * @param user 用户
     * @param salaryData 工资条对象
     * @return 更新后的工资条对象
     */
    public IObjectData updateSalaryData(User user, IObjectData salaryData) {
        return update(user, salaryData);
    }

}
