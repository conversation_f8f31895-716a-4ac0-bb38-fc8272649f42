package com.facishare.crm.fmcg.wq;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 薪资模块默认对象定义
 * @author: dev
 * @create: 2024-06-23
 */
@Slf4j
public enum SalaryDefaultObject implements PreDefineObject {
    //员工绩效指标
    EmployeeKPI("EmployeeKPIObj"),
    //工资条
    SalaryData("SalaryDataObj"),
    //工资发放单
    SalaryPaymentSlip("SalaryPaymentSlipObj"),
    //工资条明细
    SalaryDetailData("SalaryDetailDataObj"),
    //员工固定工资明细
    EmployeeFixedSalaryDetail("EmployeeFixedSalaryDetailObj"),
    //员工固定工资表
    EmployeeFixedSalary("EmployeeFixedSalaryObj"),
    //工资规则
    SalaryRule("SalaryRuleObj"),
    //工资项
    SalaryItem("SalaryItemObj");

    private final String apiName;

    private static String PACKAGE_NAME = SalaryDefaultObject.class.getPackage().getName();

    SalaryDefaultObject(String apiName) {
        this.apiName = apiName;
    }

    public static SalaryDefaultObject getEnum(String apiName) {
        List<SalaryDefaultObject> list = Arrays.asList(SalaryDefaultObject.values());
        return list.stream().filter(m -> m.getApiName().equalsIgnoreCase(apiName)).findAny().orElse(null);
    }

    public static void init() {
        for (SalaryDefaultObject object : SalaryDefaultObject.values()) {
            log.info("init {}", object.toString());
            PreDefineObjectRegistry.register(object);
        }
    }

    @Override
    public String getApiName() {
        return this.apiName;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this + actionCode + "Action";
        return new ActionClassInfo(className);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this + methodName + "Controller";
        return new ControllerClassInfo(className);
    }

    public static void main(String[] args) {
        String enumValuesJoinedBySemicolon = Arrays.stream(SalaryDefaultObject.values()).map(SalaryDefaultObject::getApiName).distinct().collect(Collectors.joining(","));
        String enumValuesJoinedByPipe = Arrays.stream(SalaryDefaultObject.values()).map(SalaryDefaultObject::getApiName).distinct().collect(Collectors.joining("|"));
        System.out.println(enumValuesJoinedBySemicolon);
        System.out.println(enumValuesJoinedByPipe);
        //打印sortname 取组成单词前3个字母 ps: "IdealCustomerProfileObj":"icp",
        System.out.println(Arrays.stream(SalaryDefaultObject.values()).map(m -> "\"" + m.getApiName() + "\":\"" + m.getApiName().toLowerCase().substring(0, 3) + "\"").collect(Collectors.joining(",")));

        List<Map<String ,String>> list = Arrays.stream(SalaryDefaultObject.values()).map(m -> {
            Map<String, String> map = new HashMap<>();
            map.put("apiName", m.getApiName());
            map.put("hidden", "false");
            return map;
        }).collect(Collectors.toList());
        String jsonStr = JSON.toJSONString(list);
        System.out.println(jsonStr);
    }
} 