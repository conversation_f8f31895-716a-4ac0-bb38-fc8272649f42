package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.SalaryDataFields;
import com.facishare.crm.fmcg.wq.dao.BaseDao;
import com.facishare.crm.fmcg.wq.notify.SalaryMessageService;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 工资条消息测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/test/salary-message")
public class SalaryMessageTestController {

    @Autowired
    private SalaryMessageService salaryMessageService;
    @Autowired
    private BaseDao baseDao;

    /**
     * 测试发送外部员工工资条消息
     *
     * @param request 请求参数
     * @return 发送结果
     */
    @PostMapping("/send-external")
    public Map<String, Object> sendExternalEmployeeMessage(@RequestBody SendExternalMessageRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始测试发送外部员工工资条消息，tenantId: {}, employeeExternalId: {}", 
                request.getTenantId(), request.getEmployeeExternalId());
            
            // 创建模拟的工资条数据
            IObjectData salaryData = createMockSalaryData(request);
            
            // 调用发送消息方法
            boolean result = salaryMessageService.sendExternalEmployeeMessage(
                request.getTenantId(), 
                request.getEmployeeExternalId(), 
                salaryData
            );
            
            if (result) {
                response.put("success", true);
                response.put("message", "外部员工工资条消息发送成功");
                response.put("data", createResponseData(request, salaryData));
                log.info("外部员工工资条消息发送成功，tenantId: {}, employeeExternalId: {}", 
                    request.getTenantId(), request.getEmployeeExternalId());
            } else {
                response.put("success", false);
                response.put("message", "外部员工工资条消息发送失败");
                response.put("error", "消息发送服务返回失败");
            }
            
        } catch (Exception e) {
            log.error("测试发送外部员工工资条消息异常，tenantId: {}, employeeExternalId: {}", 
                request.getTenantId(), request.getEmployeeExternalId(), e);
            response.put("success", false);
            response.put("message", "发送消息时发生异常");
            response.put("error", e.getMessage());
        }
        
        return response;
    }

    /**
     * 测试发送内部员工工资条消息
     *
     * @param request 请求参数
     * @return 发送结果
     */
    @PostMapping("/send-internal")
    public Map<String, Object> sendInternalEmployeeMessage(@RequestBody SendInternalMessageRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始测试发送内部员工工资条消息，tenantId: {}, employeeId: {}", 
                request.getTenantId(), request.getEmployeeId());
            
            // 创建模拟的工资条数据
            IObjectData salaryData = createMockSalaryData(request);
            
            // 调用发送消息方法
            boolean result = salaryMessageService.sendInternalEmployeeMessage(
                request.getTenantId(), 
                request.getEmployeeId(), 
                salaryData
            );
            
            if (result) {
                response.put("success", true);
                response.put("message", "内部员工工资条消息发送成功");
                response.put("data", createResponseData(request, salaryData));
                log.info("内部员工工资条消息发送成功，tenantId: {}, employeeId: {}", 
                    request.getTenantId(), request.getEmployeeId());
            } else {
                response.put("success", false);
                response.put("message", "内部员工工资条消息发送失败");
                response.put("error", "消息发送服务返回失败");
            }
            
        } catch (Exception e) {
            log.error("测试发送内部员工工资条消息异常，tenantId: {}, employeeId: {}", 
                request.getTenantId(), request.getEmployeeId(), e);
            response.put("success", false);
            response.put("message", "发送消息时发生异常");
            response.put("error", e.getMessage());
        }
        
        return response;
    }

    /**
     * 创建模拟的工资条数据
     */
    private IObjectData createMockSalaryData(Object request) {
        IObjectData salaryData = baseDao.createBaseObjectData("1", null, SalaryDataFields.API_NAME);
        
        // 设置工资条ID
        salaryData.setId("test_salary_" + System.currentTimeMillis());
        
        // 设置工资期间（当前月份）
        LocalDate now = LocalDate.now();
        LocalDate startOfMonth = now.withDayOfMonth(1);
        LocalDate endOfMonth = now.withDayOfMonth(now.lengthOfMonth());
        
        Date startDate = Date.from(startOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(endOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        salaryData.set(SalaryDataFields.START_DATE, startDate.getTime());
        salaryData.set(SalaryDataFields.END_DATE, endDate.getTime());
        
        // 设置应发工资
        String payableSalary = "8888.88";
        if (request instanceof SendExternalMessageRequest) {
            SendExternalMessageRequest extRequest = (SendExternalMessageRequest) request;
            if (extRequest.getPayableSalary() != null) {
                payableSalary = extRequest.getPayableSalary();
            }
        } else if (request instanceof SendInternalMessageRequest) {
            SendInternalMessageRequest intRequest = (SendInternalMessageRequest) request;
            if (intRequest.getPayableSalary() != null) {
                payableSalary = intRequest.getPayableSalary();
            }
        }
        salaryData.set(SalaryDataFields.PAYABLE_SALARY, payableSalary);
        
        // 设置员工信息
        if (request instanceof SendExternalMessageRequest) {
            SendExternalMessageRequest extRequest = (SendExternalMessageRequest) request;
            salaryData.set(SalaryDataFields.EMPLOYEE_EXTERNAL, extRequest.getEmployeeExternalId());
        } else if (request instanceof SendInternalMessageRequest) {
            SendInternalMessageRequest intRequest = (SendInternalMessageRequest) request;
            salaryData.set(SalaryDataFields.EMPLOYEE, intRequest.getEmployeeId());
        }
        
        return salaryData;
    }

    /**
     * 创建响应数据
     */
    private Map<String, Object> createResponseData(Object request, IObjectData salaryData) {
        Map<String, Object> data = new HashMap<>();
        data.put("salaryId", salaryData.getId());
        data.put("startDate", new Date(salaryData.get(SalaryDataFields.START_DATE, Long.class)));
        data.put("endDate", new Date(salaryData.get(SalaryDataFields.END_DATE, Long.class)));
        data.put("payableSalary", salaryData.get(SalaryDataFields.PAYABLE_SALARY, String.class));
        
        if (request instanceof SendExternalMessageRequest) {
            SendExternalMessageRequest extRequest = (SendExternalMessageRequest) request;
            data.put("tenantId", extRequest.getTenantId());
            data.put("employeeExternalId", extRequest.getEmployeeExternalId());
            data.put("messageType", "external");
        } else if (request instanceof SendInternalMessageRequest) {
            SendInternalMessageRequest intRequest = (SendInternalMessageRequest) request;
            data.put("tenantId", intRequest.getTenantId());
            data.put("employeeId", intRequest.getEmployeeId());
            data.put("messageType", "internal");
        }
        
        return data;
    }

    /**
     * 外部员工消息发送请求
     */
    public static class SendExternalMessageRequest {
        private String tenantId;
        private String employeeExternalId;
        private String payableSalary;

        public String getTenantId() {
            return tenantId;
        }

        public void setTenantId(String tenantId) {
            this.tenantId = tenantId;
        }

        public String getEmployeeExternalId() {
            return employeeExternalId;
        }

        public void setEmployeeExternalId(String employeeExternalId) {
            this.employeeExternalId = employeeExternalId;
        }

        public String getPayableSalary() {
            return payableSalary;
        }

        public void setPayableSalary(String payableSalary) {
            this.payableSalary = payableSalary;
        }
    }

    /**
     * 内部员工消息发送请求
     */
    public static class SendInternalMessageRequest {
        private String tenantId;
        private String employeeId;
        private String payableSalary;

        public String getTenantId() {
            return tenantId;
        }

        public void setTenantId(String tenantId) {
            this.tenantId = tenantId;
        }

        public String getEmployeeId() {
            return employeeId;
        }

        public void setEmployeeId(String employeeId) {
            this.employeeId = employeeId;
        }

        public String getPayableSalary() {
            return payableSalary;
        }

        public void setPayableSalary(String payableSalary) {
            this.payableSalary = payableSalary;
        }
    }
}
