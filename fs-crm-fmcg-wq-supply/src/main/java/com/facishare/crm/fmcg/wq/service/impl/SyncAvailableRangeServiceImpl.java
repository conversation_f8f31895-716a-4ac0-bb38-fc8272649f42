package com.facishare.crm.fmcg.wq.service.impl;

import com.facishare.crm.fmcg.wq.FmcgGray;
import com.facishare.crm.fmcg.wq.SyncDelayQueueTask;
import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.dao.BaseDao;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.Shop;
import com.facishare.crm.fmcg.wq.model.Supplier;
import com.facishare.crm.fmcg.wq.model.obj.ShopProductSupplierRelationshipObj;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.service.SyncAvailableRangeService;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg
 * @description: 同步可售范围数据
 * @author: zhangsm
 * @create: 2021-05-19 17:54
 **/
@Service
@Slf4j
public class SyncAvailableRangeServiceImpl implements SyncAvailableRangeService {
    @Autowired
    SupplyDao supplyDao;

    @Autowired
    BaseDao baseDao;
    @Autowired
    SupplyService supplyService;
    @Autowired
    public RedisUtils redisUtils;
    @Resource
    SyncDelayQueueTask<DelayedSyncAvailableRangeTask> syncAvailableRangeDelayQueueTask;
    // 同步任务 触发时间间隔  10 分钟
    final int availableObjSyncTaskTriggerTime = 60;

    @Override
    public void addAvailableObjSyncTask(String tenantId, String dealerSupplyId) {
        if (redisUtils.syncAvaRangeDoing(tenantId, dealerSupplyId)) {
            // 更新最新的延迟时间
            if (syncAvailableRangeDelayQueueTask.removeTaskIf(o -> {
                DelayedSyncAvailableRangeTask delayedSyncAvailableRangeTask = (DelayedSyncAvailableRangeTask) o;
                return delayedSyncAvailableRangeTask.ea.equals(tenantId) && dealerSupplyId.equals(delayedSyncAvailableRangeTask.dealerSupplyId);
            })) {
                DelayedSyncAvailableRangeTask delayedSyncAvailableRangeTask = new DelayedSyncAvailableRangeTask(availableObjSyncTaskTriggerTime, tenantId, dealerSupplyId);
                syncAvailableRangeDelayQueueTask.addTask(delayedSyncAvailableRangeTask);
            }
        } else {
            DelayedSyncAvailableRangeTask delayedSyncAvailableRangeTask = new DelayedSyncAvailableRangeTask(availableObjSyncTaskTriggerTime, tenantId, dealerSupplyId);
            syncAvailableRangeDelayQueueTask.addTask(delayedSyncAvailableRangeTask);
        }
        // 增加缓存
        redisUtils.syncAvaRangeAdd(tenantId, dealerSupplyId);
    }

    private class DelayedSyncAvailableRangeTask extends SyncDelayQueueTask.DelayedTask {

        String ea;
        String dealerSupplyId;

        public DelayedSyncAvailableRangeTask(long triggerTime, String ea, String dealerSupplyId) {
            super(triggerTime);
            this.dealerSupplyId = dealerSupplyId;
            this.ea = ea;
        }

        @Override
        public void run() {
            super.run();
            try {
                log.info("DelayedSyncAvailableRangeTask start : {} {}",ea,dealerSupplyId);
                syncAvailableObjByDealerSupplyObj(ea,
                        supplyDao.getById(ea, DealerSupplyObjConstants.API_NAME, dealerSupplyId));
            } catch (Exception e) {
                log.info("DelayedSyncAvailableRangeTask error :",e);
            } finally {
                redisUtils.syncAvaRangeRemove(ea, dealerSupplyId);
            }
        }
    }

    @Override
    public void syncAvailableObjByDealerSupplyObj(String tenantId, IObjectData dealerSupplyObj) {
        //同步主的 可售范围
        IObjectData masterObject = syncAvailableRangeObj(tenantId, dealerSupplyObj);
        //同步可售门店
        syncAvailableAccountObj(tenantId, dealerSupplyObj, masterObject);
        //同步可售产品
        syncAvailableProductObj(tenantId, dealerSupplyObj);
        //同步 部分特例数据
        List<IObjectData> avaRangeList = syncSpecialAvailableObj(tenantId, dealerSupplyObj);
        //同步价目表
        syncAvailablePriceBookObj(tenantId, dealerSupplyObj, Lists.newArrayList(masterObject));
    }

    @Override
    public void delAvailableObjByDealerSupplyObj(String tenantId, String dealerSupplyObjId, String subAccountId) {
        List<IObjectData> dealerSupplyBySuppliers = supplyDao.getDealerSupplyBySuppliers(tenantId, Lists.newArrayList(dealerSupplyObjId));
        boolean dealerSupplyDel;
        if (CollectionUtils.isNotEmpty(dealerSupplyBySuppliers)) {
            dealerSupplyDel = dealerSupplyBySuppliers.get(0).isDeleted();
        } else {
            dealerSupplyDel = Boolean.TRUE;
        }
        SearchQuery.SearchQueryBuilder searchQueryBuilder = SearchQuery.builder()
                .eq(AvailableRangeObjConstants.Field.dealerSupplyId.getApiName(), dealerSupplyBySuppliers.get(0).getId())
                .notExist(AvailableRangeObjConstants.Field.storeId.getApiName());
        if (StringUtils.isNotBlank(subAccountId)) {
            SearchQuery.SearchQueryBuilder searchQueryBuilder2 = SearchQuery.builder()
                    .eq(AvailableRangeObjConstants.Field.dealerSupplyId.getApiName(), dealerSupplyBySuppliers.get(0).getId())
                    .eq(AvailableRangeObjConstants.Field.storeId.getApiName(), subAccountId);
            searchQueryBuilder.addOrWheres(searchQueryBuilder2);
        }
        SearchQuery searchQuery = searchQueryBuilder.build();
        List<IObjectData> data = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, AvailableRangeObjConstants.API_NAME);
        if (dealerSupplyDel) {
            if (CollectionUtils.isNotEmpty(data)) {
                supplyDao.batchInvalidAndDel(User.systemUser(tenantId), data);
            }
        } else {
            if (StringUtils.isNotBlank(subAccountId)){
                for (IObjectData datum : data) {
                    String specialAccountId = datum.get(AvailableRangeObjConstants.Field.storeId.getApiName(), String.class);
                    if (StringUtils.isNotBlank(specialAccountId)){
                        supplyDao.batchInvalidAndDel(User.systemUser(tenantId),Lists.newArrayList(datum));
                    }else {
                        List<IObjectData> availableAccountList = supplyDao.getAvailableAccountList(tenantId, datum.getId(), Lists.newArrayList(subAccountId));
                        supplyDao.batchInvalidAndDel(User.systemUser(tenantId),availableAccountList);
                    }
                }
            }
        }
    }

    @Override
    public List<IObjectData> syncAvailableObjByDealerSupplyObj(String tenantId, IObjectData dealerSupplyObj,String subAccountId) {
        List<IObjectData> result = Lists.newArrayList();
        Collections.synchronizedList(result);
       // 作废掉 多余的数据
        if (StringUtils.isNotBlank(subAccountId)) {
            List<String> specialAccountIds = supplyDao.getSpecialAccountIds(tenantId, dealerSupplyObj.get(DealerSupplyObjConstants.Field.dealerId.getApiName(), String.class), subAccountId);
            if (CollectionUtils.isNotEmpty(specialAccountIds)) {
                //特例的
                specialAccountIds.parallelStream().forEach(specialAccountId -> {
                    result.add( subSyncSpecialAvailableObj(tenantId, dealerSupplyObj, specialAccountId));
                });
            } else {
                //普通的
                //同步主的 可售范围
                IObjectData masterObject = syncAvailableRangeObj(tenantId, dealerSupplyObj);
                result.add(masterObject);
                //同步可售门店
                syncAvailableAccountObj(tenantId, dealerSupplyObj, masterObject,subAccountId);
                //同步可售产品
                syncAvailableProductObj(tenantId, dealerSupplyObj);
                syncAvailablePriceBookObj(tenantId, dealerSupplyObj, Lists.newArrayList(masterObject));
            }
        } else {
            //同步主的 可售范围
            IObjectData masterObject = syncAvailableRangeObj(tenantId, dealerSupplyObj);
            result.add(masterObject);
            //同步可售门店
            syncAvailableAccountObj(tenantId, dealerSupplyObj, masterObject);
            //同步可售产品
            syncAvailableProductObj(tenantId, dealerSupplyObj);
            //同步 部分特例数据
            List<IObjectData> avaRangeList = syncSpecialAvailableObj(tenantId, dealerSupplyObj);
            //同步价目表
            syncAvailablePriceBookObj(tenantId, dealerSupplyObj, Lists.newArrayList(masterObject));
        }
        return result;
    }

    @Override
    public IObjectData syncAvailableRangeObj(String tenantId, IObjectData dealerSupplyObj) {
        Object ownDepartment = dealerSupplyObj.get(DealerSupplyObjConstants.Field.ownDepartment.getApiName());
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(AvailableRangeObjConstants.Field.dealerSupplyId.getApiName(), dealerSupplyObj.getId())
                .notExist(AvailableRangeObjConstants.Field.storeId.getApiName()).build();
        List<IObjectData> data = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, AvailableRangeObjConstants.API_NAME);
        if (CollectionUtils.isEmpty(data)) {
            //创建可售范围
            return supplyDao.createAvailableRangeObj(tenantId, "-10000", dealerSupplyObj.getId(), dealerSupplyObj.get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString(),ownDepartment);
        } else {
            return data.get(0);
        }
    }

    @Override
    public void syncAvailableProductObj(String tenantId, IObjectData dealerSupplyObj) {
        // 原来版本需要手动创建所有的餐品
        //现在 所有产品 可售范围那有可能自己创建这个逻辑 这里就会报错 暂时catch 住
        try{
            //根据供应关系获取 整个 数据 ，包含特例产品， 经营范围
            String dealerId = dealerSupplyObj.get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString();
            //经销商 范围
            Supplier supplierById = supplyService.getSupplierById(tenantId, dealerId, 1);
            Object ownDepartment = dealerSupplyObj.get(DealerSupplyObjConstants.Field.ownDepartment.getApiName());
            //获取可售范围对象

            List<String> productIdList = supplierById.getProductList()
                    .stream().map(o -> o.getProductId()).distinct().collect(Collectors.toList());

            //所有产品的逻辑 肯定不是特例
            String dealerSupplyId = dealerSupplyObj.getId();
            SearchQuery searchQuery = SearchQuery.builder()
                    .eq(AvailableRangeObjConstants.Field.dealerSupplyId.getApiName(), dealerSupplyId)
                    .notExist(AvailableRangeObjConstants.Field.storeId.getApiName()).build();
            searchQuery.getSearchTemplateQuery().setSearchSource("db");
            List<IObjectData> data = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, AvailableRangeObjConstants.API_NAME);
            if (productIdList.removeIf(o -> o.equals("ALL"))) {
                IObjectData availableRangeObj = createOrUpdateAvailableRange(tenantId, dealerId, dealerSupplyId, data, null,
                        AvailableRangeObjConstants.Value.All,ownDepartment);
                // 移除之前存在的
                SearchQuery productQuery = SearchQuery.builder()
                        .eq(AvailableProductObjConstants.Field.availableRangeId.getApiName(), availableRangeObj.getId())
                        .build();
                productQuery.getSearchTemplateQuery().setSearchSource("db");
                List<IObjectData> existProductList = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), productQuery, AvailableProductObjConstants.API_NAME);
                if (existProductList.size() == 1 && existProductList.stream().anyMatch(o -> {
                    String productId = o.get(
                            AvailableProductObjConstants.Field.productId.getApiName(), String.class);
                    return "ALL".equals(productId);

                })) {
                } else {
                    supplyDao.batchInvalidAndDel(User.systemUser(tenantId), existProductList);
                    // 全部产品还会生成一个全部产品的 可售产品的子对象
                    productIdList.clear();
                    productIdList.add("ALL");
                    List<IObjectData> availableProductList = supplyDao.createAvailableProductList(tenantId, availableRangeObj.getId(), productIdList,ownDepartment);
                }


            } else {
                //指定产品的逻辑 按照 供应商聚合
                IObjectData availableRangeObj = createOrUpdateAvailableRange(tenantId, dealerId, dealerSupplyId, data, null,
                        AvailableRangeObjConstants.Value.FIXED,ownDepartment);
                // 取出之前存在的做聚合
                SearchQuery productQuery = SearchQuery.builder()
                        .eq(AvailableProductObjConstants.Field.availableRangeId.getApiName(), availableRangeObj.getId())
                        .build();
                productQuery.getSearchTemplateQuery().setSearchSource("db");
                List<IObjectData> existProductList = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), productQuery, AvailableProductObjConstants.API_NAME);
                List<String> existProductIdList = existProductList.stream().map(o -> o.get(AvailableProductObjConstants.Field.productId.getApiName(), String.class))
                        .collect(Collectors.toList());
                List<String> addIds = productIdList.stream().filter(o -> !existProductIdList.contains(o)).distinct().collect(Collectors.toList());



                //删除产品
                List<String> delProductIds = Lists.newArrayList(existProductIdList);
                delProductIds.removeAll(productIdList);
                List<IObjectData> delProductList = existProductList.stream().filter(o->delProductIds.contains(o.get(AvailableProductObjConstants.Field.productId.getApiName(), String.class)))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(delProductList)) {
                    log.info("syncAvailableProductObj supplyId {} avaId {} delProductIds {}",dealerSupplyObj.getId(),availableRangeObj.getId(),delProductIds);
                    try {
                        supplyDao.batchInvalidAndDel(User.systemUser(tenantId), delProductList);
                    }catch (Exception e){
                        log.info("syncAvailableProductObj del error {}",e);
                    }
                }

                //新增产品
                if (CollectionUtils.isNotEmpty(addIds)) {
                    try {
                        supplyDao.createAvailableProductList(tenantId, availableRangeObj.getId()
                                , addIds, ownDepartment);
                    }catch(Exception e){
                        log.info("syncAvailableProductObj add error {}",e);
                    }
                }


            }
        }catch (Throwable e){
            log.info("syncAvailableProductObj ",e);
        }


    }

    private IObjectData createOrUpdateAvailableRange(String tenantId, String dealerId, String dealerSupplyId
            , List<IObjectData> data, AvailableRangeObjConstants.Value accountRange, AvailableRangeObjConstants.Value productRange,Object ownDepartment) {
        if (CollectionUtils.isEmpty(data)) {
            return supplyDao.createAvailableRangeObj(tenantId, "-10000", dealerSupplyId, dealerId,
                    accountRange == null ? AvailableRangeObjConstants.Value.FIXED : accountRange,
                    productRange == null ? AvailableRangeObjConstants.Value.All : productRange,ownDepartment);
        } else {
            IObjectData availableRangeObj = data.get(0);
            boolean update = false;
            if (productRange != null && !productRange.getValue().equals(
                    availableRangeObj.get(AvailableRangeObjConstants.Field.productRange.getApiName(), String.class))) {
                availableRangeObj.set(AvailableRangeObjConstants.Field.productRange.getApiName()
                        , productRange.getValue());
                update = true;

            }
            if (accountRange != null && !accountRange.getValue().equals(
                    availableRangeObj.get(AvailableRangeObjConstants.Field.accountRange.getApiName(), String.class))) {
                availableRangeObj.set(AvailableRangeObjConstants.Field.accountRange.getApiName()
                        , accountRange.getValue());
                update = true;

            }
            if (update) {
                availableRangeObj.set("calculate_status","0");
                return supplyDao.update(User.systemUser(tenantId), availableRangeObj);
            }
            return availableRangeObj;
        }
    }

    @Override
    public void syncAvailableAccountObj(String tenantId, IObjectData dealerSupplyObj, IObjectData masterObject) {
       syncAvailableAccountObj(tenantId, dealerSupplyObj, masterObject,null);
//        Object ownDepartment = dealerSupplyObj.get(DealerSupplyObjConstants.Field.ownDepartment.getApiName());
//        //根据供应关系获取 包含特例产品，下级配送商，供货门店
//        //查询供货门店
//        SearchQuery storeSearchQuery = SearchQuery.builder()
//                .in(SupplyStoreObjConstants.Field.supplyId.getApiName(), Lists.newArrayList(dealerSupplyObj.getId()))
//                .eq(SupplyStoreObjConstants.Field.isSpecialSupply.getApiName(), false)
//                .build();
//        List<IObjectData> supplyStoreData = supplyDao.getAllIObjectDataListByQuery(tenantId, storeSearchQuery, SupplyStoreObjConstants.API_NAME);
//        //查询下级配送商
//        SearchQuery disSearchQuery = SearchQuery.builder()
//                .in(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(), Lists.newArrayList(dealerSupplyObj.getId()))
//                .eq(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), false)
//                .build();
//        List<IObjectData> distributorSupplyData = supplyDao.getAllIObjectDataListByQuery(tenantId, disSearchQuery, DistributorSupplyObjConstants.API_NAME);
//
//        List<String> newAccountIds = Lists.newArrayList();
//        if (CollectionUtils.isNotEmpty(supplyStoreData)) {
//            supplyStoreData.forEach(o -> newAccountIds.add(o.get(SupplyStoreObjConstants.Field.shopId.getApiName()).toString()));
//        }
//        if (CollectionUtils.isNotEmpty(distributorSupplyData)) {
//            distributorSupplyData.forEach(o -> newAccountIds.add(o.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName()).toString()));
//        }
//        //查询已存在客户
////        IObjectData objectData=syncAvailableRangeObj(tenantId,dealerSupplyObj);
//        SearchQuery accountSearchQuery = SearchQuery.builder().eq(AvailableAccountObjConstants.Field.availableRangeId.getApiName(), masterObject.getId()).build();
//        List<IObjectData> existAccountList = supplyDao.getAllIObjectDataListByQuery(tenantId, accountSearchQuery, AvailableAccountObjConstants.API_NAME);
//        List<String> existAccountIds = Lists.newArrayList();
//        if (CollectionUtils.isNotEmpty(existAccountList)) {
//            existAccountList.forEach(o -> existAccountIds.add(o.get(AvailableAccountObjConstants.Field.accountId.getApiName()).toString()));
//        }
//
//        //区分出新增和移除的
//        List<String> addList = Lists.newArrayList();
//        addList.addAll(newAccountIds);
//        addList.removeAll(existAccountIds);
//        List<String> delList = Lists.newArrayList();
//        delList.addAll(existAccountIds);
//        delList.removeAll(newAccountIds);
//        // 不知道为啥 出现的 客户id重复
//        if (CollectionUtils.isNotEmpty(addList)) {
//            supplyDao.createAvailableAccountList(tenantId, masterObject.getId(), addList.stream().distinct().collect(Collectors.toList()),ownDepartment);
//        }
//        if (CollectionUtils.isNotEmpty(delList)) {
//            supplyDao.deleteAvailableAccountList(tenantId, masterObject.getId(), delList.stream().distinct().collect(Collectors.toList()));
//        }

    }
    @Override
    public void syncAvailableAccountObj(String tenantId, IObjectData dealerSupplyObj, IObjectData masterObject ,String subAccountId) {
        Object ownDepartment = dealerSupplyObj.get(DealerSupplyObjConstants.Field.ownDepartment.getApiName());
        //根据供应关系获取 包含特例产品，下级配送商，供货门店
        //查询供货门店
        SearchQuery.SearchQueryBuilder queryBuilder = SearchQuery.builder()
                .eq(SupplyStoreObjConstants.Field.upSupplyId.getApiName(), dealerSupplyObj.getId())
                .eq(SupplyStoreObjConstants.Field.specialSupply.getApiName(), false);
        if (StringUtils.isNotBlank(subAccountId)){
            queryBuilder.eq(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), subAccountId);
        }
        SearchQuery storeSearchQuery = queryBuilder
                .build();
        List<IObjectData> supplyStoreData = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), storeSearchQuery, SupplyStoreObjConstants.API_NAME);
        //查询下级配送商
        SearchQuery.SearchQueryBuilder disQueryBuilder = SearchQuery.builder()
                .eq(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(),dealerSupplyObj.getId())
                .eq(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), false);
        if (StringUtils.isNotBlank(subAccountId)){
            disQueryBuilder.eq(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), subAccountId);
        }
        SearchQuery disSearchQuery = disQueryBuilder
                .build();
        List<IObjectData> distributorSupplyData = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), disSearchQuery, DistributorSupplyObjConstants.API_NAME);

        List<String> newAccountIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(supplyStoreData)) {
            supplyStoreData.forEach(o -> newAccountIds.add(o.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName()).toString()));
        }
        if (CollectionUtils.isNotEmpty(distributorSupplyData)) {
            distributorSupplyData.forEach(o -> newAccountIds.add(o.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName()).toString()));
        }
        //查询已存在客户
//        IObjectData objectData=syncAvailableRangeObj(tenantId,dealerSupplyObj);
        SearchQuery.SearchQueryBuilder accountQueryBuilder = SearchQuery.builder().eq(AvailableAccountObjConstants.Field.availableRangeId.getApiName(), masterObject.getId());
        if (StringUtils.isNotBlank(subAccountId)){
            accountQueryBuilder.eq(AvailableAccountObjConstants.Field.accountId.getApiName(), subAccountId);
        }
        SearchQuery accountSearchQuery = accountQueryBuilder.build();
        List<IObjectData> existAccountList = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), accountSearchQuery, AvailableAccountObjConstants.API_NAME);
        List<String> existAccountIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(existAccountList)) {
            existAccountList.forEach(o -> existAccountIds.add(o.get(AvailableAccountObjConstants.Field.accountId.getApiName()).toString()));
        }

        //区分出新增和移除的
        List<String> addList = Lists.newArrayList();
        addList.addAll(newAccountIds);
        addList.removeAll(existAccountIds);
        List<String> delList = Lists.newArrayList();
        delList.addAll(existAccountIds);
        delList.removeAll(newAccountIds);
        // 不知道为啥 出现的 客户id重复
        if (CollectionUtils.isNotEmpty(addList)) {
            supplyDao.createAvailableAccountList(tenantId, masterObject.getId(), addList.stream().distinct().collect(Collectors.toList()),ownDepartment);
        }
        if (CollectionUtils.isNotEmpty(delList)) {
            supplyDao.deleteAvailableAccountList(tenantId, masterObject.getId(), delList.stream().distinct().collect(Collectors.toList()));
        }
    }
    @Override
    public List<IObjectData> syncSpecialAvailableObj(String tenantId, IObjectData dealerSupplyObj) {
        // 查询特例供货
        String dealerId = dealerSupplyObj.get(DealerSupplyObjConstants.Field.dealerId.getApiName(), String.class);
        // 特例供货
        List<String> specialAccountIds = supplyDao.getSpecialAccountIds(tenantId,dealerId);
        List<IObjectData> avaRangeObjList = Lists.newArrayList();
        Collections.synchronizedList(avaRangeObjList);
        //查询已经有的特例供货关系产生的供货关系
        List<IObjectData> delAvailableList = supplyDao.getAvailableRangeExcludeList(tenantId, Lists.newArrayList(dealerSupplyObj.getId()),specialAccountIds);
        if (CollectionUtils.isNotEmpty(delAvailableList)){
            log.info("syncSpecialAvailableObj delAvailableList {}",delAvailableList.stream().map(o->o.getId()).collect(Collectors.toList()));
            supplyDao.batchInvalidAndDel(User.systemUser(tenantId),delAvailableList);
        }
        //查询所有的供货关系
        if (CollectionUtils.isNotEmpty(specialAccountIds)){
            specialAccountIds.parallelStream().forEach(specialAccountId->{
                IObjectData avaRangeObj = subSyncSpecialAvailableObj(tenantId, dealerSupplyObj, specialAccountId);
                avaRangeObjList.add(avaRangeObj);
            });

        }
        return avaRangeObjList;
    }

    @Deprecated
    public List<IObjectData> syncSpecialAvailableObjOld(String tenantId, IObjectData dealerSupplyObj) {
        // 查询特例供货
        String dealerId = dealerSupplyObj.get(DealerSupplyObjConstants.Field.dealerId.getApiName(), String.class);
        Object ownDepartment = dealerSupplyObj.get(DealerSupplyObjConstants.Field.ownDepartment.getApiName());
        log.info("syncSpecialAvailableObj start");
        // 特例供货
        List<String> specialAccountIds = supplyDao.getSpecialAccountIds(tenantId,dealerId);
        // 特例 分销商

        // 特例 门店
        List<ShopProductSupplierRelationshipObj> specialSupplyByShopId = supplyDao.getSpecialSupplyByDealerId(tenantId, dealerId,specialAccountIds);
        log.info("getSpecialSupplyByDealerId end");
        //可能需要清空的
        if (CollectionUtils.isEmpty(specialSupplyByShopId)) {
            specialSupplyByShopId = Lists.newArrayList();
        }
        // 根据特例供货查询 可售范围
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(AvailableRangeObjConstants.Field.dealerSupplyId.getApiName(), dealerSupplyObj.getId())
                .in(AvailableRangeObjConstants.Field.storeId.getApiName(), specialAccountIds).build();
        List<IObjectData> data = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, AvailableRangeObjConstants.API_NAME,Lists.newArrayList(AvailableRangeObjConstants.Field.storeId.getApiName(),BaseField.id.getApiName()));
        log.info("parallelGetDatasByQueryWithFields AvailableRangeObj end");
        // key 是下级
        Map<String, IObjectData> avaRangeMap = data.stream().collect(Collectors.toMap(
                k -> k.get(AvailableRangeObjConstants.Field.storeId.getApiName(), String.class), v -> v));
        List<IObjectData> delLists = new ArrayList<>();
        List<IObjectData> addLists = new ArrayList<>();
        // 已经存在的可售产品 map
        List<String> avaRangeIdList = avaRangeMap.values().stream().map(o -> o.getId()).collect(Collectors.toList());
        Map<String, List<IObjectData>> avaAccountMap = new HashMap<>();
        Map<String, List<IObjectData>> avaProductMap = new HashMap<>();
        MapUtils.synchronizedMap(avaAccountMap);
        MapUtils.synchronizedMap(avaProductMap);
        if (CollectionUtils.isNotEmpty(avaRangeIdList)) {
            List<List<String>> partition = Lists.partition(avaRangeIdList, 200);
            partition.parallelStream().forEach(subAvaRangeIdList -> {

                SearchQuery productQuery = SearchQuery.builder()
                        .in(AvailableProductObjConstants.Field.availableRangeId.getApiName(), subAvaRangeIdList)
                        .build();
                List<IObjectData> avaProductList = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), productQuery
                        , AvailableProductObjConstants.API_NAME, Lists.newArrayList(AvailableProductObjConstants.Field.availableRangeId.getApiName()
                                , AvailableProductObjConstants.Field.productId.getApiName(), BaseField.id.getApiName()));
                log.info("parallelGetDatasByQueryWithFields AvailableProductObj end");
                Map<String, List<IObjectData>> subProductMap = avaProductList.stream()
                        .collect(Collectors.groupingBy(o -> o.get(AvailableProductObjConstants.Field.availableRangeId.getApiName(), String.class)));
                MapUtils.synchronizedMap(subProductMap);
                avaProductMap.putAll(subProductMap);
                List<IObjectData> avaAccountList = Lists.newArrayList();
                for (String id : subAvaRangeIdList) {
                    // 已经存在的可售客户 map
                    SearchQuery accountQuery = SearchQuery.builder()
                            .eq(AvailableProductObjConstants.Field.availableRangeId.getApiName(), id)
                            .build();
                    avaAccountList.addAll(supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), accountQuery
                            , AvailableAccountObjConstants.API_NAME, Lists.newArrayList(AvailableAccountObjConstants.Field.availableRangeId.getApiName()
                                    , AvailableAccountObjConstants.Field.accountId.getApiName(), BaseField.id.getApiName())));
                }
                log.info("parallelGetDatasByQueryWithFields AvailableAccountObj end");
                Map<String, List<IObjectData>> subAccountMap = avaAccountList.stream()
                        .collect(Collectors.groupingBy(o -> o.get(AvailableProductObjConstants.Field.availableRangeId.getApiName(), String.class)));
                MapUtils.synchronizedMap(subAccountMap);
                avaAccountMap.putAll(subAccountMap);
            });
            log.info("parallel query end avaproduct size {},avaAccount size {}", avaProductMap.size(), avaAccountMap.size());
        }

        //根据 avaRangeMap 取 可售客户 可售 产品
        //特例数据 按照 accountId 分组
        Map<String, List<ShopProductSupplierRelationshipObj>> shopIdAndSpecialsMap = specialSupplyByShopId.stream()
                .collect(Collectors.groupingBy(o -> o.getShopId()));
        for (Map.Entry<String, List<ShopProductSupplierRelationshipObj>> stringListEntry : shopIdAndSpecialsMap.entrySet()) {
            String shopId = stringListEntry.getKey();
            List<ShopProductSupplierRelationshipObj> specialList = stringListEntry.getValue();
            //构建可售范围
            IObjectData avaRangeObj = avaRangeMap.get(shopId);
            if (avaRangeObj == null) {
                avaRangeObj = supplyDao.convertAvailableRangeObj4Special(tenantId, "-10000", dealerSupplyObj.getId(), dealerId, shopId,ownDepartment);
                addLists.add(avaRangeObj);
                avaRangeMap.put(shopId, avaRangeObj);
            }
            // 处理可售客户
            List<IObjectData> existAccountList = avaAccountMap.get(avaRangeObj.getId());
            List<String> tempSpecialAList = Lists.newArrayList(shopId);
            if (CollectionUtils.isNotEmpty(existAccountList)) {
                //删除
                if (existAccountList.removeIf(o -> tempSpecialAList.contains(o.get(AvailableAccountObjConstants.Field.accountId.getApiName(), String.class)))) {
                    //已经存在的数据

                } else {
                    List<IObjectData> accountList = supplyDao.convertAvailableAccountList(tenantId, avaRangeObj.getId(),
                            tempSpecialAList,ownDepartment);
                    addLists.addAll(accountList);
                }
                delLists.addAll(existAccountList);
            } else {
                List<IObjectData> accountList = supplyDao.convertAvailableAccountList(tenantId, avaRangeObj.getId(),
                        tempSpecialAList, ownDepartment);
                addLists.addAll(accountList);
            }

            // 处理可售产品
            List<String> tempSpecialPList = specialList.stream().map(o -> o.getProductId()).collect(Collectors.toList());
            List<IObjectData> existProductList = avaProductMap.get(avaRangeObj.getId());
            if (CollectionUtils.isNotEmpty(existProductList)) {
                delLists.addAll(existProductList.stream().filter(o -> !tempSpecialPList.contains(o.get(AvailableProductObjConstants.Field.productId.getApiName()
                        , String.class))).collect(Collectors.toList()));
                List<String> existProductIds = existProductList.stream().map(o -> o.get(AvailableProductObjConstants.Field.productId.getApiName()
                        , String.class)).collect(Collectors.toList());
                tempSpecialPList.removeIf(o -> existProductIds.contains(o));
                if (CollectionUtils.isNotEmpty(tempSpecialPList)) {
                    List<IObjectData> productList = supplyDao.convertAvailableProductList(tenantId, avaRangeObj.getId(),
                            tempSpecialPList,ownDepartment);
                    addLists.addAll(productList);
                }
            } else {
                List<IObjectData> productList = supplyDao.convertAvailableProductList(tenantId, avaRangeObj.getId(),
                        tempSpecialPList, ownDepartment);
                addLists.addAll(productList);
            }
        }
        if (CollectionUtils.isNotEmpty(addLists)) {
            log.info("batchSave size {} start",addLists.size());
            baseDao.batchSave(User.systemUser(tenantId), addLists);
            log.info("batchSave size {} end",addLists.size());

        }
        if (CollectionUtils.isNotEmpty(delLists)) {
            log.info("batchDel size {} start",delLists.size());
            baseDao.batchInvalidAndDel(User.systemUser(tenantId), delLists);
            log.info("batchDel size {} start",delLists.size());
        }
        Collection<IObjectData> values = avaRangeMap.values();
        return Lists.newArrayList(values);
    }
    public IObjectData subSyncSpecialAvailableObj(String tenantId, IObjectData dealerSupplyObj,String specialAccountId) {
        // 查询特例供货
        String dealerId = dealerSupplyObj.get(DealerSupplyObjConstants.Field.dealerId.getApiName(), String.class);
        Object ownDepartment = dealerSupplyObj.get(DealerSupplyObjConstants.Field.ownDepartment.getApiName());
        // 特例供货
        // 特例 门店
        List<ShopProductSupplierRelationshipObj> specialSupplyByShopId = supplyDao.getSpecialSupplyByDealerId(tenantId, dealerId, specialAccountId);
        //可能需要清空的
        if (CollectionUtils.isEmpty(specialSupplyByShopId)) {
            specialSupplyByShopId = Lists.newArrayList();
        }
        // 根据特例供货查询 可售范围
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(AvailableRangeObjConstants.Field.dealerSupplyId.getApiName(), dealerSupplyObj.getId())
                .eq(AvailableRangeObjConstants.Field.storeId.getApiName(), specialAccountId).build();
        List<IObjectData> data = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, AvailableRangeObjConstants.API_NAME, Lists.newArrayList(AvailableRangeObjConstants.Field.storeId.getApiName(), BaseField.id.getApiName()));
        log.info("parallelGetDatasByQueryWithFields AvailableRangeObj end");
        List<IObjectData> delLists = new ArrayList<>();
        List<IObjectData> addLists = new ArrayList<>();
        List<IObjectData> existProduct = null;
        List<IObjectData> existAccounts = null;
        IObjectData avaRangeObj = null;
        if (CollectionUtils.isNotEmpty(data)) {
            //查到取0
            avaRangeObj = data.get(0);
            SearchQuery productQuery = SearchQuery.builder()
                    .eq(AvailableProductObjConstants.Field.availableRangeId.getApiName(), avaRangeObj.getId())
                    .build();
            existProduct = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), productQuery
                    , AvailableProductObjConstants.API_NAME, Lists.newArrayList(AvailableProductObjConstants.Field.availableRangeId.getApiName()
                            , AvailableProductObjConstants.Field.productId.getApiName(), BaseField.id.getApiName()));
            SearchQuery accountQuery = SearchQuery.builder()
                    .eq(AvailableProductObjConstants.Field.availableRangeId.getApiName(), avaRangeObj.getId())
                    .build();
            existAccounts = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), accountQuery
                    , AvailableAccountObjConstants.API_NAME, Lists.newArrayList(AvailableAccountObjConstants.Field.availableRangeId.getApiName()
                            , AvailableAccountObjConstants.Field.accountId.getApiName(), BaseField.id.getApiName()));
        } else {
            //没查到
            avaRangeObj = supplyDao.convertAvailableRangeObj4Special(tenantId, "-10000", dealerSupplyObj.getId(), dealerId, specialAccountId, ownDepartment);
            addLists.add(avaRangeObj);
        }

        //增量更新数据
        if (CollectionUtils.isNotEmpty(existAccounts)) {
            //删除
            if (existAccounts.removeIf(o -> specialAccountId.equals((o.get(AvailableAccountObjConstants.Field.accountId.getApiName(), String.class))))) {
                //已经存在的数据

            } else {
                List<IObjectData> accountList = supplyDao.convertAvailableAccountList(tenantId, avaRangeObj.getId(),
                        Lists.newArrayList(specialAccountId), ownDepartment);
                addLists.addAll(accountList);
            }
            if (CollectionUtils.isNotEmpty(existAccounts)){
                delLists.addAll(existAccounts);
            }
        } else {
            List<IObjectData> accountList = supplyDao.convertAvailableAccountList(tenantId, avaRangeObj.getId(),
                    Lists.newArrayList(specialAccountId), ownDepartment);
            addLists.addAll(accountList);
        }
        List<String> thisSpecialProductList = specialSupplyByShopId.stream().map(o->o.getProductId()).distinct().collect(Collectors.toList());
        // 处理可售产品
        if (CollectionUtils.isNotEmpty(existProduct)) {
            delLists.addAll(existProduct.stream().filter(o -> !thisSpecialProductList.contains(o.get(AvailableProductObjConstants.Field.productId.getApiName()
                    , String.class))).collect(Collectors.toList()));
            List<String> existProductIds = existProduct.stream().map(o -> o.get(AvailableProductObjConstants.Field.productId.getApiName()
                    , String.class)).collect(Collectors.toList());
            thisSpecialProductList.removeIf(o -> existProductIds.contains(o));
            if (CollectionUtils.isNotEmpty(thisSpecialProductList)) {
                List<IObjectData> productList = supplyDao.convertAvailableProductList(tenantId, avaRangeObj.getId(),
                        thisSpecialProductList, ownDepartment);
                addLists.addAll(productList);
            }
        } else {
            List<IObjectData> productList = supplyDao.convertAvailableProductList(tenantId, avaRangeObj.getId(),
                    thisSpecialProductList, ownDepartment);
            addLists.addAll(productList);
        }
        //处理价目表

        if (CollectionUtils.isNotEmpty(addLists)) {
            supplyDao.batchSave(User.systemUser(tenantId), addLists);
        }
        if (CollectionUtils.isNotEmpty(delLists)) {
            supplyDao.batchInvalidAndDel(User.systemUser(tenantId), delLists);
        }
        //同步价目表
        syncAvailablePriceBookObj(tenantId,dealerSupplyObj,Lists.newArrayList(avaRangeObj));
        return avaRangeObj;
    }
    @Override
    public void syncAvailablePriceBookObj(String tenantId, IObjectData dealerSupplyObj, List<IObjectData> masterObjectList) {
        if (FmcgGray.Supply.EI.closedSyncAvailablePriceBookObj.fsGray(tenantId)){
            return;
        }

        try{
            extracted(tenantId, dealerSupplyObj, masterObjectList);
        }catch (Exception e){
            log.info("syncAvailablePriceBookObj error {}",tenantId);
        }

    }

    private void extracted(String tenantId, IObjectData dealerSupplyObj, List<IObjectData> masterObjectList) {
        List<String> ids = Lists.newArrayList();
        masterObjectList.forEach(o -> ids.add(o.getId()));
        SearchQuery searchQuery = SearchQuery.builder()
                .in(AvailablePriceBookConstants.Field.availableRangeId.getApiName(), ids)
                .build();
        Object ownDepartment = dealerSupplyObj.get(DealerSupplyObjConstants.Field.ownDepartment.getApiName());
        List<IObjectData> data = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, AvailablePriceBookConstants.API_NAME);
        if (CollectionUtils.isNotEmpty(data)) {
            List<String> addMasterIds = Lists.newArrayList();
            Set<String> existIds = data.stream().map(o -> o.get(AvailablePriceBookConstants.Field.availableRangeId.getApiName(), String.class)).collect(Collectors.toSet());
            for (IObjectData addData : masterObjectList) {
                if (!existIds.contains(addData.getId())) {
                    addMasterIds.add(addData.getId());
                }
            }
            supplyDao.createPriceBookObj(tenantId, "-10000", addMasterIds, ownDepartment);
        } else {
            //创建标准价目表
            supplyDao.createPriceBookObj(tenantId, "-10000", ids, ownDepartment);
        }
    }


    @Override
    public void updateAllAvailableDataByShop(String tenantId, Shop shop) {
        List<IObjectData> availableObjList = Lists.newArrayList();
        Collections.synchronizedList(availableObjList);
        shop.getSupplierList().parallelStream().forEach(o->{
            List<IObjectData> dealerSupplyBySuppliers = supplyDao.getDealerSupplyBySuppliers(tenantId, Lists.newArrayList(o.getId()));
            for (IObjectData iObjectData : dealerSupplyBySuppliers) {
                List<IObjectData> c = syncAvailableObjByDealerSupplyObj(tenantId, iObjectData, shop.getId());
                Collections.synchronizedList(c);
                availableObjList.addAll(c);
            }
        });

        //删除 不在范围内的所有的可售范围
        //查询可售客户
        SearchQuery searchQuery=SearchQuery.builder()
                .nin(AvailableAccountObjConstants.Field.availableRangeId.getApiName(),availableObjList.stream().map(o->o.getId()).collect(Collectors.toList()))
                .eq(AvailableAccountObjConstants.Field.accountId.getApiName(),shop.getId()).build();
        List<IObjectData> delData = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, AvailableAccountObjConstants.API_NAME);
        if (CollectionUtils.isNotEmpty(delData)){
            supplyDao.batchInvalidAndDel(User.systemUser(tenantId),delData);
        }
        //刪除特例的经营范围
        if (CollectionUtils.isNotEmpty(delData)){
            SearchQuery delAvaObjQuery=SearchQuery.builder()
                    .in("_id",delData.stream().map(o->o.get(AvailableAccountObjConstants.Field.availableRangeId.getApiName(),String.class)).collect(Collectors.toList()))
                    .exist(AvailableRangeObjConstants.Field.storeId.getApiName())
                    .build();
            List<IObjectData> tempAvaObjList = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), delAvaObjQuery, AvailableRangeObjConstants.API_NAME);
            if (CollectionUtils.isNotEmpty(tempAvaObjList)){
                supplyDao.batchInvalidAndDel(User.systemUser(tenantId),tempAvaObjList);
            }
        }
    }

    @Override
    public void fixPriceBookObj(String tenantId) {
        List<IObjectData> allPriceData = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder().build(),
                AvailablePriceBookConstants.API_NAME, Lists.newArrayList(AvailablePriceBookConstants.Field.availableRangeId.getApiName()
                        , AvailablePriceBookConstants.Field.priceBookId.getApiName(),BaseField.dataOwnDepartment.getApiName()));
        Map<String, Set<IObjectData>> priceBookIdMap = allPriceData.stream().collect(Collectors.groupingBy(o -> o.get(AvailablePriceBookConstants.Field.priceBookId.getApiName(), String.class)
                , Collectors.mapping(o -> o, Collectors.toSet())));
        Set<IObjectData> basePriceSet = priceBookIdMap.get("60949800e41d64000164f6f8");
        Set<IObjectData> needPriceSet = priceBookIdMap.get("61b84cac41f4ad0001149dfa");
        Set<String> needPriceAvaIdSet = needPriceSet.stream().map(o->o.get(AvailablePriceBookConstants.Field.availableRangeId.getApiName(),String.class)).collect(Collectors.toSet());
        List<IObjectData> addList  = Lists.newArrayList();
        for (IObjectData iObjectData : basePriceSet) {
            String avaId = iObjectData.get(AvailablePriceBookConstants.Field.availableRangeId.getApiName(),String.class);
            if (!needPriceAvaIdSet.contains(avaId)){
                IObjectData addIObjectData =supplyDao.createBaseObjectData(tenantId,"-10000", AvailablePriceBookConstants.API_NAME);
                addIObjectData.set(AvailablePriceBookConstants.Field.priceBookId.getApiName(), "61b84cac41f4ad0001149dfa");
                addIObjectData.set("data_own_department",iObjectData.getDataOwnDepartment());
                addIObjectData.set(AvailablePriceBookConstants.Field.availableRangeId.getApiName(),avaId);
                addList.add(addIObjectData);
            }
        }
        if (CollectionUtils.isNotEmpty(addList)){
            supplyDao.batchSave(User.systemUser(tenantId),addList);
        }
    }
}
