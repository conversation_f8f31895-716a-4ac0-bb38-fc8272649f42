package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSONArray;
import com.esotericsoftware.minlog.Log;
import com.facishare.crm.fmcg.wq.api.area.AreaManageStoreById;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.model.PlanRouteInfo;
import com.facishare.crm.fmcg.wq.service.AreaService;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 片区规划路线查询门店信息
 */

public class AreaManageStoreByIdController extends PreDefineController<AreaManageStoreById.Arg, AreaManageStoreById.Result> {

    AreaService areaService = SpringUtil.getContext().getBean(AreaService.class);


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected AreaManageStoreById.Result doService(AreaManageStoreById.Arg arg) {
        AreaManageStoreById.Result result = new AreaManageStoreById.Result();
        if(arg.getAreaIds().size()>5){
            throw new ValidateException("最多支持查询5个片区的数据，请缩减范围。"); //ignoreI18n
        }
        IObjectDescribe accDesc = serviceFacade.findObject(controllerContext.getTenantId(), CommonConstants.ACCOUNT_OBJ);

        ILayout layout = serviceFacade.findListLayoutByApiName(controllerContext.getUser(),"layout_AccountObj_mobile",CommonConstants.ACCOUNT_OBJ);
        Map<String, IFieldDescribe> fieldDescribeMap = accDesc.getFieldDescribeMap();
        List<String> listFields = ObjectUtils.getShowFiledApiNameFromListLayout(layout);
        listFields.removeAll(ObjectUtils.fixFields);
        List<String> finalFields = Lists.newArrayList();
        finalFields.addAll(ObjectUtils.fixFields);
        if(CollectionUtils.isNotEmpty(listFields)){
            finalFields.addAll(listFields);
        }

        String showFieldNameOnWater = areaService.getFieldNameOnWaterDrop(controllerContext.getTenantId()); // 水滴上的字段
        Boolean isShowWaterOnList = false; // 水滴上的是否显示在客户移动端列表中
        if(StringUtils.isNotBlank(showFieldNameOnWater)){
            if(finalFields.contains(showFieldNameOnWater)) {
                isShowWaterOnList = true;
            }else{
                finalFields.add(showFieldNameOnWater);
            }
        }

        SearchTemplateQuery query = getSearchQuery(arg);
        query.setLimit(2000);
        List<IObjectData> data = ObjectUtils.queryDataWithFieldInfo(serviceFacade,controllerContext.getUser(),controllerContext.getRequestContext(),accDesc,query,finalFields);
        if(CollectionUtils.isEmpty(data)){
            return result;
        }else{
            try {
                result.setTotal(data.size());
                List<PlanRouteInfo> planData = areaService.convertPlanRouteInfo(data, fieldDescribeMap, finalFields);
                Map<String, List<PlanRouteInfo>> group = planData.stream().collect(Collectors.groupingBy(o -> o.getAreaId()));
                Map<String, AreaManageStoreById.AreaStoreInfo> info = Maps.newHashMap();
                for (Map.Entry<String, List<PlanRouteInfo>> entry : group.entrySet()) {
                    AreaManageStoreById.AreaStoreInfo areaStoreInfo = new AreaManageStoreById.AreaStoreInfo();
                    areaStoreInfo.setName(entry.getValue().get(0).getAreaName());
                    areaStoreInfo.setStores(entry.getValue());
                    info.put(entry.getKey(), areaStoreInfo);
                }
                result.setInfos(info);
                Map<String,String> accountValueAndLevelMap = ObjectUtils.getValueAndColorByDesc(fieldDescribeMap.get(ObjectUtils.CUSTOMER_LEVEL));
                result.setAccountValueAndLevelMap(accountValueAndLevelMap);
                result.setShowWaterOnList(isShowWaterOnList);
                result.setShowFieldNameOnWater(showFieldNameOnWater);
            }catch (Exception e){
                Log.error("",e);
            }

        }
        return result;

    }

    private SearchTemplateQuery getSearchQuery(AreaManageStoreById.Arg arg) {
        Filter storeFilter = new Filter();
        storeFilter.setFieldName(AccountObjConstants.Field.belongArea.getApiName());
        storeFilter.setOperator(Operator.IN);
        storeFilter.setFieldValues(arg.getAreaIds());
        if(StringUtils.isNotEmpty(arg.getQueryInfo())){
            List<Wheres> wheres = JSONArray.parseArray(arg.getQueryInfo(),Wheres.class);
            SearchQuery.SearchQueryBuilder builder = SearchQuery.builder();
            for (Wheres wheres1 :wheres) {
                builder.addWheres(wheres1);
            }
            return builder.addFilterInWhere(storeFilter).build().getSearchTemplateQuery();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(Lists.newArrayList(storeFilter));
        return query;
    }
}
