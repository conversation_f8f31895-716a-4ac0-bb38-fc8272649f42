package com.facishare.crm.fmcg.wq.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.model.PlanRouteInfo;
import com.facishare.crm.fmcg.wq.service.AreaService;
import com.facishare.crm.fmcg.wq.util.FieldUtils;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class AreaServiceImpl implements AreaService {

    @Autowired
    ServiceFacade serviceFacade;

    public static final List<String> finalShowField = Lists.newArrayList("name","location","owner","customer_label","account_level","route_ids","last_visit_closed_time","visit_frequency");

    private static final Logger logger = LoggerFactory.getLogger(AreaServiceImpl.class);

    @Override
    public List<PlanRouteInfo> convertPlanRouteInfo(List<IObjectData> data, Map<String, IFieldDescribe> fieldDescribeMap, List<String> showFiledApiNameFromListLayout) {
        if(CollectionUtils.isEmpty(data)){
            return Lists.newArrayList();
        }
        List<String> finalFields = Lists.newArrayList(finalShowField);
        showFiledApiNameFromListLayout.removeAll(ObjectUtils.fixFields);
        if(CollectionUtils.isNotEmpty(showFiledApiNameFromListLayout)){
            finalFields.addAll(showFiledApiNameFromListLayout);
        }

        List<PlanRouteInfo> res = Lists.newArrayList();
        for (IObjectData datum : data) {
            PlanRouteInfo routeInfo = new PlanRouteInfo();
            res.add(routeInfo);
            routeInfo.setAreaId(datum.get("belong_area",String.class,null));
            routeInfo.setAreaName(FieldUtils.getShowStr(datum,fieldDescribeMap.get("belong_area")));
            routeInfo.set_id(datum.getId());
            routeInfo.setRouteId(FieldUtils.getShowStr(datum,fieldDescribeMap.get("route_ids")));
            if(CollectionUtils.isNotEmpty(finalFields)){
                List<PlanRouteInfo.DataInfo> showField = Lists.newArrayList();
                routeInfo.setShowFiledList(showField);
                for (String finalField : finalFields) {
                    if(Objects.nonNull(fieldDescribeMap.get(finalField))) {
                        PlanRouteInfo.DataInfo dataInfo = new PlanRouteInfo.DataInfo();
                        dataInfo.setApiName(finalField);
                        dataInfo.setLabel(fieldDescribeMap.get(finalField).getLabel());
                        dataInfo.setValue(FieldUtils.getShowStr(datum, fieldDescribeMap.get(finalField)));
                        showField.add(dataInfo);
                    }
                }
            }
        }
        return res;
    }

    @Override
    public List<String> buildShowFieldApiName(String tenantId) {
        ILayout layout = serviceFacade.findListLayoutByApiName(User.systemUser(tenantId),CommonConstants.Account_Mobile_Layout, CommonConstants.ACCOUNT_OBJ);
        List<String> listFields = ObjectUtils.getShowFiledApiNameFromListLayout(layout);
        listFields.removeAll(ObjectUtils.fixFields);
        List<String> finalFields = Lists.newArrayList();
        finalFields.addAll(ObjectUtils.fixFields);
        if(CollectionUtils.isNotEmpty(listFields)){
            finalFields.addAll(listFields);
        }
        return finalFields;
    }

    @Override
    public String getFieldNameOnWaterDrop(String tenantId) {
        ILayout listLayout = serviceFacade.findListLayoutByApiName(User.systemUser(tenantId),CommonConstants.Account_List_Layout,CommonConstants.ACCOUNT_OBJ);
        try {
            if(Objects.isNull(listLayout) || CollectionUtils.isEmpty(listLayout.getComponents())){
                return null;
            }
            JSONArray viewInfo = JSONObject.parseArray(JSONObject.toJSONString(listLayout.getComponents().get(0).get("view_info")));
            if(Objects.isNull(viewInfo)){
                return null;
            }
            for (int i = 0; i < viewInfo.size(); i++) {
                JSONObject jsonObject = viewInfo.getJSONObject(i);
                if("map_view".equals(jsonObject.getString("name"))){
                    JSONObject bubbleInfo = jsonObject.getJSONObject("bubble_info");
                    if(Objects.nonNull(bubbleInfo)){
                        return bubbleInfo.getString("field");
                    }
                }
            }
        } catch (Exception e) {
            logger.error("getFieldNameOnWaterDrop is error ei:{}",tenantId,e);
        }
        return null;
    }

}
