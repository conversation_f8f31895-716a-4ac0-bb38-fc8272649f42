<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <modules>
        <module>fs-crm-fmcg-wq-web</module>
        <module>fs-crm-fmcg-wq-checkins</module>
        <module>fs-crm-fmcg-wq-common</module>
        <module>fs-crm-fmcg-wq-office</module>
        <module>fs-crm-fmcg-wq-supply</module>
        <module>fs-crm-fmcg-wq-all</module>
        <module>fs-crm-fmcg-wq-report</module>
        <module>fs-crm-fmcg-wq-salary</module>
    </modules>
    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <groupId>com.facishare</groupId>
    <artifactId>fs-crm-fmcg-wq</artifactId>
    <version>9.5.5-SNAPSHOT</version>

    <name>fs-crm-fmcg-wq</name>

    <properties>
        <java.version>1.8</java.version>
        <jdk.version>1.8</jdk.version>
        <war.name>fs</war.name>
        <appframework.big.version>9.5.5-SNAPSHOT</appframework.big.version>
        <appframework.version>9.5.5-SNAPSHOT</appframework.version>
        <fs-fcp.version>1.2.0-SNAPSHOT</fs-fcp.version>
        <!--        <i18n-client.version>3.3.0-SNAPSHOT</i18n-client.version>-->
        <i18n-util.version>1.4-SNAPSHOT</i18n-util.version>
        <!--        <rocketmq-support.version>2.0.0-SNAPSHOT</rocketmq-support.version>-->
        <!--        <apache.rocketmq.version>4.5.2</apache.rocketmq.version>-->
        <okio.version>1.17.2</okio.version>
        <checkins.version>9.2.0-SNAPSHOT</checkins.version>
        <!--        <enterpriserelation.rest.version>2.0.4-SNAPSHOT</enterpriserelation.rest.version>-->
        <checkins-office.version>9.2.5-SNAPSHOT</checkins-office.version>
        <fmcg-http.version>2.2.0-SNAPSHOT</fmcg-http.version>
        <appserver-common-tools.version>1.2-SNAPSHOT</appserver-common-tools.version>
        <mybatis-spring-support.version>4.5.0-SNAPSHOT</mybatis-spring-support.version>
        <!--        <fs-pod-client.version>9.0.1-SNAPSHOT</fs-pod-client.version>-->
        <webApp.contextPath />
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>core-filter</artifactId>
        </dependency>
        <!--日志级别动态调整-->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>logconfig-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.objenesis</groupId>
            <artifactId>objenesis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <!-- JUnit 5 依赖 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-launcher</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>3.12.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>3.12.4</version>
            <scope>test</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.spockframework/spock-core -->
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.spockframework/spock-spring -->
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.codehaus.groovy/groovy-all -->
        <dependency> <!-- use a specific Groovy version rather than the one specified by spock-core -->
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
        </dependency>
        <!-- jmockit 单元测试使用 -->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>powermock-api-support</artifactId>
                    <groupId>org.powermock</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>powermock-core</artifactId>
                    <groupId>org.powermock</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-support</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>powermock-core</artifactId>
                    <groupId>org.powermock</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>powermock-reflect</artifactId>
                    <groupId>org.powermock</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.facishare.appserver</groupId>
                <artifactId>fs-appserver-common-tools</artifactId>
                <version>${appserver-common-tools.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-core</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-api</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-common</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-flow</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-log</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-license</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-web</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata-restdriver</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-privilege</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-privilege-temp</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-fcp</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-config</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata-util</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-coordination</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <!--租户级配置-->
            <!--            <dependency>-->
            <!--                <groupId>com.facishare</groupId>-->
            <!--                <artifactId>fs-paas-bizconf-api</artifactId>-->
            <!--                <version>2.0.0-SNAPSHOT</version>-->
            <!--            </dependency>-->

            <!--            <dependency>-->
            <!--                <groupId>com.facishare.open</groupId>-->
            <!--                <artifactId>rocketmq-spring-support</artifactId>-->
            <!--&lt;!&ndash;
            <version>1.0.1-SNAPSHOT</version>&ndash;&gt;-->
            <!--            </dependency>-->

            <!--            <dependency>-->
            <!--                <groupId>com.facishare.open</groupId>-->
            <!--                <artifactId>fs-open-app-center-common-utils</artifactId>-->
            <!--                <version>1.0.0-SNAPSHOT</version>-->
            <!--            </dependency>-->

            <!--            <dependency>-->
            <!--                <groupId>com.facishare.open</groupId>-->
            <!--                <artifactId>fs-wechat-union-core-api</artifactId>-->
            <!--                <version>0.0.3-SNAPSHOT</version>-->
            <!--            </dependency>-->

            <!--            <dependency>-->
            <!--                <groupId>com.facishare.open</groupId>-->
            <!--                <artifactId>fs-wechat-proxy-core-api</artifactId>-->
            <!--                <version>0.0.8-SNAPSHOT</version>-->
            <!--            </dependency>-->

            <!--            <dependency>-->
            <!--                <groupId>com.facishare</groupId>-->
            <!--                <artifactId>fs-uc-api</artifactId>-->
            <!--                <version>1.0.3-SNAPSHOT</version>-->
            <!--            </dependency>-->

            <!--            <dependency>-->
            <!--                <groupId>com.lowagie</groupId>-->
            <!--                <artifactId>itext</artifactId>-->
            <!--                <version>2.1.7</version>-->
            <!--            </dependency>-->

            <!--            <dependency>-->
            <!--                <groupId>com.facishare</groupId>-->
            <!--                <artifactId>fs-sandbox-api</artifactId>-->
            <!--                <version>1.0.1-SNAPSHOT</version>-->
            <!--            </dependency>-->

            <!--            <dependency>-->
            <!--                <groupId>com.fxiaoke</groupId>-->
            <!--                <artifactId>fs-paas-gnomon-api</artifactId>-->
            <!--                <version>1.0.0-SNAPSHOT</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>i18n-util</artifactId>
                <version>${i18n-util.version}</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>com.fxiaoke</groupId>-->
            <!--                <artifactId>fs-hosts-record</artifactId>-->
            <!--                <version>1.0.0-SNAPSHOT</version>-->
            <!--            </dependency>-->

            <dependency>
                <groupId>com.facishare.appserver</groupId>
                <artifactId>checkins-model-db</artifactId>
                <version>${checkins.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.appserver</groupId>
                <artifactId>checkins-v2-api</artifactId>
                <version>${checkins.version}</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>com.fxiaoke</groupId>-->
            <!--                <artifactId>fs-enterpriserelation-rest-api2</artifactId>-->
            <!--                <version>${enterpriserelation.rest.version}</version>-->
            <!--            </dependency>-->

            <dependency>
                <groupId>com.facishare.appserver</groupId>
                <artifactId>checkins-office-v2-api</artifactId>
                <version>${checkins-office.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.fmcg</groupId>
                <artifactId>fs-fmcg-framework-metadata</artifactId>
                <version>${fmcg-http.version}</version>
            </dependency>

            <!--            <dependency>-->
            <!--                <groupId>com.facishare.open</groupId>-->
            <!--                <artifactId>rocketmq-spring-support</artifactId>-->
            <!--                <version>1.0.1-SNAPSHOT</version>-->
            <!--            </dependency>-->
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!-- JaCoCo插件配置 -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <rule>
                                    <element>BUNDLE</element>
                                    <limits>
                                        <limit>
                                            <counter>INSTRUCTION</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.80</minimum>
                                        </limit>
                                    </limits>
                                </rule>
                            </rules>
                            <!-- 暂时设置为false，等测试覆盖率达到要求后再设置为true -->
                            <haltOnFailure>false</haltOnFailure>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 配置Maven Surefire插件以支持JUnit 5 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                    <useSystemClassLoader>false</useSystemClassLoader>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.surefire</groupId>
                        <artifactId>surefire-junit-platform</artifactId>
                        <version>2.22.2</version>
                    </dependency>
                </dependencies>
            </plugin>

            <!-- 配置Maven Clean插件，使用与当前Maven版本兼容的版本 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>3.1.0</version>
            </plugin>

            <!-- 配置Maven Compiler插件，使用与当前Maven版本兼容的版本 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${jdk.version}</target>
                </configuration>
            </plugin>

            <!-- 配置Maven Resources插件，使用与当前Maven版本兼容的版本 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
            </plugin>
        </plugins>
    </build>
</project>